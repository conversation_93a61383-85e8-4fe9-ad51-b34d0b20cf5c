version: '3.8'

services:
  app:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-this}
      - TELEGRAM_API_ID=${TELEGRAM_API_ID}
      - TELEGRAM_API_HASH=${TELEGRAM_API_HASH}
      - CLIENT_URL=${CLIENT_URL:-http://localhost:3001}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./server/database.sqlite:/app/server/database.sqlite
      - ./server/uploads:/app/server/uploads
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - telegram-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - telegram-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - telegram-network

volumes:
  redis_data:

networks:
  telegram-network:
    driver: bridge
