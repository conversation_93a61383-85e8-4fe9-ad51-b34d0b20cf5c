const { Telegram<PERSON><PERSON>, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');
const { AutomationTask, TelegramAccount, GroupManagement, ScrapedMember } = require('../models');
const cron = require('node-cron');

class AutomationService {
  constructor() {
    this.runningTasks = new Map();
    this.scheduledJobs = new Map();
    this.init();
  }

  init() {
    // Start task processor
    this.startTaskProcessor();
    
    // Schedule recurring tasks
    this.scheduleRecurringTasks();
    
    console.log('Automation Service initialized');
  }

  startTaskProcessor() {
    // Process pending tasks every 30 seconds
    setInterval(async () => {
      await this.processPendingTasks();
    }, 30000);
  }

  async processPendingTasks() {
    try {
      const pendingTasks = await AutomationTask.findAll({
        where: {
          status: 'pending',
          executeAt: {
            [require('sequelize').Op.lte]: new Date()
          }
        },
        include: ['telegramAccount'],
        order: [['priority', 'DESC'], ['createdAt', 'ASC']],
        limit: 10
      });

      for (const task of pendingTasks) {
        if (!this.runningTasks.has(task.id)) {
          this.executeTask(task);
        }
      }
    } catch (error) {
      console.error('Error processing pending tasks:', error);
    }
  }

  async executeTask(task) {
    this.runningTasks.set(task.id, true);
    
    try {
      await task.markAsRunning();
      
      let result;
      switch (task.taskType) {
        case 'auto_join':
          result = await this.executeAutoJoin(task);
          break;
        case 'auto_leave':
          result = await this.executeAutoLeave(task);
          break;
        case 'auto_react':
          result = await this.executeAutoReact(task);
          break;
        case 'auto_forward':
          result = await this.executeAutoForward(task);
          break;
        case 'scheduled_message':
          result = await this.executeScheduledMessage(task);
          break;
        case 'member_scraping':
          result = await this.executeMemberScraping(task);
          break;
        case 'member_adding':
          result = await this.executeMemberAdding(task);
          break;
        default:
          throw new Error(`Unknown task type: ${task.taskType}`);
      }
      
      await task.markAsCompleted(result);
      
      // Schedule next run if recurring
      if (task.isRecurring) {
        await task.scheduleNextRun();
      }
      
    } catch (error) {
      console.error(`Task ${task.id} failed:`, error);
      await task.markAsFailed(error);
      
      // Retry if possible
      if (task.canRetry()) {
        task.status = 'pending';
        task.executeAt = new Date(Date.now() + (task.retryCount * 60000)); // Exponential backoff
        await task.save();
      }
    } finally {
      this.runningTasks.delete(task.id);
    }
  }

  async executeAutoJoin(task) {
    const { telegramAccount } = task;
    const { groupUsername, delay = 5 } = task.config;
    
    if (!telegramAccount.sessionString) {
      throw new Error('Account not verified');
    }

    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    const client = new TelegramApi(new StringSession(telegramAccount.sessionString), apiId, apiHash);

    try {
      await client.connect();
      
      // Join the group
      const result = await client.invoke(new Api.channels.JoinChannel({
        channel: groupUsername
      }));
      
      // Get group info
      const group = await client.getEntity(groupUsername);
      
      // Save group management record
      await GroupManagement.create({
        userId: task.userId,
        telegramAccountId: telegramAccount.id,
        groupId: group.id.toString(),
        groupTitle: group.title,
        groupUsername: groupUsername,
        groupType: group.megagroup ? 'supergroup' : 'group',
        joinDate: new Date(),
        status: 'active'
      });
      
      await client.disconnect();
      
      // Add delay
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay * 1000));
      }
      
      return { success: true, groupTitle: group.title };
      
    } catch (error) {
      await client.disconnect();
      throw error;
    }
  }

  async executeAutoLeave(task) {
    const { telegramAccount } = task;
    const { groupUsername } = task.config;
    
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    const client = new TelegramApi(new StringSession(telegramAccount.sessionString), apiId, apiHash);

    try {
      await client.connect();
      
      const group = await client.getEntity(groupUsername);
      
      await client.invoke(new Api.channels.LeaveChannel({
        channel: group
      }));
      
      // Update group management record
      await GroupManagement.update(
        { status: 'left' },
        { 
          where: { 
            telegramAccountId: telegramAccount.id,
            groupId: group.id.toString()
          }
        }
      );
      
      await client.disconnect();
      
      return { success: true, groupTitle: group.title };
      
    } catch (error) {
      await client.disconnect();
      throw error;
    }
  }

  async executeAutoReact(task) {
    const { telegramAccount } = task;
    const { groupUsername, messageId, emoji } = task.config;
    
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    const client = new TelegramApi(new StringSession(telegramAccount.sessionString), apiId, apiHash);

    try {
      await client.connect();
      
      const group = await client.getEntity(groupUsername);
      
      await client.invoke(new Api.messages.SendReaction({
        peer: group,
        msgId: messageId,
        reaction: [new Api.ReactionEmoji({ emoticon: emoji })]
      }));
      
      await client.disconnect();
      
      return { success: true, emoji, messageId };
      
    } catch (error) {
      await client.disconnect();
      throw error;
    }
  }

  async executeScheduledMessage(task) {
    const { telegramAccount } = task;
    const { targetType, targetId, message, mediaPath } = task.config;
    
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    const client = new TelegramApi(new StringSession(telegramAccount.sessionString), apiId, apiHash);

    try {
      await client.connect();
      
      const target = await client.getEntity(targetId);
      
      const result = await client.sendMessage(target, {
        message: message,
        file: mediaPath ? mediaPath : undefined
      });
      
      await telegramAccount.incrementMessageCount();
      await client.disconnect();
      
      return { success: true, messageId: result.id };
      
    } catch (error) {
      await client.disconnect();
      throw error;
    }
  }

  async executeMemberScraping(task) {
    const { telegramAccount } = task;
    const { groupUsername, limit = 1000 } = task.config;
    
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    const client = new TelegramApi(new StringSession(telegramAccount.sessionString), apiId, apiHash);

    try {
      await client.connect();
      
      const group = await client.getEntity(groupUsername);
      const participants = await client.getParticipants(group, { limit });
      
      let scrapedCount = 0;
      
      for (const participant of participants) {
        const existingMember = await ScrapedMember.findOne({
          where: { 
            telegramId: participant.id.toString(),
            userId: task.userId 
          }
        });

        if (!existingMember) {
          await ScrapedMember.create({
            userId: task.userId,
            telegramAccountId: telegramAccount.id,
            telegramId: participant.id.toString(),
            username: participant.username,
            firstName: participant.firstName,
            lastName: participant.lastName,
            isBot: participant.bot || false,
            isVerified: participant.verified || false,
            isPremium: participant.premium || false,
            sourceGroupId: group.id.toString(),
            sourceGroupTitle: group.title,
            sourceGroupUsername: groupUsername,
            status: 'active'
          });
          scrapedCount++;
        }
      }
      
      await client.disconnect();
      
      return { success: true, scrapedCount, totalFound: participants.length };
      
    } catch (error) {
      await client.disconnect();
      throw error;
    }
  }

  async executeMemberAdding(task) {
    const { telegramAccount } = task;
    const { groupUsername, memberIds, delay = 30 } = task.config;
    
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    const client = new TelegramApi(new StringSession(telegramAccount.sessionString), apiId, apiHash);

    try {
      await client.connect();
      
      const group = await client.getEntity(groupUsername);
      let addedCount = 0;
      const errors = [];
      
      for (const memberId of memberIds) {
        try {
          const member = await ScrapedMember.findByPk(memberId);
          if (!member) continue;
          
          await client.invoke(new Api.channels.InviteToChannel({
            channel: group,
            users: [member.telegramId]
          }));
          
          addedCount++;
          await telegramAccount.incrementMemberCount();
          
          if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay * 1000));
          }
          
        } catch (error) {
          errors.push(`Failed to add member ${memberId}: ${error.message}`);
        }
      }
      
      await client.disconnect();
      
      return { success: true, addedCount, errors };
      
    } catch (error) {
      await client.disconnect();
      throw error;
    }
  }

  scheduleRecurringTasks() {
    // Check for auto-leave tasks every hour
    cron.schedule('0 * * * *', async () => {
      await this.checkAutoLeaveTasks();
    });
    
    // Process scheduled messages every minute
    cron.schedule('* * * * *', async () => {
      await this.processScheduledMessages();
    });
  }

  async checkAutoLeaveTasks() {
    try {
      const groupsToLeave = await GroupManagement.findAll({
        where: {
          autoLeaveEnabled: true,
          status: 'active'
        },
        include: ['telegramAccount']
      });

      for (const group of groupsToLeave) {
        if (group.shouldAutoLeave()) {
          await this.createTask({
            userId: group.userId,
            telegramAccountId: group.telegramAccountId,
            taskType: 'auto_leave',
            config: {
              groupUsername: group.groupUsername
            },
            executeAt: new Date()
          });
        }
      }
    } catch (error) {
      console.error('Error checking auto-leave tasks:', error);
    }
  }

  async processScheduledMessages() {
    // This is handled by the main task processor
    // Just ensure scheduled message tasks are created properly
  }

  async createTask(taskData) {
    return await AutomationTask.create({
      ...taskData,
      executeAt: taskData.executeAt || new Date()
    });
  }

  async pauseTask(taskId) {
    const task = await AutomationTask.findByPk(taskId);
    if (task) {
      task.status = 'paused';
      await task.save();
    }
  }

  async resumeTask(taskId) {
    const task = await AutomationTask.findByPk(taskId);
    if (task && task.status === 'paused') {
      task.status = 'pending';
      await task.save();
    }
  }

  async cancelTask(taskId) {
    const task = await AutomationTask.findByPk(taskId);
    if (task) {
      await task.destroy();
    }
  }
}

module.exports = new AutomationService();
