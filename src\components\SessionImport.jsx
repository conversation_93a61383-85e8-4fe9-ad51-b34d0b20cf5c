import React, { useState, useRef } from 'react';
import { Upload, FileText, Smartphone, Monitor, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const SessionImport = () => {
  const [importType, setImportType] = useState('file');
  const [accountName, setAccountName] = useState('');
  const [sessionString, setSessionString] = useState('');
  const [jsonData, setJsonData] = useState('');
  const [uploading, setUploading] = useState(false);
  const [results, setResults] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  const supportedFormats = [
    {
      type: 'tdata',
      description: 'Telegram Desktop Data',
      extensions: '.tdata, .zip',
      icon: <Monitor className="w-5 h-5" />,
      notes: 'TData folder from Telegram Desktop'
    },
    {
      type: 'json',
      description: 'JSON Session File',
      extensions: '.json',
      icon: <FileText className="w-5 h-5" />,
      notes: 'JSON file with session data'
    },
    {
      type: 'session',
      description: 'Session String',
      extensions: '.session',
      icon: <Smartphone className="w-5 h-5" />,
      notes: 'Plain text session string'
    }
  ];

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  const handleFileSelect = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files);
    }
  };

  const handleFileUpload = async (files) => {
    if (!accountName.trim()) {
      alert('Please enter an account name first');
      return;
    }

    setUploading(true);
    setResults(null);

    try {
      const formData = new FormData();
      formData.append('accountName', accountName);
      formData.append('importType', 'file');

      for (let i = 0; i < files.length; i++) {
        formData.append('sessionFiles', files[i]);
      }

      const response = await fetch('/api/session-import/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      const data = await response.json();
      setResults(data);

    } catch (error) {
      console.error('Upload error:', error);
      setResults({
        success: false,
        message: 'Upload failed: ' + error.message
      });
    } finally {
      setUploading(false);
    }
  };

  const handleSessionStringImport = async () => {
    if (!accountName.trim() || !sessionString.trim()) {
      alert('Please enter both account name and session string');
      return;
    }

    setUploading(true);
    setResults(null);

    try {
      const response = await fetch('/api/session-import/session-string', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          sessionString: sessionString,
          accountName: accountName
        })
      });

      const data = await response.json();
      setResults(data);

      if (data.success) {
        setSessionString('');
        setAccountName('');
      }

    } catch (error) {
      console.error('Session string import error:', error);
      setResults({
        success: false,
        message: 'Import failed: ' + error.message
      });
    } finally {
      setUploading(false);
    }
  };

  const handleJsonImport = async () => {
    if (!accountName.trim() || !jsonData.trim()) {
      alert('Please enter both account name and JSON data');
      return;
    }

    setUploading(true);
    setResults(null);

    try {
      const parsedJson = JSON.parse(jsonData);
      
      const response = await fetch('/api/session-import/json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          sessionData: parsedJson,
          accountName: accountName
        })
      });

      const data = await response.json();
      setResults(data);

      if (data.success) {
        setJsonData('');
        setAccountName('');
      }

    } catch (error) {
      console.error('JSON import error:', error);
      setResults({
        success: false,
        message: 'Import failed: ' + error.message
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-3 mb-6">
          <Upload className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-800">Import Telegram Sessions</h2>
        </div>

        {/* Import Type Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Import Method
          </label>
          <div className="flex gap-4">
            <button
              onClick={() => setImportType('file')}
              className={`px-4 py-2 rounded-lg border ${
                importType === 'file'
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-gray-50 border-gray-300 text-gray-700'
              }`}
            >
              File Upload
            </button>
            <button
              onClick={() => setImportType('string')}
              className={`px-4 py-2 rounded-lg border ${
                importType === 'string'
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-gray-50 border-gray-300 text-gray-700'
              }`}
            >
              Session String
            </button>
            <button
              onClick={() => setImportType('json')}
              className={`px-4 py-2 rounded-lg border ${
                importType === 'json'
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-gray-50 border-gray-300 text-gray-700'
              }`}
            >
              JSON Data
            </button>
          </div>
        </div>

        {/* Account Name Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Account Name
          </label>
          <input
            type="text"
            value={accountName}
            onChange={(e) => setAccountName(e.target.value)}
            placeholder="Enter a name for this account"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* File Upload */}
        {importType === 'file' && (
          <div className="mb-6">
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-700 mb-2">
                Drop session files here or click to browse
              </p>
              <p className="text-sm text-gray-500 mb-4">
                Supports .tdata, .json, .session files (max 50MB each)
              </p>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".json,.session,.tdata,.zip"
                onChange={handleFileSelect}
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                disabled={uploading}
              >
                {uploading ? 'Uploading...' : 'Select Files'}
              </button>
            </div>
          </div>
        )}

        {/* Session String Input */}
        {importType === 'string' && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Session String
            </label>
            <textarea
              value={sessionString}
              onChange={(e) => setSessionString(e.target.value)}
              placeholder="Paste your session string here..."
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
            />
            <button
              onClick={handleSessionStringImport}
              disabled={uploading || !sessionString.trim() || !accountName.trim()}
              className="mt-3 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400"
            >
              {uploading ? 'Importing...' : 'Import Session'}
            </button>
          </div>
        )}

        {/* JSON Data Input */}
        {importType === 'json' && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              JSON Session Data
            </label>
            <textarea
              value={jsonData}
              onChange={(e) => setJsonData(e.target.value)}
              placeholder='{"session_string": "...", "api_id": "...", "api_hash": "..."}'
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
            />
            <button
              onClick={handleJsonImport}
              disabled={uploading || !jsonData.trim() || !accountName.trim()}
              className="mt-3 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400"
            >
              {uploading ? 'Importing...' : 'Import JSON'}
            </button>
          </div>
        )}

        {/* Results */}
        {results && (
          <div className="mb-6">
            <div className={`p-4 rounded-lg border ${
              results.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center gap-2 mb-2">
                {results.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className={`font-medium ${
                  results.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {results.message}
                </span>
              </div>

              {results.results && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 mb-2">Import Results:</p>
                  {results.results.map((result, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      {result.success ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500" />
                      )}
                      <span>{result.filename}: {result.success ? 'Success' : result.error}</span>
                    </div>
                  ))}
                </div>
              )}

              {results.summary && (
                <div className="mt-3 text-sm text-gray-600">
                  Summary: {results.summary.successful}/{results.summary.total} successful
                </div>
              )}
            </div>
          </div>
        )}

        {/* Supported Formats */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Supported Formats</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {supportedFormats.map((format) => (
              <div key={format.type} className="bg-white rounded-lg p-4 border">
                <div className="flex items-center gap-2 mb-2">
                  {format.icon}
                  <span className="font-medium text-gray-800">{format.description}</span>
                </div>
                <p className="text-sm text-gray-600 mb-1">
                  Extensions: {format.extensions}
                </p>
                <p className="text-xs text-gray-500">{format.notes}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionImport;
