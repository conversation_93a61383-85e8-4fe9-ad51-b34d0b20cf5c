const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class UserActivity extends Model {}

UserActivity.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  groupId: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Telegram group/chat ID'
  },
  memberId: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Telegram user ID'
  },
  messageCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  reactionCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  firstSeen: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  lastActive: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  lastMessageDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastReactionDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  messageHistory: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: [],
    comment: 'Array of recent message data'
  },
  engagementScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Calculated engagement score (0-100)'
  },
  lastScoreUpdate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'User-defined tags for this member'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'UserActivity',
  tableName: 'user_activities',
  timestamps: true,
  indexes: [
    {
      fields: ['user_id', 'group_id']
    },
    {
      fields: ['telegram_account_id']
    },
    {
      fields: ['group_id', 'member_id']
    },
    {
      fields: ['engagement_score']
    },
    {
      fields: ['last_active']
    }
  ]
});

module.exports = UserActivity; 