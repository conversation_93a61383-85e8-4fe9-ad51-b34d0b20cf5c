const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const TelegramAccount = sequelize.define('TelegramAccount', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  phoneNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isNumeric: true,
      len: [10, 15]
    }
  },
  accountName: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  username: {
    type: DataTypes.STRING,
    allowNull: true
  },
  telegramId: {
    type: DataTypes.BIGINT,
    allowNull: true,
    unique: true
  },
  sessionString: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  phoneCodeHash: {
    type: DataTypes.STRING,
    allowNull: true
  },
  deviceFingerprint: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON string containing device spoofing information'
  },
  proxyId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'proxy_configs',
      key: 'id'
    }
  },
  proxyRotatedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  proxyRotationReason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isOnline: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  lastActivity: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'code_sent', 'active', 'banned', 'limited', 'error'),
    defaultValue: 'pending'
  },
  proxyHost: {
    type: DataTypes.STRING,
    allowNull: true
  },
  proxyPort: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  proxyUsername: {
    type: DataTypes.STRING,
    allowNull: true
  },
  proxyPassword: {
    type: DataTypes.STRING,
    allowNull: true
  },
  dailyMessagesSent: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  dailyMembersAdded: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  totalMessagesSent: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  totalMembersAdded: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastResetDate: {
    type: DataTypes.DATEONLY,
    defaultValue: DataTypes.NOW
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
});

// Instance methods
TelegramAccount.prototype.resetDailyCounters = function() {
  const today = new Date().toISOString().split('T')[0];
  if (this.lastResetDate !== today) {
    this.dailyMessagesSent = 0;
    this.dailyMembersAdded = 0;
    this.lastResetDate = today;
    return this.save();
  }
  return Promise.resolve(this);
};

TelegramAccount.prototype.incrementMessageCount = function() {
  this.dailyMessagesSent += 1;
  this.totalMessagesSent += 1;
  this.lastActivity = new Date();
  return this.save();
};

TelegramAccount.prototype.incrementMemberCount = function() {
  this.dailyMembersAdded += 1;
  this.totalMembersAdded += 1;
  this.lastActivity = new Date();
  return this.save();
};

module.exports = TelegramAccount;
