import React, { useState, useEffect } from 'react';
import api from '../services/api';
import toast from 'react-hot-toast';
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  ClockIcon,
  CogIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

const Automation = () => {
  const [tasks, setTasks] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('tasks');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    status: '',
    taskType: ''
  });

  const [newTask, setNewTask] = useState({
    telegramAccountId: '',
    taskType: 'auto_join',
    config: {},
    scheduledAt: '',
    isRecurring: false,
    recurringPattern: '',
    priority: 5
  });

  useEffect(() => {
    fetchTasks();
    fetchAccounts();
  }, [filters]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const response = await api.get('/automation/tasks', { params: filters });
      setTasks(response.data.tasks);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
      toast.error('Failed to fetch automation tasks');
    } finally {
      setLoading(false);
    }
  };

  const fetchAccounts = async () => {
    try {
      const response = await api.getTelegramAccounts();
      setAccounts(response.data.accounts.filter(acc => acc.isActive));
    } catch (error) {
      console.error('Failed to fetch accounts:', error);
    }
  };

  const handleCreateTask = async (e) => {
    e.preventDefault();
    try {
      await api.post('/automation/tasks', newTask);
      toast.success('Automation task created successfully');
      setShowCreateModal(false);
      setNewTask({
        telegramAccountId: '',
        taskType: 'auto_join',
        config: {},
        scheduledAt: '',
        isRecurring: false,
        recurringPattern: '',
        priority: 5
      });
      fetchTasks();
    } catch (error) {
      console.error('Failed to create task:', error);
      toast.error(error.response?.data?.error || 'Failed to create task');
    }
  };

  const handlePauseTask = async (taskId) => {
    try {
      await api.put(`/automation/tasks/${taskId}/pause`);
      toast.success('Task paused');
      fetchTasks();
    } catch (error) {
      console.error('Failed to pause task:', error);
      toast.error('Failed to pause task');
    }
  };

  const handleResumeTask = async (taskId) => {
    try {
      await api.put(`/automation/tasks/${taskId}/resume`);
      toast.success('Task resumed');
      fetchTasks();
    } catch (error) {
      console.error('Failed to resume task:', error);
      toast.error('Failed to resume task');
    }
  };

  const handleCancelTask = async (taskId) => {
    if (!confirm('Are you sure you want to cancel this task?')) return;
    
    try {
      await api.delete(`/automation/tasks/${taskId}`);
      toast.success('Task cancelled');
      fetchTasks();
    } catch (error) {
      console.error('Failed to cancel task:', error);
      toast.error('Failed to cancel task');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'running': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'paused': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTaskTypeLabel = (taskType) => {
    const labels = {
      'auto_join': 'Auto Join',
      'auto_leave': 'Auto Leave',
      'auto_react': 'Auto React',
      'auto_forward': 'Auto Forward',
      'scheduled_message': 'Scheduled Message',
      'member_scraping': 'Member Scraping',
      'member_adding': 'Member Adding'
    };
    return labels[taskType] || taskType;
  };

  const renderTaskConfig = (task) => {
    const config = task.config || {};
    switch (task.taskType) {
      case 'auto_join':
      case 'auto_leave':
        return config.groupUsername || 'N/A';
      case 'scheduled_message':
        return `${config.targetType}: ${config.targetId}`;
      case 'auto_react':
        return `${config.groupUsername} - ${config.emoji}`;
      case 'member_scraping':
        return `${config.groupUsername} (${config.limit || 1000} members)`;
      case 'member_adding':
        return `${config.groupUsername} (${config.memberIds?.length || 0} members)`;
      default:
        return 'N/A';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Automation</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage automated tasks and workflows
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Create Task
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card mb-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <select
              className="mt-1 input-field"
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value, page: 1})}
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="running">Running</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="paused">Paused</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Task Type</label>
            <select
              className="mt-1 input-field"
              value={filters.taskType}
              onChange={(e) => setFilters({...filters, taskType: e.target.value, page: 1})}
            >
              <option value="">All Types</option>
              <option value="auto_join">Auto Join</option>
              <option value="auto_leave">Auto Leave</option>
              <option value="auto_react">Auto React</option>
              <option value="scheduled_message">Scheduled Message</option>
              <option value="member_scraping">Member Scraping</option>
              <option value="member_adding">Member Adding</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Per Page</label>
            <select
              className="mt-1 input-field"
              value={filters.limit}
              onChange={(e) => setFilters({...filters, limit: parseInt(e.target.value), page: 1})}
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={fetchTasks}
              className="btn-secondary w-full"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Tasks Table */}
      {tasks.length === 0 ? (
        <div className="text-center py-12">
          <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No automation tasks</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first automation task.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary"
            >
              Create Task
            </button>
          </div>
        </div>
      ) : (
        <div className="card overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Task
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Account
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Scheduled
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {tasks.map((task) => (
                  <tr key={task.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {getTaskTypeLabel(task.taskType)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {renderTaskConfig(task)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.telegramAccount?.accountName || task.telegramAccount?.phoneNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${getStatusColor(task.status)}`}>
                        {task.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {task.scheduledAt ? new Date(task.scheduledAt).toLocaleString() : 'Immediate'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        {task.status === 'running' && (
                          <button
                            onClick={() => handlePauseTask(task.id)}
                            className="text-yellow-600 hover:text-yellow-900"
                          >
                            <PauseIcon className="h-5 w-5" />
                          </button>
                        )}
                        {task.status === 'paused' && (
                          <button
                            onClick={() => handleResumeTask(task.id)}
                            className="text-green-600 hover:text-green-900"
                          >
                            <PlayIcon className="h-5 w-5" />
                          </button>
                        )}
                        {['pending', 'running', 'paused'].includes(task.status) && (
                          <button
                            onClick={() => handleCancelTask(task.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <StopIcon className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setFilters({...filters, page: Math.max(1, filters.page - 1)})}
                  disabled={filters.page === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setFilters({...filters, page: Math.min(pagination.pages, filters.page + 1)})}
                  disabled={filters.page === pagination.pages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{((filters.page - 1) * filters.limit) + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(filters.page * filters.limit, pagination.total)}
                    </span>{' '}
                    of <span className="font-medium">{pagination.total}</span> results
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Create Task Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={() => setShowCreateModal(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleCreateTask}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Create Automation Task</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Telegram Account</label>
                      <select
                        required
                        className="mt-1 input-field"
                        value={newTask.telegramAccountId}
                        onChange={(e) => setNewTask({...newTask, telegramAccountId: e.target.value})}
                      >
                        <option value="">Select account</option>
                        {accounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.accountName} ({account.phoneNumber})
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Task Type</label>
                      <select
                        className="mt-1 input-field"
                        value={newTask.taskType}
                        onChange={(e) => setNewTask({...newTask, taskType: e.target.value})}
                      >
                        <option value="auto_join">Auto Join Group</option>
                        <option value="auto_leave">Auto Leave Group</option>
                        <option value="scheduled_message">Scheduled Message</option>
                        <option value="member_scraping">Member Scraping</option>
                        <option value="member_adding">Member Adding</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Schedule Time (Optional)</label>
                      <input
                        type="datetime-local"
                        className="mt-1 input-field"
                        value={newTask.scheduledAt}
                        onChange={(e) => setNewTask({...newTask, scheduledAt: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Priority (1-10)</label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        className="mt-1 input-field"
                        value={newTask.priority}
                        onChange={(e) => setNewTask({...newTask, priority: parseInt(e.target.value)})}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button type="submit" className="btn-primary sm:ml-3">
                    Create Task
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Automation;
