const redis = require('redis');

class RedisService {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  // Initialize Redis connection
  async connect() {
    try {
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: process.env.REDIS_DB || 0,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      };

      this.client = redis.createClient(redisConfig);

      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('Redis Client Connected');
        this.isConnected = true;
      });

      this.client.on('ready', () => {
        console.log('Redis Client Ready');
        this.isConnected = true;
      });

      this.client.on('end', () => {
        console.log('Redis Client Disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
      return this.client;
    } catch (error) {
      console.error('Redis connection error:', error);
      this.isConnected = false;
      throw error;
    }
  }

  // Check if Redis is connected
  isReady() {
    return this.isConnected && this.client && this.client.isReady;
  }

  // Session management
  async setSession(sessionId, data, expireInSeconds = 86400) {
    try {
      if (!this.isReady()) return false;
      
      const sessionData = JSON.stringify(data);
      await this.client.setEx(`session:${sessionId}`, expireInSeconds, sessionData);
      return true;
    } catch (error) {
      console.error('Set session error:', error);
      return false;
    }
  }

  async getSession(sessionId) {
    try {
      if (!this.isReady()) return null;
      
      const sessionData = await this.client.get(`session:${sessionId}`);
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error('Get session error:', error);
      return null;
    }
  }

  async deleteSession(sessionId) {
    try {
      if (!this.isReady()) return false;
      
      await this.client.del(`session:${sessionId}`);
      return true;
    } catch (error) {
      console.error('Delete session error:', error);
      return false;
    }
  }

  // Cache management
  async set(key, value, expireInSeconds = 3600) {
    try {
      if (!this.isReady()) return false;
      
      const data = typeof value === 'string' ? value : JSON.stringify(value);
      await this.client.setEx(key, expireInSeconds, data);
      return true;
    } catch (error) {
      console.error('Set cache error:', error);
      return false;
    }
  }

  async get(key) {
    try {
      if (!this.isReady()) return null;
      
      const data = await this.client.get(key);
      if (!data) return null;
      
      try {
        return JSON.parse(data);
      } catch {
        return data;
      }
    } catch (error) {
      console.error('Get cache error:', error);
      return null;
    }
  }

  async del(key) {
    try {
      if (!this.isReady()) return false;
      
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Delete cache error:', error);
      return false;
    }
  }

  // Rate limiting
  async checkRateLimit(key, limit, windowInSeconds) {
    try {
      if (!this.isReady()) return { allowed: true, remaining: limit };
      
      const current = await this.client.incr(key);
      
      if (current === 1) {
        await this.client.expire(key, windowInSeconds);
      }
      
      const remaining = Math.max(0, limit - current);
      const allowed = current <= limit;
      
      return { allowed, remaining, current };
    } catch (error) {
      console.error('Rate limit check error:', error);
      return { allowed: true, remaining: limit };
    }
  }

  // Account activity tracking
  async trackAccountActivity(accountId, activity) {
    try {
      if (!this.isReady()) return false;
      
      const key = `activity:${accountId}`;
      const timestamp = Date.now();
      
      // Add activity to sorted set with timestamp as score
      await this.client.zAdd(key, { score: timestamp, value: JSON.stringify(activity) });
      
      // Keep only last 24 hours of activity
      const oneDayAgo = timestamp - (24 * 60 * 60 * 1000);
      await this.client.zRemRangeByScore(key, 0, oneDayAgo);
      
      // Set expiry for the key
      await this.client.expire(key, 86400); // 24 hours
      
      return true;
    } catch (error) {
      console.error('Track activity error:', error);
      return false;
    }
  }

  async getAccountActivity(accountId, hoursBack = 24) {
    try {
      if (!this.isReady()) return [];
      
      const key = `activity:${accountId}`;
      const since = Date.now() - (hoursBack * 60 * 60 * 1000);
      
      const activities = await this.client.zRangeByScore(key, since, '+inf');
      
      return activities.map(activity => {
        try {
          return JSON.parse(activity);
        } catch {
          return activity;
        }
      });
    } catch (error) {
      console.error('Get activity error:', error);
      return [];
    }
  }

  // Queue management for background tasks
  async addToQueue(queueName, task, priority = 0) {
    try {
      if (!this.isReady()) return false;
      
      const taskData = JSON.stringify({
        ...task,
        addedAt: Date.now(),
        id: `${queueName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });
      
      await this.client.zAdd(`queue:${queueName}`, { score: priority, value: taskData });
      return true;
    } catch (error) {
      console.error('Add to queue error:', error);
      return false;
    }
  }

  async getFromQueue(queueName, count = 1) {
    try {
      if (!this.isReady()) return [];
      
      // Get highest priority tasks
      const tasks = await this.client.zPopMax(`queue:${queueName}`, count);
      
      return tasks.map(task => {
        try {
          return JSON.parse(task.value);
        } catch {
          return { data: task.value, score: task.score };
        }
      });
    } catch (error) {
      console.error('Get from queue error:', error);
      return [];
    }
  }

  async getQueueLength(queueName) {
    try {
      if (!this.isReady()) return 0;
      
      return await this.client.zCard(`queue:${queueName}`);
    } catch (error) {
      console.error('Get queue length error:', error);
      return 0;
    }
  }

  // Distributed locks
  async acquireLock(lockKey, expireInSeconds = 30) {
    try {
      if (!this.isReady()) return null;
      
      const lockValue = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const result = await this.client.set(
        `lock:${lockKey}`, 
        lockValue, 
        { EX: expireInSeconds, NX: true }
      );
      
      return result === 'OK' ? lockValue : null;
    } catch (error) {
      console.error('Acquire lock error:', error);
      return null;
    }
  }

  async releaseLock(lockKey, lockValue) {
    try {
      if (!this.isReady()) return false;
      
      const script = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;
      
      const result = await this.client.eval(script, {
        keys: [`lock:${lockKey}`],
        arguments: [lockValue]
      });
      
      return result === 1;
    } catch (error) {
      console.error('Release lock error:', error);
      return false;
    }
  }

  // Health check
  async healthCheck() {
    try {
      if (!this.isReady()) return false;
      
      await this.client.ping();
      return true;
    } catch (error) {
      console.error('Redis health check error:', error);
      return false;
    }
  }

  // Cleanup and disconnect
  async disconnect() {
    try {
      if (this.client) {
        await this.client.quit();
        this.isConnected = false;
      }
    } catch (error) {
      console.error('Redis disconnect error:', error);
    }
  }

  // Get Redis info
  async getInfo() {
    try {
      if (!this.isReady()) return null;
      
      const info = await this.client.info();
      return info;
    } catch (error) {
      console.error('Get Redis info error:', error);
      return null;
    }
  }
}

// Create singleton instance
const redisService = new RedisService();

module.exports = redisService;
