const express = require('express');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { AutomationTask, TelegramAccount, GroupManagement } = require('../models');
const automationService = require('../services/automationService');

const router = express.Router();

// Get automation tasks
router.get('/tasks', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 50, status, taskType } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { userId: req.user.id };
    if (status) whereClause.status = status;
    if (taskType) whereClause.taskType = taskType;

    const { count, rows } = await AutomationTask.findAndCountAll({
      where: whereClause,
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['id', 'accountName', 'phoneNumber']
      }],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      tasks: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get automation tasks error:', error);
    res.status(500).json({ error: 'Failed to fetch automation tasks' });
  }
});

// Create automation task
router.post('/tasks', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { 
      telegramAccountId, 
      taskType, 
      config, 
      scheduledAt, 
      isRecurring, 
      recurringPattern,
      priority = 5 
    } = req.body;

    if (!telegramAccountId || !taskType || !config) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const task = await AutomationTask.create({
      userId: req.user.id,
      telegramAccountId,
      taskType,
      config,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : new Date(),
      executeAt: scheduledAt ? new Date(scheduledAt) : new Date(),
      isRecurring: isRecurring || false,
      recurringPattern,
      priority
    });

    res.status(201).json({
      message: 'Automation task created successfully',
      task
    });
  } catch (error) {
    console.error('Create automation task error:', error);
    res.status(500).json({ error: 'Failed to create automation task' });
  }
});

// Auto-join groups
router.post('/auto-join', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { telegramAccountId, groupUsernames, delay = 5 } = req.body;

    if (!telegramAccountId || !groupUsernames || !Array.isArray(groupUsernames)) {
      return res.status(400).json({ error: 'Account ID and group usernames array are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const tasks = [];
    for (const groupUsername of groupUsernames) {
      const task = await automationService.createTask({
        userId: req.user.id,
        telegramAccountId,
        taskType: 'auto_join',
        config: { groupUsername, delay },
        executeAt: new Date(Date.now() + tasks.length * delay * 1000)
      });
      tasks.push(task);
    }

    res.json({
      message: `Created ${tasks.length} auto-join tasks`,
      tasks
    });
  } catch (error) {
    console.error('Auto-join error:', error);
    res.status(500).json({ error: 'Failed to create auto-join tasks' });
  }
});

// Auto-leave groups
router.post('/auto-leave', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { telegramAccountId, groupUsernames } = req.body;

    if (!telegramAccountId || !groupUsernames || !Array.isArray(groupUsernames)) {
      return res.status(400).json({ error: 'Account ID and group usernames array are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const tasks = [];
    for (const groupUsername of groupUsernames) {
      const task = await automationService.createTask({
        userId: req.user.id,
        telegramAccountId,
        taskType: 'auto_leave',
        config: { groupUsername },
        executeAt: new Date()
      });
      tasks.push(task);
    }

    res.json({
      message: `Created ${tasks.length} auto-leave tasks`,
      tasks
    });
  } catch (error) {
    console.error('Auto-leave error:', error);
    res.status(500).json({ error: 'Failed to create auto-leave tasks' });
  }
});

// Schedule message
router.post('/schedule-message', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { 
      telegramAccountId, 
      targetType, 
      targetId, 
      message, 
      scheduledAt,
      mediaPath,
      isRecurring,
      recurringPattern
    } = req.body;

    if (!telegramAccountId || !targetType || !targetId || !message || !scheduledAt) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const task = await automationService.createTask({
      userId: req.user.id,
      telegramAccountId,
      taskType: 'scheduled_message',
      config: { targetType, targetId, message, mediaPath },
      scheduledAt: new Date(scheduledAt),
      executeAt: new Date(scheduledAt),
      isRecurring: isRecurring || false,
      recurringPattern
    });

    res.json({
      message: 'Message scheduled successfully',
      task
    });
  } catch (error) {
    console.error('Schedule message error:', error);
    res.status(500).json({ error: 'Failed to schedule message' });
  }
});

// Auto-react to messages
router.post('/auto-react', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { telegramAccountId, groupUsername, messageId, emoji } = req.body;

    if (!telegramAccountId || !groupUsername || !messageId || !emoji) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const task = await automationService.createTask({
      userId: req.user.id,
      telegramAccountId,
      taskType: 'auto_react',
      config: { groupUsername, messageId, emoji },
      executeAt: new Date()
    });

    res.json({
      message: 'Auto-react task created successfully',
      task
    });
  } catch (error) {
    console.error('Auto-react error:', error);
    res.status(500).json({ error: 'Failed to create auto-react task' });
  }
});

// Get group management settings
router.get('/groups', authenticateToken, async (req, res) => {
  try {
    const groups = await GroupManagement.findAll({
      where: { userId: req.user.id },
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['id', 'accountName', 'phoneNumber']
      }],
      order: [['lastActivity', 'DESC']]
    });

    res.json({ groups });
  } catch (error) {
    console.error('Get groups error:', error);
    res.status(500).json({ error: 'Failed to fetch groups' });
  }
});

// Update group automation settings
router.put('/groups/:id', authenticateToken, async (req, res) => {
  try {
    const groupId = req.params.id;
    const {
      autoJoinEnabled,
      autoLeaveEnabled,
      autoLeaveAfterDays,
      autoReactEnabled,
      autoReactEmojis,
      autoForwardEnabled,
      forwardToGroups
    } = req.body;

    const group = await GroupManagement.findOne({
      where: { id: groupId, userId: req.user.id }
    });

    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }

    await group.update({
      autoJoinEnabled,
      autoLeaveEnabled,
      autoLeaveAfterDays,
      autoReactEnabled,
      autoReactEmojis,
      autoForwardEnabled,
      forwardToGroups
    });

    res.json({
      message: 'Group automation settings updated successfully',
      group
    });
  } catch (error) {
    console.error('Update group settings error:', error);
    res.status(500).json({ error: 'Failed to update group settings' });
  }
});

// Pause task
router.put('/tasks/:id/pause', authenticateToken, async (req, res) => {
  try {
    const taskId = req.params.id;
    
    const task = await AutomationTask.findOne({
      where: { id: taskId, userId: req.user.id }
    });

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    await automationService.pauseTask(taskId);

    res.json({ message: 'Task paused successfully' });
  } catch (error) {
    console.error('Pause task error:', error);
    res.status(500).json({ error: 'Failed to pause task' });
  }
});

// Resume task
router.put('/tasks/:id/resume', authenticateToken, async (req, res) => {
  try {
    const taskId = req.params.id;
    
    const task = await AutomationTask.findOne({
      where: { id: taskId, userId: req.user.id }
    });

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    await automationService.resumeTask(taskId);

    res.json({ message: 'Task resumed successfully' });
  } catch (error) {
    console.error('Resume task error:', error);
    res.status(500).json({ error: 'Failed to resume task' });
  }
});

// Cancel task
router.delete('/tasks/:id', authenticateToken, async (req, res) => {
  try {
    const taskId = req.params.id;
    
    const task = await AutomationTask.findOne({
      where: { id: taskId, userId: req.user.id }
    });

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    await automationService.cancelTask(taskId);

    res.json({ message: 'Task cancelled successfully' });
  } catch (error) {
    console.error('Cancel task error:', error);
    res.status(500).json({ error: 'Failed to cancel task' });
  }
});

module.exports = router;
