const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ScrapedMember = sequelize.define('ScrapedMember', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  telegramId: {
    type: DataTypes.BIGINT,
    allowNull: false
  },
  username: {
    type: DataTypes.STRING,
    allowNull: true
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  phoneNumber: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isBot: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isPremium: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  sourceGroupId: {
    type: DataTypes.BIGINT,
    allowNull: true
  },
  sourceGroupTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  sourceGroupUsername: {
    type: DataTypes.STRING,
    allowNull: true
  },
  joinDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastSeen: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'banned', 'deleted'),
    defaultValue: 'active'
  },
  addedToGroups: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  messagesSent: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastMessageDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
});

module.exports = ScrapedMember;
