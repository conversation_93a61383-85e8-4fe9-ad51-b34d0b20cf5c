import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bear<PERSON> ${token}`;
    } else {
      delete this.client.defaults.headers.common['Authorization'];
    }
  }

  // Generic methods
  get(url, config = {}) {
    return this.client.get(url, config);
  }

  post(url, data = {}, config = {}) {
    return this.client.post(url, data, config);
  }

  put(url, data = {}, config = {}) {
    return this.client.put(url, data, config);
  }

  delete(url, config = {}) {
    return this.client.delete(url, config);
  }

  // Auth methods
  login(email, password) {
    return this.post('/auth/login', { email, password });
  }

  register(userData) {
    return this.post('/auth/register', userData);
  }

  getCurrentUser() {
    return this.get('/auth/me');
  }

  // Telegram account methods
  getTelegramAccounts() {
    return this.get('/telegram/accounts');
  }

  addTelegramAccount(phoneNumber, accountName) {
    return this.post('/telegram/accounts', { phoneNumber, accountName });
  }

  verifyTelegramAccount(accountId, code) {
    return this.post(`/telegram/accounts/${accountId}/verify`, { code });
  }

  deleteTelegramAccount(accountId) {
    return this.delete(`/telegram/accounts/${accountId}`);
  }

  // Member methods
  getScrapedMembers(filters = {}) {
    return this.get('/members', { params: filters });
  }

  scrapeMembers(accountId, groupUsername, options = {}) {
    return this.post('/members/scrape', { accountId, groupUsername, ...options });
  }

  addMembersToGroup(accountId, groupUsername, memberIds, options = {}) {
    return this.post('/members/add', { accountId, groupUsername, memberIds, ...options });
  }

  exportMembers(filters = {}) {
    return this.get('/members/export', { params: filters, responseType: 'blob' });
  }

  // Message methods
  sendMessage(accountId, targetType, targetId, message, options = {}) {
    return this.post('/messages/send', { 
      accountId, 
      targetType, 
      targetId, 
      message, 
      ...options 
    });
  }

  sendBulkMessages(accountId, targets, message, options = {}) {
    return this.post('/messages/bulk', { 
      accountId, 
      targets, 
      message, 
      ...options 
    });
  }

  getMessageHistory(filters = {}) {
    return this.get('/messages/history', { params: filters });
  }

  // Automation methods
  getAutomationTasks(filters = {}) {
    return this.get('/automation/tasks', { params: filters });
  }

  createAutomationTask(taskData) {
    return this.post('/automation/tasks', taskData);
  }

  pauseAutomationTask(taskId) {
    return this.put(`/automation/tasks/${taskId}/pause`);
  }

  resumeAutomationTask(taskId) {
    return this.put(`/automation/tasks/${taskId}/resume`);
  }

  cancelAutomationTask(taskId) {
    return this.delete(`/automation/tasks/${taskId}`);
  }

  autoJoinGroups(accountId, groupUsernames, delay = 5) {
    return this.post('/automation/auto-join', { telegramAccountId: accountId, groupUsernames, delay });
  }

  autoLeaveGroups(accountId, groupUsernames) {
    return this.post('/automation/auto-leave', { telegramAccountId: accountId, groupUsernames });
  }

  scheduleMessage(messageData) {
    return this.post('/automation/schedule-message', messageData);
  }

  // Tools methods
  checkUsername(username, telegramAccountId) {
    return this.post('/tools/check-username', { username, telegramAccountId });
  }

  checkUsernamesBulk(usernames, telegramAccountId) {
    return this.post('/tools/check-usernames-bulk', { usernames, telegramAccountId });
  }

  findAvailableUsernames(baseUsername, telegramAccountId, count = 10) {
    return this.post('/tools/find-available-usernames', { baseUsername, telegramAccountId, count });
  }

  formatPhone(phoneNumber, countryCode) {
    return this.post('/tools/format-phone', { phoneNumber, countryCode });
  }

  formatPhonesBulk(phoneNumbers, countryCode) {
    return this.post('/tools/format-phones-bulk', { phoneNumbers, countryCode });
  }

  getCountryCodes() {
    return this.get('/tools/country-codes');
  }

  generatePhoneVariations(baseNumber, count = 10) {
    return this.post('/tools/generate-phone-variations', { baseNumber, count });
  }

  // Message templates
  getMessageTemplates() {
    return this.get('/tools/message-templates');
  }

  createMessageTemplate(templateData) {
    return this.post('/tools/message-templates', templateData);
  }

  updateMessageTemplate(templateId, templateData) {
    return this.put(`/tools/message-templates/${templateId}`, templateData);
  }

  deleteMessageTemplate(templateId) {
    return this.delete(`/tools/message-templates/${templateId}`);
  }

  // Proxy management
  getProxies() {
    return this.get('/tools/proxies');
  }

  createProxy(proxyData) {
    return this.post('/tools/proxies', proxyData);
  }

  testProxy(proxyId) {
    return this.post(`/tools/proxies/${proxyId}/test`);
  }

  deleteProxy(proxyId) {
    return this.delete(`/tools/proxies/${proxyId}`);
  }

  generateGroupLink(accountId, groupUsername, expireDate, usageLimit) {
    return this.post('/tools/generate-group-link', { telegramAccountId: accountId, groupUsername, expireDate, usageLimit });
  }

  // Analytics methods
  getAnalyticsDashboard(timeRange = '30d') {
    return this.get(`/analytics/dashboard?timeRange=${timeRange}`);
  }

  getAnalyticsEvents(params = {}) {
    return this.get('/analytics/events', { params });
  }

  getAnalyticsDaily(days = 30) {
    return this.get(`/analytics/daily?days=${days}`);
  }

  getAnalyticsAccounts() {
    return this.get('/analytics/accounts');
  }

  getAnalyticsActivity(limit = 50) {
    return this.get(`/analytics/activity?limit=${limit}`);
  }

  getAnalyticsUsage(timeRange = '24h') {
    return this.get(`/analytics/usage?timeRange=${timeRange}`);
  }

  exportAnalytics(params = {}) {
    return this.get('/analytics/export', { params });
  }

  getAccountHealthSummary() {
    return this.get('/analytics/health/summary');
  }

  getAccountHealth(accountId) {
    return this.get(`/analytics/health/${accountId}`);
  }

  startAccountWarmup(accountId) {
    return this.post(`/analytics/health/${accountId}/warmup`);
  }

  performHealthCheck(accountId) {
    return this.post(`/analytics/health/${accountId}/check`);
  }

  // Payment methods
  getSubscriptionPricing() {
    return this.get('/payment/pricing');
  }

  createPaymentIntent(data) {
    return this.post('/payment/create-intent', data);
  }

  confirmPayment(paymentId) {
    return this.post(`/payment/confirm/${paymentId}`);
  }

  getPaymentHistory(params = {}) {
    return this.get('/payment/history', { params });
  }

  getSubscriptionStatus() {
    return this.get('/payment/subscription');
  }

  requestRefund(paymentId, data) {
    return this.post(`/payment/refund/${paymentId}`, data);
  }

  cancelSubscription() {
    return this.post('/payment/cancel-subscription');
  }

  getInvoice(paymentId) {
    return this.get(`/payment/invoice/${paymentId}`);
  }

  // Backup methods
  createBackup(data) {
    return this.post('/backup/create', data);
  }

  listBackups() {
    return this.get('/backup/list');
  }

  downloadBackup(backupName) {
    return this.get(`/backup/download/${backupName}`, { responseType: 'blob' });
  }

  deleteBackup(backupName) {
    return this.delete(`/backup/${backupName}`);
  }

  restoreBackup(data) {
    return this.post('/backup/restore', data);
  }

  uploadBackup(formData) {
    return this.post('/backup/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }

  getBackupInfo(backupName) {
    return this.get(`/backup/info/${backupName}`);
  }

  cleanupBackups(data) {
    return this.post('/backup/cleanup', data);
  }
}

const api = new ApiService();
export default api;
