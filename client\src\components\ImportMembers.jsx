import React, { useState } from 'react';
import { Upload, Users, AlertCircle, CheckCircle, X, FileText } from 'lucide-react';

const ImportMembers = ({ onClose, onImportSuccess }) => {
  const [file, setFile] = useState(null);
  const [sourceGroupTitle, setSourceGroupTitle] = useState('');
  const [sourceGroupUsername, setSourceGroupUsername] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    setFile(selectedFile);
    setError('');
    setResult(null);
  };

  const handleImport = async () => {
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('membersFile', file);
      formData.append('sourceGroupTitle', sourceGroupTitle);
      formData.append('sourceGroupUsername', sourceGroupUsername);

      const response = await fetch('/api/members/import', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
        if (onImportSuccess) {
          onImportSuccess(data);
        }
      } else {
        setError(data.error || 'Import failed');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Import Members</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        {/* File Upload */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select File (JSON or CSV)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
            <input
              type="file"
              onChange={handleFileChange}
              accept=".json,.csv"
              className="hidden"
              id="members-file-upload"
            />
            <label htmlFor="members-file-upload" className="cursor-pointer">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
              <p className="text-sm text-gray-600">
                {file ? file.name : 'Click to select file'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                JSON or CSV file with member data
              </p>
            </label>
          </div>
        </div>

        {/* Source Group Information */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Source Group Title (Optional)
          </label>
          <input
            type="text"
            value={sourceGroupTitle}
            onChange={(e) => setSourceGroupTitle(e.target.value)}
            placeholder="e.g., Crypto Trading Group"
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Source Group Username (Optional)
          </label>
          <input
            type="text"
            value={sourceGroupUsername}
            onChange={(e) => setSourceGroupUsername(e.target.value)}
            placeholder="e.g., @cryptotrading"
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            <div className="flex items-center">
              <AlertCircle size={16} className="mr-2" />
              {error}
            </div>
          </div>
        )}

        {/* Success Display */}
        {result && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            <div className="flex items-center mb-2">
              <CheckCircle size={16} className="mr-2" />
              {result.message}
            </div>
            {result.results && (
              <div className="text-sm">
                <p>Imported: {result.results.imported}</p>
                {result.results.duplicates > 0 && (
                  <p>Duplicates: {result.results.duplicates}</p>
                )}
                {result.results.errors && result.results.errors.length > 0 && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-red-600">
                      View Errors ({result.results.errors.length})
                    </summary>
                    <div className="mt-1 text-xs max-h-20 overflow-y-auto">
                      {result.results.errors.slice(0, 5).map((error, index) => (
                        <p key={index} className="text-red-600">{error}</p>
                      ))}
                      {result.results.errors.length > 5 && (
                        <p className="text-gray-500">... and {result.results.errors.length - 5} more</p>
                      )}
                    </div>
                  </details>
                )}
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleImport}
            disabled={loading || !file}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Importing...
              </>
            ) : (
              <>
                <Users size={16} className="mr-2" />
                Import Members
              </>
            )}
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-4 text-xs text-gray-500">
          <p className="font-medium mb-1">Required fields in file:</p>
          <ul className="list-disc list-inside space-y-1">
            <li><strong>telegramId</strong> or <strong>username</strong> (at least one required)</li>
            <li>firstName, lastName (optional)</li>
            <li>phoneNumber (optional)</li>
            <li>isBot, isVerified, isPremium (optional booleans)</li>
          </ul>
          
          <div className="mt-3">
            <p className="font-medium mb-1">Example JSON format:</p>
            <div className="bg-gray-100 p-2 rounded text-xs font-mono">
              <pre>{`[
  {
    "telegramId": "123456789",
    "username": "john_doe",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "+1234567890"
  }
]`}</pre>
            </div>
          </div>

          <div className="mt-3">
            <p className="font-medium mb-1">Example CSV format:</p>
            <div className="bg-gray-100 p-2 rounded text-xs font-mono">
              <pre>{`telegramId,username,firstName,lastName
123456789,john_doe,John,Doe
987654321,jane_smith,Jane,Smith`}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImportMembers;
