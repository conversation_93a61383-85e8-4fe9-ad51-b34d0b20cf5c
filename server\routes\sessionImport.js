const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken } = require('../middleware/auth');
const sessionImportService = require('../services/SessionImportService');
const extract = require('extract-zip');
const rimraf = require('rimraf');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/sessions');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `${uniqueSuffix}-${file.originalname}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Max 10 files
  },
  fileFilter: (req, file, cb) => {
    // Allow various session file types
    const allowedTypes = [
      'application/json',
      'text/plain',
      'application/octet-stream',
      'application/x-zip-compressed',
      'application/zip'
    ];
    
    const allowedExtensions = ['.json', '.session', '.tdata', '.zip'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Allowed: .json, .session, .tdata, .zip'));
    }
  }
});

/**
 * Extract a zip file containing TData
 */
async function extractTDataZip(zipPath) {
  try {
    // Create extraction directory
    const extractDir = zipPath.replace('.zip', '-extracted');
    if (!fs.existsSync(extractDir)) {
      fs.mkdirSync(extractDir, { recursive: true });
    }
    
    // Extract the zip file
    await extract(zipPath, { dir: extractDir });
    
    // Find the TData folder in the extracted contents
    let tdataPath = null;
    
    // Check if the zip contains a TData folder directly
    if (fs.existsSync(path.join(extractDir, 'tdata'))) {
      tdataPath = path.join(extractDir, 'tdata');
      console.log('Found tdata folder directly in zip root');
    } 
    // Check if the zip contains a phone number folder with tdata subfolder
    else {
      const entries = fs.readdirSync(extractDir, { withFileTypes: true });
      
      // Look for phone number folders first
      for (const entry of entries) {
        if (entry.isDirectory() && entry.name.startsWith('+')) {
          const possibleTdataPath = path.join(extractDir, entry.name, 'tdata');
          if (fs.existsSync(possibleTdataPath)) {
            tdataPath = path.join(extractDir, entry.name);
            console.log(`Found phone number folder: ${entry.name}`);
            break;
          }
        }
      }
      
      // If no phone number folder found, search recursively
      if (!tdataPath) {
        console.log('No phone number folder found, searching recursively');
        
        // Search for TData folder recursively
        const findTDataFolder = (dir) => {
          const entries = fs.readdirSync(dir, { withFileTypes: true });
          
          for (const entry of entries) {
            if (entry.isDirectory()) {
              const fullPath = path.join(dir, entry.name);
              if (entry.name.toLowerCase() === 'tdata') {
                return fullPath;
              }
              const nestedResult = findTDataFolder(fullPath);
              if (nestedResult) return nestedResult;
            }
          }
          return null;
        };
        
        const foundTdataPath = findTDataFolder(extractDir);
        if (foundTdataPath) {
          // If we found a tdata folder directly, use its parent directory
          // This ensures we capture the phone number folder if it exists
          tdataPath = path.dirname(foundTdataPath);
          console.log(`Found tdata folder at: ${foundTdataPath}`);
        }
      }
    }
    
    if (!tdataPath) {
      throw new Error('No TData folder found in the zip file');
    }
    
    return { extractDir, tdataPath };
  } catch (error) {
    throw new Error(`Failed to extract zip file: ${error.message}`);
  }
}

/**
 * Clean up extracted directories
 */
function cleanupExtractedDir(dirPath) {
  try {
    if (fs.existsSync(dirPath)) {
      rimraf.sync(dirPath);
      console.log(`🗑️ Cleaned up extracted directory: ${dirPath}`);
    }
  } catch (error) {
    console.error('❌ Failed to cleanup directory:', error);
  }
}

/**
 * @route POST /api/session-import/upload
 * @desc Upload session files for import
 * @access Private
 */
router.post('/upload', authenticateToken, upload.array('sessionFiles', 10), async (req, res) => {
  try {
    const { accountName, importType } = req.body;
    const userId = req.user.userId;
    const extractedDirs = []; // Keep track of extracted directories

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }

    if (!accountName) {
      return res.status(400).json({
        success: false,
        message: 'Account name is required'
      });
    }

    const results = [];

    for (const file of req.files) {
      try {
        let account;
        const filePath = file.path;
        const fileExtension = path.extname(file.originalname).toLowerCase();

        // Import based on file type
        if (fileExtension === '.json') {
          account = await sessionImportService.importJSONSession(
            userId, 
            filePath, 
            `${accountName}_${results.length + 1}`
          );
        } else if (fileExtension === '.session') {
          account = await sessionImportService.importSessionFile(
            userId, 
            filePath, 
            `${accountName}_${results.length + 1}`
          );
        } else if (fileExtension === '.zip') {
          // Extract zip file containing TData
          const { extractDir, tdataPath } = await extractTDataZip(filePath);
          extractedDirs.push(extractDir);
          
          account = await sessionImportService.importTDataSession(
            userId, 
            tdataPath, 
            `${accountName}_${results.length + 1}`
          );
        } else if (file.originalname.includes('tdata') || fileExtension === '.tdata') {
          // Handle TData folder
          account = await sessionImportService.importTDataSession(
            userId, 
            filePath, 
            `${accountName}_${results.length + 1}`
          );
        } else {
          throw new Error(`Unsupported file type: ${fileExtension}`);
        }

        results.push({
          filename: file.originalname,
          success: true,
          accountId: account.id,
          accountName: account.accountName
        });

        // Clean up uploaded file
        sessionImportService.cleanupUploadedFile(filePath);

      } catch (error) {
        results.push({
          filename: file.originalname,
          success: false,
          error: error.message
        });

        // Clean up failed upload
        sessionImportService.cleanupUploadedFile(file.path);
      }
    }

    // Clean up extracted directories
    extractedDirs.forEach(dir => cleanupExtractedDir(dir));

    res.json({
      success: true,
      message: 'Session import completed',
      results: results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    });

  } catch (error) {
    console.error('Session import error:', error);
    res.status(500).json({
      success: false,
      message: 'Session import failed',
      error: error.message
    });
  }
});

/**
 * @route POST /api/session-import/json
 * @desc Import session from JSON data
 * @access Private
 */
router.post('/json', authenticateToken, async (req, res) => {
  try {
    const { sessionData, accountName } = req.body;
    const userId = req.user.userId;

    if (!sessionData || !accountName) {
      return res.status(400).json({
        success: false,
        message: 'Session data and account name are required'
      });
    }

    // Create temporary JSON file
    const tempPath = path.join(__dirname, '../uploads/sessions', `temp_${Date.now()}.json`);
    fs.writeFileSync(tempPath, JSON.stringify(sessionData, null, 2));

    // Import the session
    const account = await sessionImportService.importJSONSession(userId, tempPath, accountName);

    // Clean up temp file
    sessionImportService.cleanupUploadedFile(tempPath);

    res.json({
      success: true,
      message: 'Session imported successfully from JSON data',
      account: {
        id: account.id,
        accountName: account.accountName,
        phoneNumber: account.phoneNumber,
        status: account.status
      }
    });

  } catch (error) {
    console.error('JSON session import error:', error);
    res.status(500).json({
      success: false,
      message: 'JSON session import failed',
      error: error.message
    });
  }
});

/**
 * @route POST /api/session-import/session-string
 * @desc Import session from session string
 * @access Private
 */
router.post('/session-string', authenticateToken, async (req, res) => {
  try {
    const { sessionString, accountName } = req.body;
    const userId = req.user.userId;

    if (!sessionString || !accountName) {
      return res.status(400).json({
        success: false,
        message: 'Session string and account name are required'
      });
    }

    // Create temporary session file
    const tempPath = path.join(__dirname, '../uploads/sessions', `temp_${Date.now()}.session`);
    fs.writeFileSync(tempPath, sessionString);

    // Import the session
    const account = await sessionImportService.importSessionFile(userId, tempPath, accountName);

    // Clean up temp file
    sessionImportService.cleanupUploadedFile(tempPath);

    res.json({
      success: true,
      message: 'Session imported successfully from session string',
      account: {
        id: account.id,
        accountName: account.accountName,
        phoneNumber: account.phoneNumber,
        status: account.status
      }
    });

  } catch (error) {
    console.error('Session string import error:', error);
    res.status(500).json({
      success: false,
      message: 'Session string import failed',
      error: error.message
    });
  }
});

/**
 * @route POST /api/session-import/validate/:accountId
 * @desc Validate imported session
 * @access Private
 */
router.post('/validate/:accountId', authenticateToken, async (req, res) => {
  try {
    const { accountId } = req.params;
    const userId = req.user.userId;

    // Verify account belongs to user
    const { TelegramAccount } = require('../models');
    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: userId }
    });

    if (!account) {
      return res.status(404).json({
        success: false,
        message: 'Account not found'
      });
    }

    // Validate the session
    const validation = await sessionImportService.validateImportedSession(accountId);

    res.json({
      success: true,
      message: 'Session validation completed',
      validation: validation,
      account: {
        id: account.id,
        accountName: account.accountName,
        status: validation.valid ? 'active' : 'invalid'
      }
    });

  } catch (error) {
    console.error('Session validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Session validation failed',
      error: error.message
    });
  }
});

/**
 * @route GET /api/session-import/stats
 * @desc Get session import statistics
 * @access Private
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await sessionImportService.getImportStats(req.user.userId);
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Get import stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get import statistics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/session-import/supported-formats
 * @desc Get supported session import formats
 * @access Private
 */
router.get('/supported-formats', authenticateToken, (req, res) => {
  res.json({
    success: true,
    formats: [
      {
        type: 'tdata',
        description: 'Telegram Desktop data folder',
        extensions: ['.tdata', '.zip'],
        notes: 'Contains key_datas, settings0, maps0 files'
      },
      {
        type: 'json',
        description: 'JSON session file',
        extensions: ['.json'],
        notes: 'Supports Telethon, Pyrogram, GramJS formats'
      },
      {
        type: 'session',
        description: 'Session string file',
        extensions: ['.session'],
        notes: 'Plain text file with base64 session string'
      }
    ],
    maxFileSize: '50MB',
    maxFiles: 10
  });
});

module.exports = router;
