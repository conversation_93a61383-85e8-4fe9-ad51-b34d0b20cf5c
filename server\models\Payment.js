const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Payment = sequelize.define('Payment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  stripePaymentIntentId: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  stripeCustomerId: {
    type: DataTypes.STRING,
    allowNull: true
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  status: {
    type: DataTypes.ENUM(
      'pending',
      'processing',
      'succeeded',
      'failed',
      'canceled',
      'refunded'
    ),
    defaultValue: 'pending'
  },
  subscriptionPlan: {
    type: DataTypes.ENUM('basic', 'premium', 'enterprise'),
    allowNull: false
  },
  subscriptionDuration: {
    type: DataTypes.INTEGER, // in months
    allowNull: false,
    defaultValue: 1
  },
  paymentMethod: {
    type: DataTypes.ENUM('stripe', 'paypal', 'crypto', 'bank_transfer'),
    defaultValue: 'stripe'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  paidAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  refundedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  refundAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
});

// Instance methods
Payment.prototype.markAsPaid = function() {
  this.status = 'succeeded';
  this.paidAt = new Date();
  return this.save();
};

Payment.prototype.markAsFailed = function(reason) {
  this.status = 'failed';
  this.notes = reason;
  return this.save();
};

Payment.prototype.processRefund = function(amount, reason) {
  this.status = 'refunded';
  this.refundedAt = new Date();
  this.refundAmount = amount || this.amount;
  this.notes = reason;
  return this.save();
};

// Static methods
Payment.getSubscriptionPricing = function() {
  return {
    basic: {
      monthly: 9.99,
      yearly: 99.99,
      features: {
        maxAccounts: 3,
        dailyMessages: 500,
        dailyAdds: 250,
        automation: true,
        analytics: true,
        support: 'email'
      }
    },
    premium: {
      monthly: 29.99,
      yearly: 299.99,
      features: {
        maxAccounts: 10,
        dailyMessages: 2000,
        dailyAdds: 1000,
        automation: true,
        analytics: true,
        proxySupport: true,
        prioritySupport: true,
        support: 'chat'
      }
    },
    enterprise: {
      monthly: 99.99,
      yearly: 999.99,
      features: {
        maxAccounts: 50,
        dailyMessages: 10000,
        dailyAdds: 5000,
        automation: true,
        analytics: true,
        proxySupport: true,
        prioritySupport: true,
        customIntegrations: true,
        dedicatedSupport: true,
        support: 'phone'
      }
    }
  };
};

Payment.calculateAmount = function(plan, duration = 1) {
  const pricing = this.getSubscriptionPricing();
  if (!pricing[plan]) {
    throw new Error('Invalid subscription plan');
  }
  
  if (duration === 12) {
    return pricing[plan].yearly;
  } else {
    return pricing[plan].monthly * duration;
  }
};

Payment.getUserPaymentHistory = async function(userId, options = {}) {
  const { limit = 50, offset = 0, status } = options;
  
  const whereClause = { userId };
  if (status) {
    whereClause.status = status;
  }
  
  return await this.findAndCountAll({
    where: whereClause,
    limit,
    offset,
    order: [['createdAt', 'DESC']]
  });
};

Payment.getRevenueStats = async function(options = {}) {
  const { startDate, endDate } = options;
  
  const whereClause = { status: 'succeeded' };
  if (startDate && endDate) {
    whereClause.paidAt = {
      [sequelize.Op.between]: [startDate, endDate]
    };
  }
  
  const stats = await this.findAll({
    where: whereClause,
    attributes: [
      'subscriptionPlan',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.col('amount')), 'totalRevenue'],
      [sequelize.fn('AVG', sequelize.col('amount')), 'averageAmount']
    ],
    group: ['subscriptionPlan'],
    raw: true
  });
  
  return stats;
};

module.exports = Payment;
