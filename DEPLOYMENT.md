# Deployment Guide

This guide covers different deployment options for the Telegram Management System.

## Prerequisites

Before deploying, ensure you have:

1. **Telegram API Credentials**
   - Go to [my.telegram.org/apps](https://my.telegram.org/apps)
   - Create a new application
   - Note down your `api_id` and `api_hash`

2. **Server Requirements**
   - VPS with at least 1GB RAM
   - Node.js 16+ installed
   - Domain name (optional but recommended)
   - SSL certificate (for HTTPS)

## Option 1: Manual VPS Deployment

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx (optional)
sudo apt install nginx -y
```

### 2. Application Setup

```bash
# Clone repository
git clone <your-repo-url>
cd telegram-management-system

# Install dependencies
npm run install:all

# Configure environment
cd server
cp .env.example .env
nano .env  # Edit with your configuration
```

### 3. Environment Configuration

Edit `server/.env`:

```env
# Production settings
NODE_ENV=production
PORT=3001
JWT_SECRET=your-super-secure-random-string-here

# Telegram API (from my.telegram.org/apps)
TELEGRAM_API_ID=your-api-id
TELEGRAM_API_HASH=your-api-hash

# Database (SQLite for simplicity)
# No additional config needed for SQLite

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Frontend URL
CLIENT_URL=https://yourdomain.com
```

### 4. Build and Start

```bash
# Build frontend
cd client
npm run build

# Start with PM2
cd ../server
pm2 start app.js --name "telegram-management"
pm2 startup
pm2 save
```

### 5. Nginx Configuration (Optional)

Create `/etc/nginx/sites-available/telegram-management`:

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/telegram-management /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Option 2: Docker Deployment

### 1. Using Docker Compose

```bash
# Clone repository
git clone <your-repo-url>
cd telegram-management-system

# Create environment file
cp .env.example .env
nano .env  # Configure your settings

# Start with Docker Compose
docker-compose up -d
```

### 2. Environment Variables for Docker

Create `.env` file in project root:

```env
JWT_SECRET=your-super-secure-random-string
TELEGRAM_API_ID=your-api-id
TELEGRAM_API_HASH=your-api-hash
CLIENT_URL=https://yourdomain.com
```

### 3. SSL Setup for Docker

Place your SSL certificates in `ssl/` directory:
- `ssl/cert.pem`
- `ssl/key.pem`

## Option 3: Cloud Platform Deployment

### Heroku

1. **Prepare for Heroku:**
```bash
# Install Heroku CLI
# Create Procfile
echo "web: cd server && node app.js" > Procfile

# Create heroku app
heroku create your-app-name
```

2. **Configure Environment:**
```bash
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-secret
heroku config:set TELEGRAM_API_ID=your-api-id
heroku config:set TELEGRAM_API_HASH=your-api-hash
```

3. **Deploy:**
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### DigitalOcean App Platform

1. **Create app.yaml:**
```yaml
name: telegram-management
services:
- name: web
  source_dir: /
  github:
    repo: your-username/telegram-management-system
    branch: main
  run_command: cd server && node app.js
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: JWT_SECRET
    value: your-secret
  - key: TELEGRAM_API_ID
    value: your-api-id
  - key: TELEGRAM_API_HASH
    value: your-api-hash
```

## Security Considerations

### 1. Environment Variables
- Never commit `.env` files
- Use strong, random JWT secrets
- Rotate secrets regularly

### 2. Database Security
- Use PostgreSQL for production
- Enable SSL connections
- Regular backups

### 3. Network Security
- Use HTTPS only
- Configure firewall rules
- Enable rate limiting
- Use fail2ban for SSH protection

### 4. Application Security
- Keep dependencies updated
- Monitor for vulnerabilities
- Use security headers
- Implement proper logging

## Monitoring and Maintenance

### 1. PM2 Monitoring
```bash
# View logs
pm2 logs telegram-management

# Monitor resources
pm2 monit

# Restart application
pm2 restart telegram-management
```

### 2. Database Maintenance
```bash
# Backup SQLite database
cp server/database.sqlite backups/database-$(date +%Y%m%d).sqlite

# For PostgreSQL
pg_dump telegram_management > backup.sql
```

### 3. Log Rotation
```bash
# Configure logrotate
sudo nano /etc/logrotate.d/telegram-management
```

### 4. Updates
```bash
# Pull latest changes
git pull origin main

# Install new dependencies
npm run install:all

# Rebuild frontend
cd client && npm run build

# Restart application
pm2 restart telegram-management
```

## Troubleshooting

### Common Issues

1. **Port already in use:**
```bash
sudo lsof -i :3001
sudo kill -9 <PID>
```

2. **Permission denied:**
```bash
sudo chown -R $USER:$USER /path/to/app
```

3. **Database connection issues:**
- Check file permissions
- Verify database path
- Check disk space

4. **Telegram API errors:**
- Verify API credentials
- Check rate limits
- Ensure proper session handling

### Performance Optimization

1. **Enable Gzip compression**
2. **Use CDN for static assets**
3. **Implement caching**
4. **Database indexing**
5. **Connection pooling**

## Backup Strategy

### 1. Database Backups
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
cp /path/to/database.sqlite /backups/db_$DATE.sqlite
find /backups -name "db_*.sqlite" -mtime +7 -delete
```

### 2. Application Backups
```bash
# Backup uploads and sessions
tar -czf backup_$DATE.tar.gz uploads/ sessions/
```

### 3. Automated Backups
```bash
# Add to crontab
0 2 * * * /path/to/backup-script.sh
```

## Support

For deployment issues:
1. Check application logs
2. Verify environment configuration
3. Test API endpoints
4. Monitor system resources
5. Review security settings

Remember to test your deployment thoroughly before going live!
