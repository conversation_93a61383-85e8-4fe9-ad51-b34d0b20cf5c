const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { Payment, User } = require('../models');
const AnalyticsService = require('./analyticsService');

class PaymentService {
  // Create payment intent for subscription
  static async createPaymentIntent(userId, subscriptionPlan, duration = 1) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const amount = Payment.calculateAmount(subscriptionPlan, duration);
      
      // Create or get Stripe customer
      let stripeCustomerId = user.stripeCustomerId;
      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`.trim(),
          metadata: { userId: user.id }
        });
        stripeCustomerId = customer.id;
        await user.update({ stripeCustomerId });
      }

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'usd',
        customer: stripeCustomerId,
        metadata: {
          userId,
          subscriptionPlan,
          duration
        }
      });

      // Create payment record
      const payment = await Payment.create({
        userId,
        stripePaymentIntentId: paymentIntent.id,
        stripeCustomerId,
        amount,
        subscriptionPlan,
        subscriptionDuration: duration,
        status: 'pending'
      });

      return {
        clientSecret: paymentIntent.client_secret,
        paymentId: payment.id,
        amount
      };
    } catch (error) {
      console.error('Create payment intent error:', error);
      throw error;
    }
  }

  // Handle successful payment
  static async handleSuccessfulPayment(paymentIntentId) {
    try {
      const payment = await Payment.findOne({
        where: { stripePaymentIntentId: paymentIntentId },
        include: [{ model: User, as: 'user' }]
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      // Mark payment as successful
      await payment.markAsPaid();

      // Update user subscription
      const user = payment.user;
      const currentExpiry = user.subscriptionExpiry || new Date();
      const newExpiry = new Date(Math.max(currentExpiry.getTime(), Date.now()));
      newExpiry.setMonth(newExpiry.getMonth() + payment.subscriptionDuration);

      const subscriptionLimits = this.getSubscriptionLimits(payment.subscriptionPlan);
      
      await user.update({
        subscriptionPlan: payment.subscriptionPlan,
        subscriptionExpiry: newExpiry,
        dailyMessageLimit: subscriptionLimits.dailyMessages,
        dailyAddLimit: subscriptionLimits.dailyAdds,
        maxAccounts: subscriptionLimits.maxAccounts
      });

      // Log analytics event
      await AnalyticsService.logEvent({
        userId: user.id,
        eventType: 'payment_made',
        success: true,
        eventData: {
          amount: payment.amount,
          plan: payment.subscriptionPlan,
          duration: payment.subscriptionDuration
        }
      });

      return { payment, user };
    } catch (error) {
      console.error('Handle successful payment error:', error);
      throw error;
    }
  }

  // Handle failed payment
  static async handleFailedPayment(paymentIntentId, reason) {
    try {
      const payment = await Payment.findOne({
        where: { stripePaymentIntentId: paymentIntentId }
      });

      if (payment) {
        await payment.markAsFailed(reason);
        
        // Log analytics event
        await AnalyticsService.logEvent({
          userId: payment.userId,
          eventType: 'payment_made',
          success: false,
          errorMessage: reason,
          eventData: {
            amount: payment.amount,
            plan: payment.subscriptionPlan
          }
        });
      }

      return payment;
    } catch (error) {
      console.error('Handle failed payment error:', error);
      throw error;
    }
  }

  // Get subscription limits
  static getSubscriptionLimits(plan) {
    const pricing = Payment.getSubscriptionPricing();
    return pricing[plan]?.features || pricing.basic.features;
  }

  // Check if user subscription is active
  static async checkSubscriptionStatus(userId) {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const now = new Date();
    const isActive = user.subscriptionExpiry && user.subscriptionExpiry > now;
    
    if (!isActive && user.subscriptionPlan !== 'free') {
      // Downgrade to free plan
      await user.update({
        subscriptionPlan: 'free',
        dailyMessageLimit: 50,
        dailyAddLimit: 25,
        maxAccounts: 1
      });

      // Log analytics event
      await AnalyticsService.logEvent({
        userId,
        eventType: 'subscription_expired',
        success: true,
        eventData: { previousPlan: user.subscriptionPlan }
      });
    }

    return {
      isActive,
      plan: user.subscriptionPlan,
      expiry: user.subscriptionExpiry,
      limits: {
        dailyMessages: user.dailyMessageLimit,
        dailyAdds: user.dailyAddLimit,
        maxAccounts: user.maxAccounts
      }
    };
  }

  // Get user payment history
  static async getPaymentHistory(userId, options = {}) {
    return await Payment.getUserPaymentHistory(userId, options);
  }

  // Process refund
  static async processRefund(paymentId, amount, reason) {
    try {
      const payment = await Payment.findByPk(paymentId, {
        include: [{ model: User, as: 'user' }]
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'succeeded') {
        throw new Error('Cannot refund non-successful payment');
      }

      // Process Stripe refund
      const refund = await stripe.refunds.create({
        payment_intent: payment.stripePaymentIntentId,
        amount: amount ? Math.round(amount * 100) : undefined,
        reason: 'requested_by_customer'
      });

      // Update payment record
      await payment.processRefund(amount, reason);

      // If full refund, adjust user subscription
      if (!amount || amount >= payment.amount) {
        const user = payment.user;
        const refundMonths = payment.subscriptionDuration;
        const newExpiry = new Date(user.subscriptionExpiry);
        newExpiry.setMonth(newExpiry.getMonth() - refundMonths);

        if (newExpiry <= new Date()) {
          // Subscription expired, downgrade to free
          await user.update({
            subscriptionPlan: 'free',
            subscriptionExpiry: null,
            dailyMessageLimit: 50,
            dailyAddLimit: 25,
            maxAccounts: 1
          });
        } else {
          await user.update({ subscriptionExpiry: newExpiry });
        }
      }

      return { refund, payment };
    } catch (error) {
      console.error('Process refund error:', error);
      throw error;
    }
  }

  // Get revenue statistics (admin only)
  static async getRevenueStats(options = {}) {
    return await Payment.getRevenueStats(options);
  }

  // Handle Stripe webhook
  static async handleWebhook(event) {
    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handleSuccessfulPayment(event.data.object.id);
          break;
          
        case 'payment_intent.payment_failed':
          await this.handleFailedPayment(
            event.data.object.id,
            event.data.object.last_payment_error?.message || 'Payment failed'
          );
          break;
          
        case 'customer.subscription.deleted':
          // Handle subscription cancellation
          const customerId = event.data.object.customer;
          const user = await User.findOne({ where: { stripeCustomerId: customerId } });
          if (user) {
            await user.update({
              subscriptionPlan: 'free',
              subscriptionExpiry: null,
              dailyMessageLimit: 50,
              dailyAddLimit: 25,
              maxAccounts: 1
            });
          }
          break;
          
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      console.error('Webhook handling error:', error);
      throw error;
    }
  }

  // Create subscription plans in Stripe (setup function)
  static async createStripePlans() {
    const pricing = Payment.getSubscriptionPricing();
    
    for (const [planName, planData] of Object.entries(pricing)) {
      try {
        // Create monthly price
        await stripe.prices.create({
          unit_amount: Math.round(planData.monthly * 100),
          currency: 'usd',
          recurring: { interval: 'month' },
          product_data: {
            name: `${planName.charAt(0).toUpperCase() + planName.slice(1)} Plan`,
            description: `Monthly subscription for ${planName} plan`
          },
          metadata: { plan: planName, interval: 'monthly' }
        });

        // Create yearly price
        await stripe.prices.create({
          unit_amount: Math.round(planData.yearly * 100),
          currency: 'usd',
          recurring: { interval: 'year' },
          product_data: {
            name: `${planName.charAt(0).toUpperCase() + planName.slice(1)} Plan (Yearly)`,
            description: `Yearly subscription for ${planName} plan`
          },
          metadata: { plan: planName, interval: 'yearly' }
        });
      } catch (error) {
        console.error(`Error creating ${planName} plan:`, error);
      }
    }
  }
}

module.exports = PaymentService;
