# Complete Testing Guide - Telegram Management System

## 🧪 **How to Test All Features**

### **Prerequisites**
1. ✅ System is running on http://localhost:3000
2. ✅ Backend API is running on http://localhost:3001
3. ✅ You have Telegram API credentials (api_id and api_hash)
4. ✅ You have at least one Telegram account for testing

### **Step 1: Initial Setup**

#### **1.1 Configure Environment**
```bash
# Edit server/.env file
TELEGRAM_API_ID=your-api-id
TELEGRAM_API_HASH=your-api-hash
JWT_SECRET=your-secure-secret
```

#### **1.2 Start the System**
```bash
npm run dev
```

#### **1.3 Access the Application**
- Open http://localhost:3000
- You should see the login page

### **Step 2: User Authentication Testing**

#### **2.1 Register New Account**
1. Click "Sign up"
2. Fill in registration form:
   - Username: `testuser`
   - Email: `<EMAIL>`
   - Password: `password123`
   - First Name: `Test`
   - Last Name: `User`
3. Click "Sign up"
4. ✅ Should automatically log you in

#### **2.2 Test Login/Logout**
1. Logout from the system
2. Login with credentials
3. ✅ Should redirect to dashboard

### **Step 3: Dashboard Testing**

#### **3.1 View Dashboard**
1. Navigate to Dashboard
2. ✅ Should show welcome message
3. ✅ Should display statistics (all zeros initially)
4. ✅ Should show subscription plan info

### **Step 4: Telegram Account Management**

#### **4.1 Add Telegram Account**
1. Go to "Telegram Accounts" page
2. Click "Add Account"
3. Enter your phone number (with country code): `+**********`
4. Enter account name: `My Test Account`
5. Click "Add Account"
6. ✅ Should show "Verification code sent" message

#### **4.2 Verify Account**
1. Check your phone for Telegram verification code
2. Enter the code in the popup
3. If you have 2FA, enter your password
4. Click "Verify"
5. ✅ Account should show as "Active"

#### **4.3 Test Account Status**
1. ✅ Account should appear in the list
2. ✅ Should show account details (name, phone, status)
3. ✅ Should show daily counters (messages, members added)

### **Step 5: Member Scraping Testing**

#### **5.1 Scrape Members from Public Group**
1. Go to "Members" page
2. Click "Scrape Members"
3. Select your Telegram account
4. Enter a public group username: `@telegram` (official Telegram channel)
5. Set limit: `100`
6. Click "Start Scraping"
7. ✅ Should show success message with scraped count

#### **5.2 View Scraped Members**
1. ✅ Members should appear in the table
2. ✅ Should show member details (name, username, source group)
3. Test search functionality
4. Test filtering by source group

#### **5.3 Export Members**
1. Click "Export" button
2. ✅ Should download CSV file with member data

### **Step 6: Messaging Testing**

#### **6.1 Send Single Message**
1. Go to "Messages" page
2. Select "Single Message" tab
3. Select your Telegram account
4. Target type: `user`
5. Target ID: Your own username or phone number
6. Message: `Hello, this is a test message!`
7. Click "Send Message"
8. ✅ Should receive the message on your Telegram

#### **6.2 Test Bulk Messaging**
1. Select "Bulk Messages" tab
2. Select your Telegram account
3. Message: `Hello {firstName}, this is a bulk test!`
4. Select some scraped members
5. Set delay: `5` seconds
6. Click "Send to X Members"
7. ✅ Should show progress and success count

### **Step 7: Automation Testing**

#### **7.1 Create Auto-Join Task**
1. Go to "Automation" page
2. Click "Create Task"
3. Select your Telegram account
4. Task Type: "Auto Join Group"
5. Schedule: Leave empty for immediate
6. Priority: `5`
7. Click "Create Task"
8. ✅ Task should appear in the list

#### **7.2 Test Scheduled Message**
1. Create new task
2. Task Type: "Scheduled Message"
3. Schedule: Set time 2 minutes from now
4. Click "Create Task"
5. ✅ Task should execute at scheduled time

#### **7.3 Test Task Management**
1. ✅ View task status in real-time
2. Test pause/resume functionality
3. Test task cancellation
4. Filter tasks by status and type

### **Step 8: Tools Testing**

#### **8.1 Username Checker**
1. Go to "Tools" page
2. Select "Username Checker" tab
3. Enter username: `@testusername123`
4. Select your Telegram account
5. Click "Check Username"
6. ✅ Should show availability status

#### **8.2 Phone Formatter**
1. Select "Phone Formatter" tab
2. Enter phone: `**********`
3. Country code: `US`
4. Click "Format Phone"
5. ✅ Should show formatted versions

#### **8.3 Message Templates**
1. Select "Message Templates" tab
2. Create new template:
   - Name: `Welcome Message`
   - Content: `Welcome {firstName} to our group!`
   - Category: `Welcome`
3. Click "Create Template"
4. ✅ Template should appear in saved list

#### **8.4 Proxy Manager**
1. Select "Proxy Manager" tab
2. Add proxy configuration:
   - Name: `Test Proxy`
   - Host: `proxy.example.com`
   - Port: `8080`
   - Type: `HTTP`
3. Click "Add Proxy"
4. Click "Test" to test connection
5. ✅ Should show test results

### **Step 9: Settings Testing**

#### **9.1 Profile Settings**
1. Go to "Settings" page
2. Update profile information
3. Click "Update Profile"
4. ✅ Should save successfully

#### **9.2 Password Change**
1. Select "Security" tab
2. Enter current password
3. Enter new password
4. Confirm new password
5. Click "Change Password"
6. ✅ Should update successfully

#### **9.3 Subscription Info**
1. Select "Subscription" tab
2. ✅ Should show current plan details
3. ✅ Should show usage limits

### **Step 10: Advanced Feature Testing**

#### **10.1 Real-time Updates**
1. Open two browser windows
2. Perform actions in one window
3. ✅ Should see updates in both windows

#### **10.2 Rate Limiting**
1. Try to make many rapid requests
2. ✅ Should be rate limited appropriately

#### **10.3 Error Handling**
1. Try invalid operations
2. ✅ Should show appropriate error messages
3. ✅ Should not crash the system

### **Step 11: Performance Testing**

#### **11.1 Large Data Sets**
1. Scrape large number of members (1000+)
2. ✅ Should handle efficiently
3. Test pagination and filtering

#### **11.2 Concurrent Operations**
1. Run multiple tasks simultaneously
2. ✅ Should process in queue
3. ✅ Should maintain system stability

### **Step 12: Security Testing**

#### **12.1 Authentication**
1. Try accessing protected routes without login
2. ✅ Should redirect to login
3. Try with invalid tokens
4. ✅ Should reject access

#### **12.2 Input Validation**
1. Try SQL injection attempts
2. Try XSS attempts
3. ✅ Should be properly sanitized

### **🎯 Expected Results**

After completing all tests, you should have:

✅ **Working user authentication system**
✅ **Functional Telegram account management**
✅ **Successful member scraping from groups**
✅ **Working messaging system (single and bulk)**
✅ **Functional automation tasks**
✅ **Working professional tools**
✅ **Proper settings management**
✅ **Real-time updates and notifications**
✅ **Proper error handling and security**

### **🐛 Troubleshooting**

#### **Common Issues:**

1. **"Failed to send verification code"**
   - Check Telegram API credentials
   - Verify phone number format
   - Ensure account isn't already registered

2. **"Database connection failed"**
   - Check if SQLite file has write permissions
   - Restart the server

3. **"Rate limit exceeded"**
   - Wait for rate limit to reset
   - Check your subscription limits

4. **"Username check failed"**
   - Ensure account is verified and active
   - Check internet connection

### **🚀 Performance Benchmarks**

Expected performance metrics:
- **Member scraping**: 100-1000 members/minute
- **Message sending**: 10-50 messages/minute (with delays)
- **Username checking**: 5-10 checks/minute
- **Database operations**: <100ms response time
- **API endpoints**: <500ms response time

### **✅ Success Criteria**

The system passes testing if:
1. All core features work without errors
2. Real-time updates function properly
3. Security measures are effective
4. Performance meets benchmarks
5. UI is responsive and intuitive
6. Error handling is graceful
7. Data persistence works correctly

**🎉 If all tests pass, your Telegram Management System is fully functional and ready for production use!**
