{"name": "telegram-management-system", "version": "1.0.0", "description": "Comprehensive Telegram management platform for multi-account automation", "main": "server/app.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["telegram", "automation", "scraping", "bulk-messaging"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"archiver": "^7.0.1", "axios": "^1.10.0", "form-data": "^4.0.3"}}