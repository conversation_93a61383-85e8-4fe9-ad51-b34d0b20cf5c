import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, ExternalLink, Key, Shield, Zap, Users } from 'lucide-react';

const ApiSetup = ({ user, onUpdate, onClose }) => {
  const [apiId, setApiId] = useState('');
  const [apiHash, setApiHash] = useState('');
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (user?.telegramApiId) {
      setApiId(user.telegramApiId);
    }
  }, [user]);

  const handleSave = async () => {
    if (!apiId || !apiHash) {
      setError('Both API ID and API Hash are required');
      return;
    }

    if (apiHash.length !== 32) {
      setError('API Hash must be exactly 32 characters long');
      return;
    }

    if (!/^\d+$/.test(apiId)) {
      setError('API ID must be a number');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/users/api-credentials', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          telegramApiId: apiId,
          telegramApiHash: apiHash
        })
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('API credentials saved successfully!');
        if (onUpdate) {
          onUpdate(data.user);
        }
      } else {
        setError(data.error || 'Failed to save API credentials');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    if (!apiId || !apiHash) {
      setError('Please enter both API ID and API Hash first');
      return;
    }

    setTesting(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/users/test-api-credentials', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          telegramApiId: apiId,
          telegramApiHash: apiHash
        })
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('✅ API credentials are valid and working!');
      } else {
        setError(data.error || 'API credentials test failed');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Key className="h-8 w-8 text-blue-600" />
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Telegram API Setup</h2>
                <p className="text-gray-600">Configure your personal Telegram API credentials</p>
              </div>
            </div>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Benefits Section */}
          <div className="grid md:grid-cols-3 gap-4">
            <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
              <Shield className="h-6 w-6 text-blue-600 mt-1" />
              <div>
                <h3 className="font-semibold text-blue-900">Your Own Access</h3>
                <p className="text-sm text-blue-700">No sharing, maximum security</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
              <Zap className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <h3 className="font-semibold text-green-900">Unlimited Speed</h3>
                <p className="text-sm text-green-700">Direct connection to Telegram</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-4 bg-purple-50 rounded-lg">
              <Users className="h-6 w-6 text-purple-600 mt-1" />
              <div>
                <h3 className="font-semibold text-purple-900">No Limits</h3>
                <p className="text-sm text-purple-700">Your own API quota</p>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">How to Get Your API Credentials</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</span>
                <div>
                  <p className="font-medium">Visit Telegram's Developer Portal</p>
                  <a 
                    href="https://my.telegram.org/apps" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                  >
                    <span>https://my.telegram.org/apps</span>
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">2</span>
                <p>Login with your Telegram phone number</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">3</span>
                <p>Click "Create new application" and fill in the form</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">4</span>
                <p>Copy your <strong>API ID</strong> and <strong>API Hash</strong></p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API ID
              </label>
              <input
                type="text"
                value={apiId}
                onChange={(e) => setApiId(e.target.value)}
                placeholder="e.g., 12345678"
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-sm text-gray-500 mt-1">A number you get from Telegram</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API Hash
              </label>
              <input
                type="password"
                value={apiHash}
                onChange={(e) => setApiHash(e.target.value)}
                placeholder="32-character string from Telegram"
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-sm text-gray-500 mt-1">A 32-character string you get from Telegram</p>
            </div>
          </div>

          {/* Status Messages */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {success && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <p className="text-green-700">{success}</p>
            </div>
          )}

          {/* Current Status */}
          {user?.apiCredentialsVerified && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">API Credentials Configured</span>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                Last verified: {new Date(user.apiCredentialsVerifiedAt).toLocaleString()}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={handleTest}
              disabled={testing || !apiId || !apiHash}
              className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {testing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Testing...
                </>
              ) : (
                'Test Credentials'
              )}
            </button>
            <button
              onClick={handleSave}
              disabled={loading || !apiId || !apiHash}
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                'Save Credentials'
              )}
            </button>
          </div>

          {/* Security Note */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-900">Security Note</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Your API credentials are stored securely and encrypted. They are only used to connect to Telegram on your behalf.
                  You can revoke access anytime by deleting the application from Telegram's developer portal.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiSetup;
