const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Configuration
const API_BASE_URL = 'http://localhost:3002/api';
const SESSION_FOLDER = './+855715258054';

// Session file paths
const JSON_FILE = path.join(SESSION_FOLDER, '+855715258054.json');
const SESSION_FILE = path.join(SESSION_FOLDER, '+855715258054.session');
const TDATA_FOLDER = path.join(SESSION_FOLDER, 'tdata');

// Test user credentials (auto-generated for quick testing)
const USER_CREDENTIALS = {
  email: 'user' + Date.now() + '@example.com',
  password: 'password123',
  username: 'user' + Date.now(),
  firstName: 'Test',
  lastName: 'User'
};

async function loginUser() {
  try {
    console.log('🔐 Logging in user...');
    const response = await axios.post(`${API_BASE_URL}/auth/login`, USER_CREDENTIALS);
    
    if (response.data.token) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      throw new Error('No token received');
    }
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    
    // Try to register if login fails
    console.log('🔄 Attempting to register new user...');
    try {
      const registerResponse = await axios.post(`${API_BASE_URL}/auth/register`, USER_CREDENTIALS);
      
      if (registerResponse.data.token) {
        console.log('✅ Registration successful');
        return registerResponse.data.token;
      }
    } catch (regError) {
      console.error('❌ Registration failed:', regError.response?.data?.message || regError.message);
    }
    
    throw error;
  }
}

async function setUserApiCredentials(token) {
  try {
    console.log('🔑 Setting up API credentials...');
    
    const response = await axios.post(`${API_BASE_URL}/users/api-credentials`, {
      telegramApiId: '2040',
      telegramApiHash: 'b18441a1ff607e10a989891a5462e627'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ API credentials set successfully');
    return response.data;
  } catch (error) {
    console.error('❌ Failed to set API credentials:', error.response?.data?.error || error.message);
    throw error;
  }
}

async function importJSONSession(token) {
  try {
    console.log('📄 Importing JSON session...');
    
    if (!fs.existsSync(JSON_FILE)) {
      throw new Error('JSON file not found');
    }
    
    const jsonData = JSON.parse(fs.readFileSync(JSON_FILE, 'utf8'));
    
    // Prepare session data for import
    const sessionData = {
      session_string: jsonData.session_file || jsonData.phone,
      api_id: jsonData.app_id,
      api_hash: jsonData.app_hash,
      phone: jsonData.phone,
      first_name: jsonData.first_name,
      last_name: jsonData.last_name,
      username: jsonData.username,
      device: jsonData.device,
      app_version: jsonData.app_version,
      sdk: jsonData.sdk,
      lang_code: jsonData.lang_code,
      system_lang_code: jsonData.system_lang_code
    };
    
    const response = await axios.post(`${API_BASE_URL}/session-import/json`, {
      sessionData: sessionData,
      accountName: `${jsonData.first_name || 'Account'} (JSON Import)`
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log('✅ JSON session imported successfully!');
      console.log(`   Account ID: ${response.data.account.id}`);
      console.log(`   Account Name: ${response.data.account.accountName}`);
      return response.data.account;
    } else {
      throw new Error(response.data.message || 'JSON import failed');
    }
    
  } catch (error) {
    console.error('❌ JSON import failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function importSessionFile(token) {
  try {
    console.log('🔑 Importing .session file...');
    
    if (!fs.existsSync(SESSION_FILE)) {
      throw new Error('Session file not found');
    }
    
    // Read session file as base64
    const sessionBuffer = fs.readFileSync(SESSION_FILE);
    const sessionString = sessionBuffer.toString('base64');
    
    const response = await axios.post(`${API_BASE_URL}/session-import/session-string`, {
      sessionString: sessionString,
      accountName: 'Account (Session File Import)'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log('✅ Session file imported successfully!');
      console.log(`   Account ID: ${response.data.account.id}`);
      console.log(`   Account Name: ${response.data.account.accountName}`);
      return response.data.account;
    } else {
      throw new Error(response.data.message || 'Session file import failed');
    }
    
  } catch (error) {
    console.error('❌ Session file import failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function importTDataFolder(token) {
  try {
    console.log('📁 Importing TData folder...');
    
    if (!fs.existsSync(TDATA_FOLDER)) {
      throw new Error('TData folder not found');
    }
    
    // Create a zip file of the tdata folder
    const archiver = require('archiver');
    const zipPath = path.join(__dirname, 'tdata_temp.zip');
    
    await new Promise((resolve, reject) => {
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', { zlib: { level: 9 } });
      
      output.on('close', resolve);
      archive.on('error', reject);
      
      archive.pipe(output);
      archive.directory(TDATA_FOLDER, 'tdata');
      archive.finalize();
    });
    
    // Upload the zip file
    const formData = new FormData();
    formData.append('sessions', fs.createReadStream(zipPath), 'tdata.zip');
    formData.append('accountName', 'Account (TData Import)');
    
    const response = await axios.post(`${API_BASE_URL}/session-import/upload`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      }
    });
    
    // Clean up temp file
    fs.unlinkSync(zipPath);
    
    if (response.data.success && response.data.results.length > 0) {
      const result = response.data.results[0];
      if (result.success) {
        console.log('✅ TData imported successfully!');
        console.log(`   Account ID: ${result.accountId}`);
        console.log(`   Account Name: ${result.accountName}`);
        return { id: result.accountId, accountName: result.accountName };
      } else {
        throw new Error(result.error || 'TData import failed');
      }
    } else {
      throw new Error('TData import failed');
    }
    
  } catch (error) {
    console.error('❌ TData import failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function verifyAccount(token, accountId) {
  try {
    console.log('🔍 Verifying account status...');
    
    const response = await axios.get(`${API_BASE_URL}/telegram/accounts/${accountId}/status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`📊 Account status: ${response.data.status}`);
    return response.data;
    
  } catch (error) {
    console.error('⚠️ Account verification failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Telegram session import process...\n');
    
    // Check if session files exist
    console.log('📋 Checking available session files:');
    console.log(`   JSON file: ${fs.existsSync(JSON_FILE) ? '✅' : '❌'} ${JSON_FILE}`);
    console.log(`   Session file: ${fs.existsSync(SESSION_FILE) ? '✅' : '❌'} ${SESSION_FILE}`);
    console.log(`   TData folder: ${fs.existsSync(TDATA_FOLDER) ? '✅' : '❌'} ${TDATA_FOLDER}\n`);
    
    // Step 1: Login/Register
    const token = await loginUser();
    
    // Step 2: Set API credentials
    await setUserApiCredentials(token);
    
    // Step 3: Try importing (in order of preference)
    let account = null;
    
    // Try JSON import first (most reliable)
    if (fs.existsSync(JSON_FILE)) {
      account = await importJSONSession(token);
    }
    
    // If JSON failed, try session file
    if (!account && fs.existsSync(SESSION_FILE)) {
      account = await importSessionFile(token);
    }
    
    // If both failed, try TData
    if (!account && fs.existsSync(TDATA_FOLDER)) {
      account = await importTDataFolder(token);
    }
    
    if (account) {
      // Step 4: Verify the account
      await verifyAccount(token, account.id);
      
      console.log('\n🎉 Import process completed successfully!');
      console.log('\n📝 Next steps:');
      console.log('1. Open your web application at http://localhost:3000');
      console.log('2. Go to "Telegram Accounts" section');
      console.log('3. Your imported account should be visible and ready to use');
      console.log('4. You can now use features like member scraping, messaging, etc.');
    } else {
      console.log('\n❌ All import methods failed. Please check your session files.');
    }
    
  } catch (error) {
    console.error('\n💥 Import process failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the server is running (npm run dev)');
    console.log('2. Update USER_CREDENTIALS in this script');
    console.log('3. Ensure session files exist and are valid');
    console.log('4. Check server logs for detailed error messages');
  }
}

// Install required dependencies if not present
try {
  require('form-data');
  require('archiver');
} catch (e) {
  console.log('📦 Installing required dependencies...');
  console.log('Run: npm install form-data archiver');
  process.exit(1);
}

// Run the import
main();
