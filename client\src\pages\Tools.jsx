import React, { useState, useEffect } from 'react';
import api from '../services/api';
import toast from 'react-hot-toast';
import {
  MagnifyingGlassIcon,
  PhoneIcon,
  DocumentTextIcon,
  ServerIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

const Tools = () => {
  const [activeTab, setActiveTab] = useState('username');
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);

  // Username checker state
  const [usernameData, setUsernameData] = useState({
    username: '',
    telegramAccountId: '',
    results: []
  });

  // Phone formatter state
  const [phoneData, setPhoneData] = useState({
    phoneNumber: '',
    countryCode: '',
    results: []
  });

  // Message templates state
  const [templates, setTemplates] = useState([]);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    content: '',
    category: 'custom',
    variables: []
  });

  // Proxy state
  const [proxies, setProxies] = useState([]);
  const [newProxy, setNewProxy] = useState({
    name: '',
    host: '',
    port: '',
    username: '',
    password: '',
    proxyType: 'http',
    country: ''
  });

  useEffect(() => {
    fetchAccounts();
    if (activeTab === 'templates') {
      fetchTemplates();
    } else if (activeTab === 'proxies') {
      fetchProxies();
    }
  }, [activeTab]);

  const fetchAccounts = async () => {
    try {
      const response = await api.getTelegramAccounts();
      setAccounts(response.data.accounts.filter(acc => acc.isActive));
    } catch (error) {
      console.error('Failed to fetch accounts:', error);
    }
  };

  const fetchTemplates = async () => {
    try {
      const response = await api.get('/tools/message-templates');
      setTemplates(response.data.templates);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    }
  };

  const fetchProxies = async () => {
    try {
      const response = await api.get('/tools/proxies');
      setProxies(response.data.proxies);
    } catch (error) {
      console.error('Failed to fetch proxies:', error);
    }
  };

  const handleUsernameCheck = async (e) => {
    e.preventDefault();
    if (!usernameData.username || !usernameData.telegramAccountId) {
      toast.error('Please enter username and select account');
      return;
    }

    setLoading(true);
    try {
      const response = await api.post('/tools/check-username', {
        username: usernameData.username,
        telegramAccountId: usernameData.telegramAccountId
      });

      setUsernameData(prev => ({
        ...prev,
        results: [response.data, ...prev.results.slice(0, 9)]
      }));

      toast.success('Username checked successfully');
    } catch (error) {
      console.error('Username check failed:', error);
      toast.error(error.response?.data?.error || 'Username check failed');
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneFormat = async (e) => {
    e.preventDefault();
    if (!phoneData.phoneNumber) {
      toast.error('Please enter a phone number');
      return;
    }

    setLoading(true);
    try {
      const response = await api.post('/tools/format-phone', {
        phoneNumber: phoneData.phoneNumber,
        countryCode: phoneData.countryCode
      });

      setPhoneData(prev => ({
        ...prev,
        results: [response.data, ...prev.results.slice(0, 9)]
      }));

      toast.success('Phone number formatted successfully');
    } catch (error) {
      console.error('Phone format failed:', error);
      toast.error('Phone format failed');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = async (e) => {
    e.preventDefault();
    if (!newTemplate.name || !newTemplate.content) {
      toast.error('Please enter template name and content');
      return;
    }

    try {
      await api.post('/tools/message-templates', newTemplate);
      toast.success('Template created successfully');
      setNewTemplate({
        name: '',
        content: '',
        category: 'custom',
        variables: []
      });
      fetchTemplates();
    } catch (error) {
      console.error('Failed to create template:', error);
      toast.error('Failed to create template');
    }
  };

  const handleDeleteTemplate = async (templateId) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    try {
      await api.delete(`/tools/message-templates/${templateId}`);
      toast.success('Template deleted successfully');
      fetchTemplates();
    } catch (error) {
      console.error('Failed to delete template:', error);
      toast.error('Failed to delete template');
    }
  };

  const handleCreateProxy = async (e) => {
    e.preventDefault();
    if (!newProxy.name || !newProxy.host || !newProxy.port) {
      toast.error('Please enter proxy name, host, and port');
      return;
    }

    try {
      await api.post('/tools/proxies', newProxy);
      toast.success('Proxy created successfully');
      setNewProxy({
        name: '',
        host: '',
        port: '',
        username: '',
        password: '',
        proxyType: 'http',
        country: ''
      });
      fetchProxies();
    } catch (error) {
      console.error('Failed to create proxy:', error);
      toast.error('Failed to create proxy');
    }
  };

  const handleTestProxy = async (proxyId) => {
    try {
      setLoading(true);
      const response = await api.post(`/tools/proxies/${proxyId}/test`);
      
      if (response.data.success) {
        toast.success(`Proxy test successful (${response.data.responseTime}ms)`);
      } else {
        toast.error(`Proxy test failed: ${response.data.error}`);
      }
      
      fetchProxies();
    } catch (error) {
      console.error('Failed to test proxy:', error);
      toast.error('Failed to test proxy');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProxy = async (proxyId) => {
    if (!confirm('Are you sure you want to delete this proxy?')) return;

    try {
      await api.delete(`/tools/proxies/${proxyId}`);
      toast.success('Proxy deleted successfully');
      fetchProxies();
    } catch (error) {
      console.error('Failed to delete proxy:', error);
      toast.error('Failed to delete proxy');
    }
  };

  const tabs = [
    { id: 'username', name: 'Username Checker', icon: MagnifyingGlassIcon },
    { id: 'phone', name: 'Phone Formatter', icon: PhoneIcon },
    { id: 'templates', name: 'Message Templates', icon: DocumentTextIcon },
    { id: 'proxies', name: 'Proxy Manager', icon: ServerIcon }
  ];

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Tools</h1>
        <p className="mt-1 text-sm text-gray-600">
          Professional tools for Telegram automation
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-5 w-5 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Username Checker Tab */}
      {activeTab === 'username' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Check Username Availability</h3>
            <form onSubmit={handleUsernameCheck} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Username</label>
                  <input
                    type="text"
                    required
                    className="mt-1 input-field"
                    placeholder="@username or username"
                    value={usernameData.username}
                    onChange={(e) => setUsernameData({...usernameData, username: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Telegram Account</label>
                  <select
                    required
                    className="mt-1 input-field"
                    value={usernameData.telegramAccountId}
                    onChange={(e) => setUsernameData({...usernameData, telegramAccountId: e.target.value})}
                  >
                    <option value="">Select account</option>
                    {accounts.map(account => (
                      <option key={account.id} value={account.id}>
                        {account.accountName} ({account.phoneNumber})
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <button
                type="submit"
                disabled={loading}
                className="btn-primary flex items-center"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                ) : (
                  <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
                )}
                Check Username
              </button>
            </form>
          </div>

          {/* Username Results */}
          {usernameData.results.length > 0 && (
            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Checks</h3>
              <div className="space-y-3">
                {usernameData.results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">@{result.username}</div>
                      <div className="text-sm text-gray-500">
                        {result.entityType && `Type: ${result.entityType}`}
                        {result.entityInfo?.title && ` - ${result.entityInfo.title}`}
                      </div>
                    </div>
                    <div className="flex items-center">
                      {result.isAvailable ? (
                        <CheckCircleIcon className="h-6 w-6 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-6 w-6 text-red-500" />
                      )}
                      <span className={`ml-2 text-sm font-medium ${
                        result.isAvailable ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {result.isAvailable ? 'Available' : 'Taken'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Phone Formatter Tab */}
      {activeTab === 'phone' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Format Phone Number</h3>
            <form onSubmit={handlePhoneFormat} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <input
                    type="text"
                    required
                    className="mt-1 input-field"
                    placeholder="+1234567890 or 1234567890"
                    value={phoneData.phoneNumber}
                    onChange={(e) => setPhoneData({...phoneData, phoneNumber: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Country Code (Optional)</label>
                  <input
                    type="text"
                    className="mt-1 input-field"
                    placeholder="US, GB, etc."
                    value={phoneData.countryCode}
                    onChange={(e) => setPhoneData({...phoneData, countryCode: e.target.value})}
                  />
                </div>
              </div>
              <button
                type="submit"
                disabled={loading}
                className="btn-primary flex items-center"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                ) : (
                  <PhoneIcon className="h-5 w-5 mr-2" />
                )}
                Format Phone
              </button>
            </form>
          </div>

          {/* Phone Results */}
          {phoneData.results.length > 0 && (
            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Formats</h3>
              <div className="space-y-3">
                {phoneData.results.map((result, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Original:</span> {result.original}
                      </div>
                      <div>
                        <span className="font-medium">Formatted:</span> {result.formatted}
                      </div>
                      <div>
                        <span className="font-medium">Telegram:</span> {result.telegramFormat}
                      </div>
                      <div>
                        <span className="font-medium">Valid:</span> 
                        <span className={result.isValid ? 'text-green-600' : 'text-red-600'}>
                          {result.isValid ? ' Yes' : ' No'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Message Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Create Message Template</h3>
            <form onSubmit={handleCreateTemplate} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Template Name</label>
                  <input
                    type="text"
                    required
                    className="mt-1 input-field"
                    placeholder="Welcome Message"
                    value={newTemplate.name}
                    onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Category</label>
                  <select
                    className="mt-1 input-field"
                    value={newTemplate.category}
                    onChange={(e) => setNewTemplate({...newTemplate, category: e.target.value})}
                  >
                    <option value="custom">Custom</option>
                    <option value="welcome">Welcome</option>
                    <option value="promotion">Promotion</option>
                    <option value="notification">Notification</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Message Content</label>
                <textarea
                  required
                  rows={4}
                  className="mt-1 input-field"
                  placeholder="Hello {firstName}, welcome to our group!"
                  value={newTemplate.content}
                  onChange={(e) => setNewTemplate({...newTemplate, content: e.target.value})}
                />
                <p className="mt-1 text-xs text-gray-500">
                  Use variables: {'{firstName}'}, {'{lastName}'}, {'{username}'}
                </p>
              </div>
              <button type="submit" className="btn-primary">
                Create Template
              </button>
            </form>
          </div>

          {/* Templates List */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Saved Templates</h3>
            {templates.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No templates created yet</p>
            ) : (
              <div className="space-y-3">
                {templates.map((template) => (
                  <div key={template.id} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{template.name}</h4>
                      <div className="flex items-center space-x-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                          {template.category}
                        </span>
                        <button
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="text-red-600 hover:text-red-900 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm">{template.content}</p>
                    <div className="mt-2 text-xs text-gray-500">
                      Used {template.usageCount} times
                      {template.lastUsed && ` • Last used: ${new Date(template.lastUsed).toLocaleDateString()}`}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Proxy Manager Tab */}
      {activeTab === 'proxies' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add Proxy Configuration</h3>
            <form onSubmit={handleCreateProxy} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Proxy Name</label>
                  <input
                    type="text"
                    required
                    className="mt-1 input-field"
                    placeholder="My Proxy"
                    value={newProxy.name}
                    onChange={(e) => setNewProxy({...newProxy, name: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Host</label>
                  <input
                    type="text"
                    required
                    className="mt-1 input-field"
                    placeholder="proxy.example.com"
                    value={newProxy.host}
                    onChange={(e) => setNewProxy({...newProxy, host: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Port</label>
                  <input
                    type="number"
                    required
                    className="mt-1 input-field"
                    placeholder="8080"
                    value={newProxy.port}
                    onChange={(e) => setNewProxy({...newProxy, port: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Username (Optional)</label>
                  <input
                    type="text"
                    className="mt-1 input-field"
                    value={newProxy.username}
                    onChange={(e) => setNewProxy({...newProxy, username: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Password (Optional)</label>
                  <input
                    type="password"
                    className="mt-1 input-field"
                    value={newProxy.password}
                    onChange={(e) => setNewProxy({...newProxy, password: e.target.value})}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Type</label>
                  <select
                    className="mt-1 input-field"
                    value={newProxy.proxyType}
                    onChange={(e) => setNewProxy({...newProxy, proxyType: e.target.value})}
                  >
                    <option value="http">HTTP</option>
                    <option value="https">HTTPS</option>
                    <option value="socks4">SOCKS4</option>
                    <option value="socks5">SOCKS5</option>
                  </select>
                </div>
              </div>
              <button type="submit" className="btn-primary">
                Add Proxy
              </button>
            </form>
          </div>

          {/* Proxies List */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Proxy Configurations</h3>
            {proxies.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No proxies configured yet</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Address
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {proxies.map((proxy) => (
                      <tr key={proxy.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{proxy.name}</div>
                          <div className="text-sm text-gray-500">{proxy.proxyType.toUpperCase()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {proxy.host}:{proxy.port}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            Success Rate: {proxy.successRate.toFixed(1)}%
                          </div>
                          {proxy.responseTime && (
                            <div className="text-sm text-gray-500">
                              Response: {proxy.responseTime}ms
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleTestProxy(proxy.id)}
                              disabled={loading}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Test
                            </button>
                            <button
                              onClick={() => handleDeleteProxy(proxy.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Tools;
