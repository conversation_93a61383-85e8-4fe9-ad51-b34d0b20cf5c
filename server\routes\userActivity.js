const express = require('express');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { UserActivity, TelegramAccount, ScrapedMember } = require('../models');
const userActivityService = require('../services/userActivityService');
const { Op } = require('sequelize');

const router = express.Router();

// Start tracking user activity in a group
router.post('/track/start', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { telegramAccountId, groupId, options } = req.body;

    if (!telegramAccountId || !groupId) {
      return res.status(400).json({ error: 'Account ID and group ID are required' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const result = await userActivityService.startTracking(telegramAccountId, groupId, options);
    
    if (result.success) {
      res.json({ message: result.message, details: result });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Start activity tracking error:', error);
    res.status(500).json({ error: 'Failed to start activity tracking' });
  }
});

// Stop tracking user activity
router.post('/track/stop', authenticateToken, async (req, res) => {
  try {
    const { telegramAccountId, groupId } = req.body;

    if (!telegramAccountId || !groupId) {
      return res.status(400).json({ error: 'Account ID and group ID are required' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const result = await userActivityService.stopTracking(telegramAccountId, groupId);
    
    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Stop activity tracking error:', error);
    res.status(500).json({ error: 'Failed to stop activity tracking' });
  }
});

// Get tracking status
router.get('/track/status', authenticateToken, async (req, res) => {
  try {
    const { telegramAccountId, groupId } = req.query;

    if (!telegramAccountId || !groupId) {
      return res.status(400).json({ error: 'Account ID and group ID are required' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const status = userActivityService.getTrackingStatus(telegramAccountId, groupId);
    res.json(status);
  } catch (error) {
    console.error('Get tracking status error:', error);
    res.status(500).json({ error: 'Failed to get tracking status' });
  }
});

// Calculate engagement scores
router.post('/engagement/calculate', authenticateToken, async (req, res) => {
  try {
    const { groupId } = req.body;

    if (!groupId) {
      return res.status(400).json({ error: 'Group ID is required' });
    }

    const result = await userActivityService.calculateEngagementScores(req.user.id, groupId);
    
    if (result.success) {
      res.json({ message: result.message, details: result });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Calculate engagement scores error:', error);
    res.status(500).json({ error: 'Failed to calculate engagement scores' });
  }
});

// Get most active users
router.get('/most-active', authenticateToken, async (req, res) => {
  try {
    const { groupId, limit = 10 } = req.query;

    if (!groupId) {
      return res.status(400).json({ error: 'Group ID is required' });
    }

    const activeUsers = await userActivityService.getMostActiveUsers(
      req.user.id, 
      groupId, 
      parseInt(limit)
    );
    
    res.json({ activeUsers });
  } catch (error) {
    console.error('Get most active users error:', error);
    res.status(500).json({ error: 'Failed to get most active users' });
  }
});

// Get inactive users
router.get('/inactive', authenticateToken, async (req, res) => {
  try {
    const { groupId, days = 30 } = req.query;

    if (!groupId) {
      return res.status(400).json({ error: 'Group ID is required' });
    }

    const inactiveUsers = await userActivityService.getInactiveUsers(
      req.user.id, 
      groupId, 
      parseInt(days)
    );
    
    res.json({ inactiveUsers });
  } catch (error) {
    console.error('Get inactive users error:', error);
    res.status(500).json({ error: 'Failed to get inactive users' });
  }
});

// Get user activity data
router.get('/data', authenticateToken, async (req, res) => {
  try {
    const { 
      groupId, 
      memberId,
      page = 1, 
      limit = 50,
      sortBy = 'engagementScore',
      sortOrder = 'DESC'
    } = req.query;

    if (!groupId) {
      return res.status(400).json({ error: 'Group ID is required' });
    }

    const whereClause = { 
      userId: req.user.id,
      groupId
    };
    
    if (memberId) {
      whereClause.memberId = memberId;
    }

    const offset = (page - 1) * limit;
    const validSortFields = ['engagementScore', 'messageCount', 'lastActive', 'firstSeen'];
    const validSortOrders = ['ASC', 'DESC'];
    
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'engagementScore';
    const order = validSortOrders.includes(sortOrder) ? sortOrder : 'DESC';

    const { count, rows } = await UserActivity.findAndCountAll({
      where: whereClause,
      include: [{
        model: ScrapedMember,
        as: 'member',
        attributes: ['id', 'username', 'firstName', 'lastName', 'telegramId']
      }],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortField, order]]
    });

    res.json({
      activities: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get user activity data error:', error);
    res.status(500).json({ error: 'Failed to get user activity data' });
  }
});

// Update user tags or notes
router.put('/member/:memberId/group/:groupId', authenticateToken, async (req, res) => {
  try {
    const { memberId, groupId } = req.params;
    const { tags, notes } = req.body;

    const activity = await UserActivity.findOne({
      where: {
        userId: req.user.id,
        groupId,
        memberId
      }
    });

    if (!activity) {
      return res.status(404).json({ error: 'Activity record not found' });
    }

    await activity.update({
      tags: tags !== undefined ? tags : activity.tags,
      notes: notes !== undefined ? notes : activity.notes
    });

    res.json({
      message: 'User activity updated successfully',
      activity
    });
  } catch (error) {
    console.error('Update user activity error:', error);
    res.status(500).json({ error: 'Failed to update user activity' });
  }
});

// Get activity statistics for a group
router.get('/stats/:groupId', authenticateToken, async (req, res) => {
  try {
    const { groupId } = req.params;

    // Get total members
    const totalMembers = await UserActivity.count({
      where: {
        userId: req.user.id,
        groupId
      }
    });

    // Get active members (active in the last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const activeMembers = await UserActivity.count({
      where: {
        userId: req.user.id,
        groupId,
        lastActive: {
          [Op.gte]: sevenDaysAgo
        }
      }
    });

    // Get total message count
    const messageStats = await UserActivity.findOne({
      where: {
        userId: req.user.id,
        groupId
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('messageCount')), 'totalMessages'],
        [sequelize.fn('SUM', sequelize.col('reactionCount')), 'totalReactions']
      ],
      raw: true
    });

    // Get most recent activity
    const mostRecentActivity = await UserActivity.findOne({
      where: {
        userId: req.user.id,
        groupId
      },
      order: [['lastActive', 'DESC']],
      attributes: ['lastActive'],
      raw: true
    });

    res.json({
      groupId,
      totalMembers,
      activeMembers,
      inactiveMembers: totalMembers - activeMembers,
      activityRate: totalMembers > 0 ? (activeMembers / totalMembers) * 100 : 0,
      totalMessages: messageStats ? parseInt(messageStats.totalMessages) || 0 : 0,
      totalReactions: messageStats ? parseInt(messageStats.totalReactions) || 0 : 0,
      lastActivity: mostRecentActivity ? mostRecentActivity.lastActive : null
    });
  } catch (error) {
    console.error('Get activity statistics error:', error);
    res.status(500).json({ error: 'Failed to get activity statistics' });
  }
});

module.exports = router; 