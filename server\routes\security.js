const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const DeviceSpoofingService = require('../services/DeviceSpoofingService');
const ProxyService = require('../services/ProxyService');
const { ProxyConfig, TelegramAccount } = require('../models');

const router = express.Router();

// Get user's device fingerprints
router.get('/device-fingerprints', authenticateToken, async (req, res) => {
  try {
    const accounts = await TelegramAccount.findAll({
      where: { userId: req.user.id },
      attributes: ['id', 'accountName', 'deviceFingerprint', 'createdAt']
    });

    const fingerprints = accounts.map(account => ({
      accountId: account.id,
      accountName: account.accountName,
      fingerprint: account.deviceFingerprint ? JSON.parse(account.deviceFingerprint) : null,
      createdAt: account.createdAt
    }));

    res.json({ fingerprints });
  } catch (error) {
    console.error('Error getting device fingerprints:', error);
    res.status(500).json({ error: 'Failed to get device fingerprints' });
  }
});

// Generate new device fingerprint for account
router.post('/device-fingerprints/:accountId/regenerate', authenticateToken, async (req, res) => {
  try {
    const { accountId } = req.params;
    const { platform } = req.body;

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const newFingerprint = DeviceSpoofingService.generateDeviceFingerprint(
      req.user.id,
      accountId,
      platform
    );

    await account.update({
      deviceFingerprint: JSON.stringify(newFingerprint)
    });

    res.json({
      message: 'Device fingerprint regenerated successfully',
      fingerprint: newFingerprint
    });
  } catch (error) {
    console.error('Error regenerating device fingerprint:', error);
    res.status(500).json({ error: 'Failed to regenerate device fingerprint' });
  }
});

// Get user's proxies
router.get('/proxies', authenticateToken, async (req, res) => {
  try {
    const { country, type, active } = req.query;
    
    const filters = {};
    if (country) filters.country = country;
    if (type) filters.type = type;
    if (active !== undefined) filters.isActive = active === 'true';

    const proxies = await ProxyService.getUserProxies(req.user.id, filters);
    const stats = await ProxyService.getProxyStats(req.user.id);

    res.json({ 
      proxies: proxies.map(p => ({
        ...p.toJSON(),
        connectionString: p.getConnectionString()
      })),
      stats 
    });
  } catch (error) {
    console.error('Error getting proxies:', error);
    res.status(500).json({ error: 'Failed to get proxies' });
  }
});

// Add new proxy
router.post('/proxies', authenticateToken, async (req, res) => {
  try {
    const {
      name,
      type,
      host,
      port,
      username,
      password,
      country,
      provider
    } = req.body;

    // Validation
    if (!name || !host || !port) {
      return res.status(400).json({ error: 'Name, host, and port are required' });
    }

    const proxyConfig = {
      name,
      type: type || 'http',
      host,
      port: parseInt(port),
      username,
      password,
      country: country || 'US',
      provider
    };

    const proxy = await ProxyService.addUserProxy(req.user.id, proxyConfig);

    res.status(201).json({
      message: 'Proxy added successfully',
      proxy: proxy.toJSON()
    });
  } catch (error) {
    console.error('Error adding proxy:', error);
    res.status(500).json({ 
      error: error.message || 'Failed to add proxy' 
    });
  }
});

// Test proxy
router.post('/proxies/:proxyId/test', authenticateToken, async (req, res) => {
  try {
    const { proxyId } = req.params;

    const proxy = await ProxyConfig.findOne({
      where: { id: proxyId, userId: req.user.id }
    });

    if (!proxy) {
      return res.status(404).json({ error: 'Proxy not found' });
    }

    const testResult = await ProxyService.testProxy(proxy, 10000);

    await proxy.update({
      lastTested: new Date(),
      responseTime: proxy.responseTime,
      externalIp: proxy.externalIp,
      lastError: testResult ? null : 'Test failed'
    });

    res.json({
      success: testResult,
      responseTime: proxy.responseTime,
      externalIp: proxy.externalIp,
      message: testResult ? 'Proxy test successful' : 'Proxy test failed'
    });
  } catch (error) {
    console.error('Error testing proxy:', error);
    res.status(500).json({ error: 'Failed to test proxy' });
  }
});

// Delete proxy
router.delete('/proxies/:proxyId', authenticateToken, async (req, res) => {
  try {
    const { proxyId } = req.params;

    const proxy = await ProxyConfig.findOne({
      where: { id: proxyId, userId: req.user.id }
    });

    if (!proxy) {
      return res.status(404).json({ error: 'Proxy not found' });
    }

    // Check if proxy is in use
    const accountsUsingProxy = await TelegramAccount.count({
      where: { proxyId: proxyId, userId: req.user.id }
    });

    if (accountsUsingProxy > 0) {
      return res.status(400).json({ 
        error: `Cannot delete proxy. It is currently used by ${accountsUsingProxy} account(s)` 
      });
    }

    await proxy.destroy();

    res.json({ message: 'Proxy deleted successfully' });
  } catch (error) {
    console.error('Error deleting proxy:', error);
    res.status(500).json({ error: 'Failed to delete proxy' });
  }
});

// Rotate proxy for account
router.post('/accounts/:accountId/rotate-proxy', authenticateToken, async (req, res) => {
  try {
    const { accountId } = req.params;
    const { reason } = req.body;

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const newProxy = await ProxyService.rotateProxy(
      req.user.id, 
      accountId, 
      reason || 'manual'
    );

    res.json({
      message: 'Proxy rotated successfully',
      newProxy: newProxy
    });
  } catch (error) {
    console.error('Error rotating proxy:', error);
    res.status(500).json({ 
      error: error.message || 'Failed to rotate proxy' 
    });
  }
});

// Get security recommendations
router.get('/recommendations', authenticateToken, async (req, res) => {
  try {
    const accounts = await TelegramAccount.findAll({
      where: { userId: req.user.id },
      include: [{
        model: ProxyConfig,
        as: 'proxy',
        required: false
      }]
    });

    const proxies = await ProxyService.getUserProxies(req.user.id);
    
    const recommendations = [];

    // Check for accounts without proxies
    const accountsWithoutProxy = accounts.filter(a => !a.proxyId);
    if (accountsWithoutProxy.length > 0) {
      recommendations.push({
        type: 'warning',
        title: 'Accounts without proxies',
        description: `${accountsWithoutProxy.length} account(s) are not using proxies, which may increase detection risk.`,
        action: 'Add proxies to protect your accounts',
        priority: 'high'
      });
    }

    // Check for accounts without device fingerprints
    const accountsWithoutFingerprint = accounts.filter(a => !a.deviceFingerprint);
    if (accountsWithoutFingerprint.length > 0) {
      recommendations.push({
        type: 'info',
        title: 'Missing device fingerprints',
        description: `${accountsWithoutFingerprint.length} account(s) don't have device fingerprints.`,
        action: 'Device fingerprints will be generated automatically on next use',
        priority: 'medium'
      });
    }

    // Check proxy health
    const unhealthyProxies = proxies.filter(p => !p.isActive || p.lastError);
    if (unhealthyProxies.length > 0) {
      recommendations.push({
        type: 'warning',
        title: 'Unhealthy proxies detected',
        description: `${unhealthyProxies.length} proxy(ies) are not working properly.`,
        action: 'Test and fix or replace unhealthy proxies',
        priority: 'high'
      });
    }

    // Check for geographic diversity
    const countries = [...new Set(proxies.map(p => p.country))];
    if (countries.length < 2 && proxies.length > 1) {
      recommendations.push({
        type: 'info',
        title: 'Limited geographic diversity',
        description: 'All proxies are from the same country.',
        action: 'Consider adding proxies from different countries for better security',
        priority: 'low'
      });
    }

    res.json({ recommendations });
  } catch (error) {
    console.error('Error getting security recommendations:', error);
    res.status(500).json({ error: 'Failed to get security recommendations' });
  }
});

// Get activity patterns for user
router.get('/activity-patterns', authenticateToken, async (req, res) => {
  try {
    const pattern = DeviceSpoofingService.generateActivityPattern(req.user.id);
    res.json({ pattern });
  } catch (error) {
    console.error('Error getting activity patterns:', error);
    res.status(500).json({ error: 'Failed to get activity patterns' });
  }
});

module.exports = router;
