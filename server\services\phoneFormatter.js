class PhoneFormatter {
  constructor() {
    this.countryCodes = {
      'US': '+1',
      'CA': '+1',
      'GB': '+44',
      'DE': '+49',
      'FR': '+33',
      'IT': '+39',
      'ES': '+34',
      'RU': '+7',
      'IN': '+91',
      'CN': '+86',
      'JP': '+81',
      'KR': '+82',
      'BR': '+55',
      'MX': '+52',
      'AR': '+54',
      'AU': '+61',
      'NZ': '+64',
      'ZA': '+27',
      'EG': '+20',
      'NG': '+234',
      'KE': '+254',
      'TR': '+90',
      'SA': '+966',
      'AE': '+971',
      'PK': '+92',
      'BD': '+880',
      'TH': '+66',
      'VN': '+84',
      'PH': '+63',
      'ID': '+62',
      'MY': '+60',
      'SG': '+65',
      'UA': '+380',
      'PL': '+48',
      'NL': '+31',
      'BE': '+32',
      'CH': '+41',
      'AT': '+43',
      'SE': '+46',
      'NO': '+47',
      'DK': '+45',
      'FI': '+358',
      'GR': '+30',
      'PT': '+351',
      'CZ': '+420',
      'HU': '+36',
      'RO': '+40',
      'BG': '+359',
      'HR': '+385',
      'RS': '+381',
      'SI': '+386',
      'SK': '+421',
      'LT': '+370',
      'LV': '+371',
      'EE': '+372',
      'IL': '+972',
      'IR': '+98',
      'IQ': '+964',
      'JO': '+962',
      'LB': '+961',
      'SY': '+963',
      'YE': '+967',
      'OM': '+968',
      'QA': '+974',
      'KW': '+965',
      'BH': '+973'
    };

    this.patterns = {
      '+1': /^(\+1)(\d{3})(\d{3})(\d{4})$/, // US/Canada
      '+44': /^(\+44)(\d{4})(\d{6})$/, // UK
      '+49': /^(\+49)(\d{3,4})(\d{7,8})$/, // Germany
      '+33': /^(\+33)(\d{1})(\d{2})(\d{2})(\d{2})(\d{2})$/, // France
      '+39': /^(\+39)(\d{3})(\d{3})(\d{4})$/, // Italy
      '+7': /^(\+7)(\d{3})(\d{3})(\d{2})(\d{2})$/, // Russia
      '+91': /^(\+91)(\d{5})(\d{5})$/, // India
      '+86': /^(\+86)(\d{3})(\d{4})(\d{4})$/, // China
      '+81': /^(\+81)(\d{2})(\d{4})(\d{4})$/, // Japan
      '+82': /^(\+82)(\d{2})(\d{4})(\d{4})$/, // South Korea
      '+55': /^(\+55)(\d{2})(\d{5})(\d{4})$/, // Brazil
      '+52': /^(\+52)(\d{3})(\d{3})(\d{4})$/, // Mexico
      '+61': /^(\+61)(\d{3})(\d{3})(\d{3})$/, // Australia
      '+90': /^(\+90)(\d{3})(\d{3})(\d{2})(\d{2})$/, // Turkey
      '+380': /^(\+380)(\d{2})(\d{3})(\d{2})(\d{2})$/, // Ukraine
      '+48': /^(\+48)(\d{3})(\d{3})(\d{3})$/, // Poland
      '+31': /^(\+31)(\d{2})(\d{4})(\d{4})$/, // Netherlands
      '+46': /^(\+46)(\d{2})(\d{3})(\d{2})(\d{2})$/, // Sweden
      '+47': /^(\+47)(\d{4})(\d{4})$/, // Norway
      '+45': /^(\+45)(\d{4})(\d{4})$/, // Denmark
      '+358': /^(\+358)(\d{2})(\d{3})(\d{4})$/, // Finland
      '+41': /^(\+41)(\d{2})(\d{3})(\d{2})(\d{2})$/, // Switzerland
      '+43': /^(\+43)(\d{4})(\d{6})$/, // Austria
      '+32': /^(\+32)(\d{3})(\d{2})(\d{2})(\d{2})$/, // Belgium
      '+972': /^(\+972)(\d{2})(\d{3})(\d{4})$/, // Israel
      '+966': /^(\+966)(\d{2})(\d{3})(\d{4})$/, // Saudi Arabia
      '+971': /^(\+971)(\d{2})(\d{3})(\d{4})$/, // UAE
      '+92': /^(\+92)(\d{3})(\d{3})(\d{4})$/, // Pakistan
      '+880': /^(\+880)(\d{4})(\d{6})$/, // Bangladesh
      '+66': /^(\+66)(\d{2})(\d{3})(\d{4})$/, // Thailand
      '+84': /^(\+84)(\d{3})(\d{3})(\d{4})$/, // Vietnam
      '+63': /^(\+63)(\d{3})(\d{3})(\d{4})$/, // Philippines
      '+62': /^(\+62)(\d{3})(\d{4})(\d{4})$/, // Indonesia
      '+60': /^(\+60)(\d{2})(\d{4})(\d{4})$/, // Malaysia
      '+65': /^(\+65)(\d{4})(\d{4})$/ // Singapore
    };
  }

  formatPhone(phoneNumber, countryCode = null) {
    if (!phoneNumber) return null;

    // Clean the phone number
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // Add country code if not present
    if (!cleaned.startsWith('+')) {
      if (countryCode && this.countryCodes[countryCode]) {
        cleaned = this.countryCodes[countryCode] + cleaned;
      } else {
        // Default to US if no country code specified
        cleaned = '+1' + cleaned;
      }
    }

    // Find matching pattern and format
    for (const [code, pattern] of Object.entries(this.patterns)) {
      if (cleaned.startsWith(code)) {
        const match = cleaned.match(pattern);
        if (match) {
          return this.applyFormat(match, code);
        }
      }
    }

    // Return cleaned number if no pattern matches
    return cleaned;
  }

  applyFormat(match, countryCode) {
    switch (countryCode) {
      case '+1': // US/Canada
        return `${match[1]} (${match[2]}) ${match[3]}-${match[4]}`;
      case '+44': // UK
        return `${match[1]} ${match[2]} ${match[3]}`;
      case '+49': // Germany
        return `${match[1]} ${match[2]} ${match[3]}`;
      case '+33': // France
        return `${match[1]} ${match[2]} ${match[3]} ${match[4]} ${match[5]} ${match[6]}`;
      case '+7': // Russia
        return `${match[1]} ${match[2]} ${match[3]}-${match[4]}-${match[5]}`;
      case '+91': // India
        return `${match[1]} ${match[2]} ${match[3]}`;
      case '+86': // China
        return `${match[1]} ${match[2]} ${match[3]} ${match[4]}`;
      default:
        return match[0]; // Return original if no specific format
    }
  }

  validatePhone(phoneNumber) {
    if (!phoneNumber) return false;

    const cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // Must start with + and have at least 7 digits
    if (!cleaned.startsWith('+') || cleaned.length < 8) {
      return false;
    }

    // Check if it matches any known pattern
    for (const [code, pattern] of Object.entries(this.patterns)) {
      if (cleaned.startsWith(code) && pattern.test(cleaned)) {
        return true;
      }
    }

    // Basic validation for unknown patterns
    return cleaned.length >= 8 && cleaned.length <= 15;
  }

  extractCountryCode(phoneNumber) {
    if (!phoneNumber) return null;

    const cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    for (const [country, code] of Object.entries(this.countryCodes)) {
      if (cleaned.startsWith(code)) {
        return {
          country,
          code,
          number: cleaned.substring(code.length)
        };
      }
    }

    return null;
  }

  generatePhoneVariations(baseNumber, count = 10) {
    const variations = [];
    const countryInfo = this.extractCountryCode(baseNumber);
    
    if (!countryInfo) return variations;

    const baseDigits = countryInfo.number;
    const countryCode = countryInfo.code;

    // Generate variations by changing last few digits
    for (let i = 0; i < count; i++) {
      const lastDigits = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      const newNumber = baseDigits.substring(0, baseDigits.length - 4) + lastDigits;
      variations.push(countryCode + newNumber);
    }

    return variations;
  }

  formatForTelegram(phoneNumber) {
    // Telegram expects phone numbers without + and spaces
    if (!phoneNumber) return null;
    
    return phoneNumber.replace(/[^\d]/g, '');
  }

  formatForDisplay(phoneNumber) {
    // Format for user-friendly display
    return this.formatPhone(phoneNumber);
  }

  getCountryFromCode(countryCode) {
    return Object.keys(this.countryCodes).find(
      country => this.countryCodes[country] === countryCode
    );
  }

  getAllCountryCodes() {
    return Object.entries(this.countryCodes).map(([country, code]) => ({
      country,
      code,
      name: this.getCountryName(country)
    }));
  }

  getCountryName(countryCode) {
    const names = {
      'US': 'United States',
      'CA': 'Canada',
      'GB': 'United Kingdom',
      'DE': 'Germany',
      'FR': 'France',
      'IT': 'Italy',
      'ES': 'Spain',
      'RU': 'Russia',
      'IN': 'India',
      'CN': 'China',
      'JP': 'Japan',
      'KR': 'South Korea',
      'BR': 'Brazil',
      'MX': 'Mexico',
      'AR': 'Argentina',
      'AU': 'Australia',
      'NZ': 'New Zealand',
      'ZA': 'South Africa',
      'EG': 'Egypt',
      'NG': 'Nigeria',
      'KE': 'Kenya',
      'TR': 'Turkey',
      'SA': 'Saudi Arabia',
      'AE': 'United Arab Emirates',
      'PK': 'Pakistan',
      'BD': 'Bangladesh',
      'TH': 'Thailand',
      'VN': 'Vietnam',
      'PH': 'Philippines',
      'ID': 'Indonesia',
      'MY': 'Malaysia',
      'SG': 'Singapore',
      'UA': 'Ukraine',
      'PL': 'Poland',
      'NL': 'Netherlands',
      'BE': 'Belgium',
      'CH': 'Switzerland',
      'AT': 'Austria',
      'SE': 'Sweden',
      'NO': 'Norway',
      'DK': 'Denmark',
      'FI': 'Finland',
      'GR': 'Greece',
      'PT': 'Portugal',
      'CZ': 'Czech Republic',
      'HU': 'Hungary',
      'RO': 'Romania',
      'BG': 'Bulgaria',
      'HR': 'Croatia',
      'RS': 'Serbia',
      'SI': 'Slovenia',
      'SK': 'Slovakia',
      'LT': 'Lithuania',
      'LV': 'Latvia',
      'EE': 'Estonia',
      'IL': 'Israel',
      'IR': 'Iran',
      'IQ': 'Iraq',
      'JO': 'Jordan',
      'LB': 'Lebanon',
      'SY': 'Syria',
      'YE': 'Yemen',
      'OM': 'Oman',
      'QA': 'Qatar',
      'KW': 'Kuwait',
      'BH': 'Bahrain'
    };

    return names[countryCode] || countryCode;
  }
}

module.exports = new PhoneFormatter();
