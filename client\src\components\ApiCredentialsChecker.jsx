import React, { useState, useEffect } from 'react';
import { AlertTriangle, Key, CheckCircle } from 'lucide-react';
import ApiSetup from './ApiSetup';

const ApiCredentialsChecker = ({ children, user, onUserUpdate }) => {
  const [showApiSetup, setShowApiSetup] = useState(false);
  const [apiStatus, setApiStatus] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkApiCredentials();
  }, [user]);

  const checkApiCredentials = async () => {
    try {
      const response = await fetch('/api/user/api-credentials/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setApiStatus(data);
      }
    } catch (error) {
      console.error('Failed to check API credentials:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApiSetupComplete = (updatedUser) => {
    setApiStatus({
      hasCredentials: true,
      verified: true,
      verifiedAt: new Date(),
      apiId: updatedUser.telegramApiId
    });
    setShowApiSetup(false);
    if (onUserUpdate) {
      onUserUpdate(updatedUser);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show API setup requirement
  if (!apiStatus?.hasCredentials || !apiStatus?.verified) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
              <Key className="h-8 w-8 text-yellow-600" />
            </div>
            
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              API Credentials Required
            </h2>
            
            <p className="text-gray-600 mb-6">
              To use Telegram automation features, you need to configure your personal Telegram API credentials. 
              This is a one-time setup that takes just 2-3 minutes.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="text-left">
                  <h3 className="font-medium text-blue-900">Why This is Better</h3>
                  <ul className="text-sm text-blue-700 mt-1 space-y-1">
                    <li>• Your own API access (no sharing)</li>
                    <li>• Unlimited speed and performance</li>
                    <li>• Maximum security and privacy</li>
                    <li>• Professional-grade setup</li>
                  </ul>
                </div>
              </div>
            </div>

            <button
              onClick={() => setShowApiSetup(true)}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 font-medium"
            >
              Set Up API Credentials
            </button>

            <p className="text-xs text-gray-500 mt-4">
              Get your credentials from{' '}
              <a 
                href="https://my.telegram.org/apps" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800"
              >
                my.telegram.org/apps
              </a>
            </p>
          </div>
        </div>

        {showApiSetup && (
          <ApiSetup
            user={user}
            onUpdate={handleApiSetupComplete}
            onClose={() => setShowApiSetup(false)}
          />
        )}
      </div>
    );
  }

  // Show API credentials status banner if not verified
  const showStatusBanner = apiStatus?.hasCredentials && !apiStatus?.verified;

  return (
    <div>
      {showStatusBanner && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  API Credentials Not Verified
                </p>
                <p className="text-sm text-yellow-700">
                  Please verify your Telegram API credentials to use all features.
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowApiSetup(true)}
              className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded text-sm hover:bg-yellow-200"
            >
              Verify Now
            </button>
          </div>
        </div>
      )}

      {children}

      {showApiSetup && (
        <ApiSetup
          user={user}
          onUpdate={handleApiSetupComplete}
          onClose={() => setShowApiSetup(false)}
        />
      )}
    </div>
  );
};

export default ApiCredentialsChecker;
