const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { authenticateToken } = require('../middleware/auth');
const PaymentService = require('../services/paymentService');
const { Payment } = require('../models');

const router = express.Router();

// Get subscription pricing
router.get('/pricing', (req, res) => {
  try {
    const pricing = Payment.getSubscriptionPricing();
    res.json({ pricing });
  } catch (error) {
    console.error('Get pricing error:', error);
    res.status(500).json({ error: 'Failed to fetch pricing' });
  }
});

// Create payment intent
router.post('/create-intent', authenticateToken, async (req, res) => {
  try {
    const { subscriptionPlan, duration = 1 } = req.body;
    
    if (!subscriptionPlan || !['basic', 'premium', 'enterprise'].includes(subscriptionPlan)) {
      return res.status(400).json({ error: 'Invalid subscription plan' });
    }

    if (!duration || duration < 1 || duration > 12) {
      return res.status(400).json({ error: 'Invalid duration (1-12 months)' });
    }

    const result = await PaymentService.createPaymentIntent(
      req.user.id,
      subscriptionPlan,
      duration
    );

    res.json(result);
  } catch (error) {
    console.error('Create payment intent error:', error);
    res.status(500).json({ error: 'Failed to create payment intent' });
  }
});

// Confirm payment
router.post('/confirm/:paymentId', authenticateToken, async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    const payment = await Payment.findOne({
      where: { id: paymentId, userId: req.user.id }
    });

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    // Get payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(payment.stripePaymentIntentId);
    
    if (paymentIntent.status === 'succeeded') {
      const result = await PaymentService.handleSuccessfulPayment(payment.stripePaymentIntentId);
      res.json({ 
        success: true, 
        message: 'Payment confirmed successfully',
        subscription: result.user.subscriptionPlan,
        expiry: result.user.subscriptionExpiry
      });
    } else {
      res.status(400).json({ 
        error: 'Payment not completed',
        status: paymentIntent.status 
      });
    }
  } catch (error) {
    console.error('Confirm payment error:', error);
    res.status(500).json({ error: 'Failed to confirm payment' });
  }
});

// Get payment history
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;
    const offset = (page - 1) * limit;

    const result = await PaymentService.getPaymentHistory(req.user.id, {
      limit: parseInt(limit),
      offset,
      status
    });

    res.json({
      payments: result.rows,
      pagination: {
        total: result.count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(result.count / limit)
      }
    });
  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({ error: 'Failed to fetch payment history' });
  }
});

// Get current subscription status
router.get('/subscription', authenticateToken, async (req, res) => {
  try {
    const status = await PaymentService.checkSubscriptionStatus(req.user.id);
    res.json(status);
  } catch (error) {
    console.error('Get subscription status error:', error);
    res.status(500).json({ error: 'Failed to fetch subscription status' });
  }
});

// Request refund
router.post('/refund/:paymentId', authenticateToken, async (req, res) => {
  try {
    const { paymentId } = req.params;
    const { amount, reason } = req.body;

    const payment = await Payment.findOne({
      where: { id: paymentId, userId: req.user.id }
    });

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    if (payment.status !== 'succeeded') {
      return res.status(400).json({ error: 'Cannot refund non-successful payment' });
    }

    const result = await PaymentService.processRefund(paymentId, amount, reason);
    res.json({ 
      success: true, 
      message: 'Refund processed successfully',
      refund: result.refund
    });
  } catch (error) {
    console.error('Process refund error:', error);
    res.status(500).json({ error: 'Failed to process refund' });
  }
});

// Stripe webhook endpoint
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    await PaymentService.handleWebhook(event);
    res.json({ received: true });
  } catch (error) {
    console.error('Webhook handling error:', error);
    res.status(500).json({ error: 'Webhook handling failed' });
  }
});

// Admin routes
router.get('/admin/revenue', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { startDate, endDate } = req.query;
    const options = {};
    
    if (startDate) options.startDate = new Date(startDate);
    if (endDate) options.endDate = new Date(endDate);

    const stats = await PaymentService.getRevenueStats(options);
    res.json({ stats });
  } catch (error) {
    console.error('Get revenue stats error:', error);
    res.status(500).json({ error: 'Failed to fetch revenue statistics' });
  }
});

router.get('/admin/payments', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { page = 1, limit = 50, status, userId } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    if (status) whereClause.status = status;
    if (userId) whereClause.userId = userId;

    const result = await Payment.findAndCountAll({
      where: whereClause,
      include: [{
        model: require('../models').User,
        as: 'user',
        attributes: ['id', 'username', 'email', 'firstName', 'lastName']
      }],
      limit: parseInt(limit),
      offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      payments: result.rows,
      pagination: {
        total: result.count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(result.count / limit)
      }
    });
  } catch (error) {
    console.error('Get admin payments error:', error);
    res.status(500).json({ error: 'Failed to fetch payments' });
  }
});

// Cancel subscription
router.post('/cancel-subscription', authenticateToken, async (req, res) => {
  try {
    const { User } = require('../models');
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // If user has a Stripe customer ID, cancel any active subscriptions
    if (user.stripeCustomerId) {
      const subscriptions = await stripe.subscriptions.list({
        customer: user.stripeCustomerId,
        status: 'active'
      });

      for (const subscription of subscriptions.data) {
        await stripe.subscriptions.del(subscription.id);
      }
    }

    // Update user to free plan
    await user.update({
      subscriptionPlan: 'free',
      subscriptionExpiry: null,
      dailyMessageLimit: 50,
      dailyAddLimit: 25,
      maxAccounts: 1
    });

    res.json({ 
      success: true, 
      message: 'Subscription cancelled successfully' 
    });
  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({ error: 'Failed to cancel subscription' });
  }
});

// Get invoice
router.get('/invoice/:paymentId', authenticateToken, async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    const payment = await Payment.findOne({
      where: { id: paymentId, userId: req.user.id },
      include: [{
        model: require('../models').User,
        as: 'user',
        attributes: ['username', 'email', 'firstName', 'lastName']
      }]
    });

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    // Generate invoice data
    const invoice = {
      paymentId: payment.id,
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      subscriptionPlan: payment.subscriptionPlan,
      subscriptionDuration: payment.subscriptionDuration,
      paidAt: payment.paidAt,
      customer: {
        name: `${payment.user.firstName} ${payment.user.lastName}`.trim(),
        email: payment.user.email
      },
      invoiceNumber: `INV-${payment.id.toString().padStart(6, '0')}`,
      dueDate: payment.createdAt
    };

    res.json({ invoice });
  } catch (error) {
    console.error('Get invoice error:', error);
    res.status(500).json({ error: 'Failed to fetch invoice' });
  }
});

module.exports = router;
