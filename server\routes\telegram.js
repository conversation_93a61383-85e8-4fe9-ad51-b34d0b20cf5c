const express = require('express');
const { Telegram<PERSON><PERSON>, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { TelegramAccount } = require('../models');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

const router = express.Router();

// Get all telegram accounts for user
router.get('/accounts', authenticateToken, async (req, res) => {
  try {
    const accounts = await TelegramAccount.findAll({
      where: { userId: req.user.id },
      attributes: { exclude: ['sessionString'] }
    });
    res.json({ accounts });
  } catch (error) {
    console.error('Get accounts error:', error);
    res.status(500).json({ error: 'Failed to fetch accounts' });
  }
});

// Add new telegram account
router.post('/accounts', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { phoneNumber, accountName } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({ error: 'Phone number is required' });
    }

    // Check account limits
    const existingAccounts = await TelegramAccount.count({
      where: { userId: req.user.id }
    });

    if (existingAccounts >= req.limits.maxAccounts) {
      return res.status(403).json({ 
        error: `Account limit reached. Your plan allows ${req.limits.maxAccounts} accounts.` 
      });
    }

    // Check if phone number already exists
    const existingAccount = await TelegramAccount.findOne({
      where: { phoneNumber }
    });

    if (existingAccount) {
      return res.status(400).json({ error: 'Phone number already registered' });
    }

    // Create account record
    const account = await TelegramAccount.create({
      userId: req.user.id,
      phoneNumber,
      accountName: accountName || `Account ${phoneNumber}`,
      status: 'pending'
    });

    // Check if user has API credentials
    if (!req.user.telegramApiId || !req.user.telegramApiHash) {
      return res.status(400).json({
        error: 'Please configure your Telegram API credentials first',
        requiresApiSetup: true
      });
    }

    if (!req.user.apiCredentialsVerified) {
      return res.status(400).json({
        error: 'Please verify your Telegram API credentials first',
        requiresApiVerification: true
      });
    }

    // Initialize Telegram client and send code
    try {
      const apiId = parseInt(req.user.telegramApiId);
      const apiHash = req.user.telegramApiHash;

      const client = new TelegramApi(new StringSession(''), apiId, apiHash, {
        connectionRetries: 5,
        retryDelay: 1000,
        timeout: 30000,
        useWSS: false
      });

      await client.connect();

      // Send authentication code
      const result = await client.invoke(new Api.auth.SendCode({
        phoneNumber: phoneNumber,
        apiId: apiId,
        apiHash: apiHash,
        settings: new Api.CodeSettings({
          allowFlashcall: false,
          currentNumber: false,
          allowAppHash: false
        })
      }));

      // Store the phone code hash for verification
      await account.update({
        phoneCodeHash: result.phoneCodeHash,
        status: 'code_sent'
      });

      await client.disconnect();

      res.json({
        message: 'Verification code sent to your phone',
        accountId: account.id,
        requiresCode: true
      });

    } catch (error) {
      console.error('Telegram client error:', error);
      await account.destroy();

      if (error.message.includes('PHONE_NUMBER_INVALID')) {
        res.status(400).json({ error: 'Invalid phone number format. Please use international format (+**********)' });
      } else if (error.message.includes('PHONE_NUMBER_BANNED')) {
        res.status(400).json({ error: 'This phone number is banned from Telegram' });
      } else if (error.message.includes('PHONE_NUMBER_FLOOD')) {
        res.status(429).json({ error: 'Too many requests. Please try again later' });
      } else if (error.message.includes('API_ID_INVALID')) {
        res.status(500).json({ error: 'Invalid Telegram API credentials' });
      } else {
        res.status(500).json({ error: 'Failed to send verification code: ' + error.message });
      }
    }

  } catch (error) {
    console.error('Add account error:', error);
    res.status(500).json({ error: 'Failed to add account' });
  }
});

// Resend verification code
router.post('/accounts/:id/resend-code', authenticateToken, async (req, res) => {
  try {
    const accountId = req.params.id;

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    if (account.status === 'active') {
      return res.status(400).json({ error: 'Account is already verified' });
    }

    // Check if user has API credentials
    if (!req.user.telegramApiId || !req.user.telegramApiHash) {
      return res.status(400).json({
        error: 'Please configure your Telegram API credentials first',
        requiresApiSetup: true
      });
    }

    try {
      const apiId = parseInt(req.user.telegramApiId);
      const apiHash = req.user.telegramApiHash;

      const client = new TelegramApi(new StringSession(''), apiId, apiHash, {
        connectionRetries: 5,
        retryDelay: 1000,
        timeout: 30000
      });

      await client.connect();

      // Send authentication code
      const result = await client.invoke(new Api.auth.SendCode({
        phoneNumber: account.phoneNumber,
        apiId: apiId,
        apiHash: apiHash,
        settings: new Api.CodeSettings({
          allowFlashcall: false,
          currentNumber: false,
          allowAppHash: false
        })
      }));

      // Update the phone code hash
      await account.update({
        phoneCodeHash: result.phoneCodeHash,
        status: 'code_sent'
      });

      await client.disconnect();

      res.json({
        message: 'Verification code sent to your phone',
        accountId: account.id
      });

    } catch (error) {
      console.error('Resend code error:', error);

      if (error.message.includes('PHONE_NUMBER_INVALID')) {
        res.status(400).json({ error: 'Invalid phone number format' });
      } else if (error.message.includes('PHONE_NUMBER_FLOOD')) {
        res.status(429).json({ error: 'Too many requests. Please try again later' });
      } else if (error.message.includes('API_ID_INVALID')) {
        res.status(500).json({ error: 'Telegram API credentials not configured. Please contact administrator.' });
      } else {
        res.status(500).json({ error: 'Failed to resend verification code: ' + error.message });
      }
    }

  } catch (error) {
    console.error('Resend code error:', error);
    res.status(500).json({ error: 'Failed to resend verification code' });
  }
});

// Verify telegram account with code
router.post('/accounts/:id/verify', authenticateToken, async (req, res) => {
  try {
    const { code, password } = req.body;
    const accountId = req.params.id;

    if (!code) {
      return res.status(400).json({ error: 'Verification code is required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    // Check if user has API credentials
    if (!req.user.telegramApiId || !req.user.telegramApiHash) {
      return res.status(400).json({
        error: 'Please configure your Telegram API credentials first',
        requiresApiSetup: true
      });
    }

    // Initialize Telegram client with verification
    const apiId = parseInt(req.user.telegramApiId);
    const apiHash = req.user.telegramApiHash;

    if (!account.phoneCodeHash) {
      return res.status(400).json({ error: 'No verification code was sent. Please request a new code.' });
    }

    const client = new TelegramApi(new StringSession(''), apiId, apiHash, {
      connectionRetries: 5,
      retryDelay: 1000,
      timeout: 30000
    });

    try {
      await client.connect();

      // Sign in with the verification code
      let authResult;
      try {
        authResult = await client.invoke(new Api.auth.SignIn({
          phoneNumber: account.phoneNumber,
          phoneCodeHash: account.phoneCodeHash,
          phoneCode: code
        }));
      } catch (signInError) {
        if (signInError.message.includes('SESSION_PASSWORD_NEEDED')) {
          // 2FA is enabled, need password
          if (!password) {
            await client.disconnect();
            return res.status(400).json({
              error: 'Two-factor authentication is enabled. Please provide your password.',
              requiresPassword: true
            });
          }

          // Get password info
          const passwordInfo = await client.invoke(new Api.account.GetPassword());

          // Compute password hash
          const { computeCheck } = require('telegram/Password');
          const passwordHash = await computeCheck(passwordInfo, password);

          // Sign in with password
          authResult = await client.invoke(new Api.auth.CheckPassword({
            password: passwordHash
          }));
        } else {
          throw signInError;
        }
      }

      // Get user info
      const me = await client.getMe();

      // Save session and user info
      await account.update({
        sessionString: client.session.save(),
        telegramId: me.id.toString(),
        firstName: me.firstName,
        lastName: me.lastName,
        username: me.username,
        status: 'active',
        isActive: true,
        lastActivity: new Date(),
        phoneCodeHash: null // Clear the hash after successful verification
      });

      await client.disconnect();

      res.json({
        message: 'Account verified successfully',
        account: {
          id: account.id,
          phoneNumber: account.phoneNumber,
          accountName: account.accountName,
          firstName: account.firstName,
          lastName: account.lastName,
          username: account.username,
          status: account.status
        }
      });

    } catch (error) {
      await client.disconnect();
      console.error('Verification error:', error);

      if (error.message.includes('PHONE_CODE_INVALID')) {
        res.status(400).json({ error: 'Invalid verification code. Please check and try again.' });
      } else if (error.message.includes('PHONE_CODE_EXPIRED')) {
        res.status(400).json({ error: 'Verification code has expired. Please request a new code.' });
      } else if (error.message.includes('PASSWORD_HASH_INVALID')) {
        res.status(400).json({ error: 'Invalid 2FA password. Please try again.' });
      } else if (error.message.includes('FLOOD_WAIT')) {
        const waitTime = error.message.match(/FLOOD_WAIT_(\d+)/);
        res.status(429).json({ error: `Too many attempts. Please wait ${waitTime ? waitTime[1] : '60'} seconds.` });
      } else {
        res.status(500).json({ error: 'Verification failed: ' + error.message });
      }
    }

  } catch (error) {
    console.error('Verify account error:', error);
    res.status(500).json({ error: 'Failed to verify account' });
  }
});

// Delete telegram account
router.delete('/accounts/:id', authenticateToken, async (req, res) => {
  try {
    const accountId = req.params.id;

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    await account.destroy();

    res.json({ message: 'Account deleted successfully' });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({ error: 'Failed to delete account' });
  }
});

// Get account status
router.get('/accounts/:id/status', authenticateToken, async (req, res) => {
  try {
    const accountId = req.params.id;

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    // Try to connect and check status
    if (account.sessionString) {
      try {
        const apiId = parseInt(process.env.TELEGRAM_API_ID);
        const apiHash = process.env.TELEGRAM_API_HASH;
        
        const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);
        await client.connect();
        
        const me = await client.getMe();
        
        await account.update({
          isOnline: true,
          lastActivity: new Date(),
          status: 'active'
        });

        await client.disconnect();

        res.json({ 
          status: 'online',
          account: account.toJSON()
        });

      } catch (error) {
        await account.update({
          isOnline: false,
          status: 'error'
        });

        res.json({ 
          status: 'offline',
          error: error.message,
          account: account.toJSON()
        });
      }
    } else {
      res.json({ 
        status: 'not_verified',
        account: account.toJSON()
      });
    }

  } catch (error) {
    console.error('Check status error:', error);
    res.status(500).json({ error: 'Failed to check account status' });
  }
});

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/temp/',
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.json', '.session', '.tdata'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext) || file.mimetype === 'application/json') {
      cb(null, true);
    } else {
      cb(new Error('Only JSON, session, and tdata files are allowed'));
    }
  }
});

// Import account from JSON
router.post('/accounts/import-json', authenticateToken, checkSubscription, upload.single('accountFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Check account limits
    const existingAccounts = await TelegramAccount.count({
      where: { userId: req.user.id }
    });

    if (existingAccounts >= req.limits.maxAccounts) {
      return res.status(403).json({
        error: `Account limit reached. Your plan allows ${req.limits.maxAccounts} accounts.`
      });
    }

    // Read and parse JSON file
    const fileContent = await fs.readFile(req.file.path, 'utf8');
    let accountData;

    try {
      accountData = JSON.parse(fileContent);
    } catch (parseError) {
      await fs.unlink(req.file.path); // Clean up
      return res.status(400).json({ error: 'Invalid JSON format' });
    }

    // Validate required fields
    const requiredFields = ['phoneNumber', 'sessionString'];
    const missingFields = requiredFields.filter(field => !accountData[field]);

    if (missingFields.length > 0) {
      await fs.unlink(req.file.path); // Clean up
      return res.status(400).json({
        error: `Missing required fields: ${missingFields.join(', ')}`
      });
    }

    // Check if phone number already exists
    const existingAccount = await TelegramAccount.findOne({
      where: { phoneNumber: accountData.phoneNumber }
    });

    if (existingAccount) {
      await fs.unlink(req.file.path); // Clean up
      return res.status(400).json({ error: 'Phone number already registered' });
    }

    // Test the session string
    try {
      const apiId = parseInt(process.env.TELEGRAM_API_ID);
      const apiHash = process.env.TELEGRAM_API_HASH;

      const client = new TelegramApi(new StringSession(accountData.sessionString), apiId, apiHash);
      await client.connect();

      // Get user info to validate session
      const me = await client.getMe();
      await client.disconnect();

      // Create account record
      const account = await TelegramAccount.create({
        userId: req.user.id,
        phoneNumber: accountData.phoneNumber,
        accountName: accountData.accountName || `Imported ${accountData.phoneNumber}`,
        sessionString: accountData.sessionString,
        telegramId: me.id.toString(),
        firstName: me.firstName || accountData.firstName,
        lastName: me.lastName || accountData.lastName,
        username: me.username || accountData.username,
        status: 'active',
        isActive: true,
        lastActivity: new Date(),
        notes: 'Imported from JSON'
      });

      // Clean up uploaded file
      await fs.unlink(req.file.path);

      res.json({
        message: 'Account imported successfully',
        account: {
          id: account.id,
          phoneNumber: account.phoneNumber,
          accountName: account.accountName,
          firstName: account.firstName,
          lastName: account.lastName,
          username: account.username,
          status: account.status
        }
      });

    } catch (sessionError) {
      await fs.unlink(req.file.path); // Clean up
      console.error('Session validation error:', sessionError);

      if (sessionError.message.includes('AUTH_KEY_INVALID')) {
        return res.status(400).json({ error: 'Invalid or expired session string' });
      } else {
        return res.status(400).json({ error: 'Failed to validate session. Please check the session string.' });
      }
    }

  } catch (error) {
    // Clean up uploaded file on error
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Failed to clean up file:', unlinkError);
      }
    }

    console.error('Import JSON error:', error);
    res.status(500).json({ error: 'Failed to import account' });
  }
});

// Import account from tdata folder
router.post('/accounts/import-tdata', authenticateToken, checkSubscription, upload.array('tdataFiles', 20), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No tdata files uploaded' });
    }

    const { phoneNumber, accountName } = req.body;

    if (!phoneNumber) {
      // Clean up uploaded files
      for (const file of req.files) {
        await fs.unlink(file.path);
      }
      return res.status(400).json({ error: 'Phone number is required' });
    }

    // Check account limits
    const existingAccounts = await TelegramAccount.count({
      where: { userId: req.user.id }
    });

    if (existingAccounts >= req.limits.maxAccounts) {
      // Clean up uploaded files
      for (const file of req.files) {
        await fs.unlink(file.path);
      }
      return res.status(403).json({
        error: `Account limit reached. Your plan allows ${req.limits.maxAccounts} accounts.`
      });
    }

    // Check if phone number already exists
    const existingAccount = await TelegramAccount.findOne({
      where: { phoneNumber }
    });

    if (existingAccount) {
      // Clean up uploaded files
      for (const file of req.files) {
        await fs.unlink(file.path);
      }
      return res.status(400).json({ error: 'Phone number already registered' });
    }

    try {
      // Process tdata files and convert to session string
      const sessionString = await convertTdataToSession(req.files, phoneNumber);

      if (!sessionString) {
        throw new Error('Failed to convert tdata to session');
      }

      // Test the session
      const apiId = parseInt(process.env.TELEGRAM_API_ID);
      const apiHash = process.env.TELEGRAM_API_HASH;

      const client = new TelegramApi(new StringSession(sessionString), apiId, apiHash);
      await client.connect();

      // Get user info to validate session
      const me = await client.getMe();
      await client.disconnect();

      // Create account record
      const account = await TelegramAccount.create({
        userId: req.user.id,
        phoneNumber,
        accountName: accountName || `Imported ${phoneNumber}`,
        sessionString,
        telegramId: me.id.toString(),
        firstName: me.firstName,
        lastName: me.lastName,
        username: me.username,
        status: 'active',
        isActive: true,
        lastActivity: new Date(),
        notes: 'Imported from tdata'
      });

      // Clean up uploaded files
      for (const file of req.files) {
        await fs.unlink(file.path);
      }

      res.json({
        message: 'Account imported from tdata successfully',
        account: {
          id: account.id,
          phoneNumber: account.phoneNumber,
          accountName: account.accountName,
          firstName: account.firstName,
          lastName: account.lastName,
          username: account.username,
          status: account.status
        }
      });

    } catch (conversionError) {
      // Clean up uploaded files
      for (const file of req.files) {
        await fs.unlink(file.path);
      }

      console.error('Tdata conversion error:', conversionError);
      res.status(400).json({
        error: 'Failed to convert tdata to session. Please ensure you uploaded all required tdata files.'
      });
    }

  } catch (error) {
    // Clean up uploaded files on error
    if (req.files) {
      for (const file of req.files) {
        try {
          await fs.unlink(file.path);
        } catch (unlinkError) {
          console.error('Failed to clean up file:', unlinkError);
        }
      }
    }

    console.error('Import tdata error:', error);
    res.status(500).json({ error: 'Failed to import account from tdata' });
  }
});

// Helper function to convert tdata files to session string
async function convertTdataToSession(files, phoneNumber) {
  try {
    // This is a simplified conversion - in a real implementation,
    // you would need to properly parse the tdata files
    // For now, we'll look for key files and attempt conversion

    const keyFile = files.find(f => f.originalname.includes('key_data'));
    const mapFile = files.find(f => f.originalname.includes('maps'));

    if (!keyFile) {
      throw new Error('key_data file not found in tdata');
    }

    // Read the key file
    const keyData = await fs.readFile(keyFile.path);

    // This is a placeholder - actual tdata conversion requires
    // proper parsing of Telegram Desktop's data format
    // You would need to implement the actual conversion logic here

    // For demonstration, we'll return null to indicate conversion failed
    // In a real implementation, you would:
    // 1. Parse the tdata files
    // 2. Extract the auth key and other session data
    // 3. Convert to StringSession format

    return null; // Placeholder - implement actual conversion

  } catch (error) {
    console.error('Tdata conversion error:', error);
    return null;
  }
}

// Export account data as JSON
router.get('/accounts/:id/export', authenticateToken, async (req, res) => {
  try {
    const accountId = req.params.id;

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    // Prepare export data (excluding sensitive session string for security)
    const exportData = {
      phoneNumber: account.phoneNumber,
      accountName: account.accountName,
      firstName: account.firstName,
      lastName: account.lastName,
      username: account.username,
      telegramId: account.telegramId,
      status: account.status,
      isActive: account.isActive,
      totalMessagesSent: account.totalMessagesSent,
      totalMembersAdded: account.totalMembersAdded,
      notes: account.notes,
      createdAt: account.createdAt,
      // sessionString: account.sessionString, // Uncomment if you want to include session
      exportedAt: new Date(),
      exportedBy: req.user.username
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="telegram_account_${account.phoneNumber}.json"`);
    res.json(exportData);

  } catch (error) {
    console.error('Export account error:', error);
    res.status(500).json({ error: 'Failed to export account' });
  }
});

// Bulk import accounts from JSON array
router.post('/accounts/bulk-import', authenticateToken, checkSubscription, upload.single('accountsFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Read and parse JSON file
    const fileContent = await fs.readFile(req.file.path, 'utf8');
    let accountsData;

    try {
      accountsData = JSON.parse(fileContent);
    } catch (parseError) {
      await fs.unlink(req.file.path);
      return res.status(400).json({ error: 'Invalid JSON format' });
    }

    if (!Array.isArray(accountsData)) {
      await fs.unlink(req.file.path);
      return res.status(400).json({ error: 'JSON must contain an array of accounts' });
    }

    // Check account limits
    const existingAccounts = await TelegramAccount.count({
      where: { userId: req.user.id }
    });

    if (existingAccounts + accountsData.length > req.limits.maxAccounts) {
      await fs.unlink(req.file.path);
      return res.status(403).json({
        error: `Account limit exceeded. Your plan allows ${req.limits.maxAccounts} accounts. You have ${existingAccounts} and trying to import ${accountsData.length}.`
      });
    }

    const results = {
      imported: 0,
      failed: 0,
      errors: []
    };

    for (const [index, accountData] of accountsData.entries()) {
      try {
        // Validate required fields
        if (!accountData.phoneNumber || !accountData.sessionString) {
          results.failed++;
          results.errors.push(`Account ${index + 1}: Missing phoneNumber or sessionString`);
          continue;
        }

        // Check if phone number already exists
        const existingAccount = await TelegramAccount.findOne({
          where: { phoneNumber: accountData.phoneNumber }
        });

        if (existingAccount) {
          results.failed++;
          results.errors.push(`Account ${index + 1}: Phone number ${accountData.phoneNumber} already exists`);
          continue;
        }

        // Test session (optional - can be skipped for bulk import speed)
        const apiId = parseInt(process.env.TELEGRAM_API_ID);
        const apiHash = process.env.TELEGRAM_API_HASH;

        let userInfo = {};
        try {
          const client = new TelegramApi(new StringSession(accountData.sessionString), apiId, apiHash);
          await client.connect();
          const me = await client.getMe();
          await client.disconnect();

          userInfo = {
            telegramId: me.id.toString(),
            firstName: me.firstName,
            lastName: me.lastName,
            username: me.username
          };
        } catch (sessionError) {
          // Continue with import even if session test fails
          console.warn(`Session test failed for ${accountData.phoneNumber}:`, sessionError.message);
        }

        // Create account record
        await TelegramAccount.create({
          userId: req.user.id,
          phoneNumber: accountData.phoneNumber,
          accountName: accountData.accountName || `Imported ${accountData.phoneNumber}`,
          sessionString: accountData.sessionString,
          telegramId: userInfo.telegramId || accountData.telegramId,
          firstName: userInfo.firstName || accountData.firstName,
          lastName: userInfo.lastName || accountData.lastName,
          username: userInfo.username || accountData.username,
          status: 'active',
          isActive: true,
          lastActivity: new Date(),
          notes: 'Bulk imported from JSON'
        });

        results.imported++;

      } catch (accountError) {
        results.failed++;
        results.errors.push(`Account ${index + 1}: ${accountError.message}`);
      }
    }

    // Clean up uploaded file
    await fs.unlink(req.file.path);

    res.json({
      message: `Bulk import completed. ${results.imported} accounts imported, ${results.failed} failed.`,
      results
    });

  } catch (error) {
    // Clean up uploaded file on error
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Failed to clean up file:', unlinkError);
      }
    }

    console.error('Bulk import error:', error);
    res.status(500).json({ error: 'Failed to bulk import accounts' });
  }
});

module.exports = router;
