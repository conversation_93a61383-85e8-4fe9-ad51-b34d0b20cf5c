import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

const Analytics = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [dashboardData, setDashboardData] = useState(null);
  const [accountHealth, setAccountHealth] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      const [dashboardResponse, healthResponse, activityResponse] = await Promise.all([
        api.get(`/analytics/dashboard?timeRange=${timeRange}`),
        api.get('/analytics/health/summary'),
        api.get('/analytics/activity?limit=20')
      ]);

      setDashboardData(dashboardResponse.data);
      setAccountHealth(healthResponse.data);
      setRecentActivity(activityResponse.data.activity);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toString() || '0';
  };

  const getEventTypeIcon = (eventType) => {
    const icons = {
      message_sent: '💬',
      member_added: '👥',
      member_scraped: '🔍',
      group_joined: '➕',
      group_left: '➖',
      automation_executed: '🤖',
      login: '🔐',
      account_added: '📱',
      payment_made: '💳'
    };
    return icons[eventType] || '📊';
  };

  const getHealthColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 30) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        
        <div className="flex space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          <button
            onClick={fetchAnalyticsData}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      {dashboardData?.eventStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {dashboardData.eventStats.map((stat) => (
            <div key={stat.eventType} className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 capitalize">
                    {stat.eventType.replace('_', ' ')}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(stat.total)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {stat.successRate}% success rate
                  </p>
                </div>
                <div className="text-3xl">
                  {getEventTypeIcon(stat.eventType)}
                </div>
              </div>
              
              <div className="mt-4">
                <div className="flex justify-between text-xs text-gray-600">
                  <span>Success: {stat.successful}</span>
                  <span>Failed: {stat.failed}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${stat.successRate}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Account Health Summary */}
      {accountHealth && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Account Health Overview</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{accountHealth.summary.total}</p>
              <p className="text-sm text-gray-600">Total Accounts</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{accountHealth.summary.healthy}</p>
              <p className="text-sm text-gray-600">Healthy</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{accountHealth.summary.warning}</p>
              <p className="text-sm text-gray-600">Warning</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{accountHealth.summary.critical}</p>
              <p className="text-sm text-gray-600">Critical</p>
            </div>
          </div>

          <div className="space-y-3">
            {accountHealth.accounts.slice(0, 5).map((account) => (
              <div key={account.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    account.healthScore >= 80 ? 'bg-green-500' :
                    account.healthScore >= 60 ? 'bg-yellow-500' :
                    account.healthScore >= 30 ? 'bg-orange-500' : 'bg-red-500'
                  }`}></div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {account.telegramAccount?.accountName || account.telegramAccount?.phoneNumber}
                    </p>
                    <p className="text-sm text-gray-600">
                      Risk Level: {account.riskLevel}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-lg font-bold ${getHealthColor(account.healthScore)}`}>
                    {account.healthScore}%
                  </p>
                  <p className="text-xs text-gray-500">Health Score</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Activity</h2>
        
        <div className="space-y-3">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-center justify-between p-3 border-l-4 border-blue-500 bg-gray-50">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">
                  {getEventTypeIcon(activity.eventType)}
                </div>
                <div>
                  <p className="font-medium text-gray-900 capitalize">
                    {activity.eventType.replace('_', ' ')}
                  </p>
                  <p className="text-sm text-gray-600">
                    {activity.telegramAccount?.accountName || 'System'}
                  </p>
                  {activity.errorMessage && (
                    <p className="text-xs text-red-600">{activity.errorMessage}</p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className={`inline-flex px-2 py-1 text-xs rounded-full ${
                  activity.success 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {activity.success ? 'Success' : 'Failed'}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {new Date(activity.timestamp).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>

        {recentActivity.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>No recent activity found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Analytics;
