# Quick Setup Guide

This guide will help you get the Telegram Management System running locally in just a few minutes.

## Prerequisites

- **Node.js 16+** - [Download here](https://nodejs.org/)
- **Git** - [Download here](https://git-scm.com/)
- **Telegram API Credentials** - [Get them here](https://my.telegram.org/apps)

## Step 1: Get Telegram API Credentials

1. Go to [my.telegram.org/apps](https://my.telegram.org/apps)
2. Log in with your Telegram account
3. Click "Create application"
4. Fill in the form:
   - **App title**: Your app name (e.g., "My TG Manager")
   - **Short name**: Short identifier (e.g., "mytgmanager")
   - **Platform**: Choose "Desktop"
   - **Description**: Brief description
5. Save your `api_id` and `api_hash` - you'll need these!

## Step 2: <PERSON>lone and Install

```bash
# Clone the repository
git clone <repository-url>
cd telegram-management-system

# Install all dependencies
npm run install:all
```

## Step 3: Configure Environment

```bash
# Navigate to server directory
cd server

# Copy environment template
cp .env.example .env

# Edit the environment file
# On Windows: notepad .env
# On Mac/Linux: nano .env
```

**Edit the `.env` file with your credentials:**

```env
# Change this to a secure random string
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Add your Telegram API credentials here
TELEGRAM_API_ID=your-api-id-from-step-1
TELEGRAM_API_HASH=your-api-hash-from-step-1

# Keep other settings as default for local development
```

## Step 4: Start the Application

```bash
# Go back to project root
cd ..

# Start both frontend and backend
npm run dev
```

You should see output like:
```
[0] Server running on port 3001
[1] ➜  Local:   http://localhost:3000/
```

## Step 5: Access the Application

Open your browser and go to: **http://localhost:3000**

You should see the login page!

## Step 6: Create Your Account

1. Click "Sign up" on the login page
2. Fill in your details:
   - Username (unique)
   - Email address
   - Password (minimum 6 characters)
   - First/Last name (optional)
3. Click "Sign up"
4. You'll be automatically logged in

## Step 7: Add Your First Telegram Account

1. Go to "Telegram Accounts" in the sidebar
2. Click "Add Account"
3. Enter your phone number (with country code, e.g., +**********)
4. Give it a name (optional)
5. Click "Add Account"
6. Check your phone for the verification code
7. Enter the code in the popup
8. If you have 2FA enabled, enter your password
9. Your account should now show as "Active"

## Step 8: Test the Features

### Scrape Members
1. Go to "Members" page
2. Click "Scrape Members"
3. Select your Telegram account
4. Enter a public group username (e.g., @telegram)
5. Click "Start Scraping"

### Send Messages
1. Go to "Messages" page
2. Try sending a single message to yourself
3. Use your username or phone number as target

## Troubleshooting

### Common Issues

**1. "Failed to send verification code"**
- Check your phone number format (include country code)
- Make sure your Telegram API credentials are correct
- Try a different phone number

**2. "Database connection failed"**
- The SQLite database is created automatically
- Make sure you have write permissions in the server directory

**3. "Port already in use"**
- Another application might be using port 3000 or 3001
- Kill the process: `npx kill-port 3000 3001`
- Or change ports in the configuration

**4. "Module not found" errors**
- Run `npm run install:all` again
- Delete `node_modules` folders and reinstall:
  ```bash
  rm -rf node_modules client/node_modules server/node_modules
  npm run install:all
  ```

**5. Frontend not loading**
- Make sure both frontend and backend are running
- Check the browser console for errors
- Try refreshing the page

### Getting Help

If you encounter issues:

1. **Check the console output** for error messages
2. **Verify your environment variables** in `server/.env`
3. **Make sure all dependencies are installed** with `npm run install:all`
4. **Check if ports 3000 and 3001 are available**

### Development Tips

- **Auto-reload**: Both frontend and backend auto-reload when you make changes
- **Database**: SQLite database file is created at `server/database.sqlite`
- **Logs**: Server logs appear in the terminal where you ran `npm run dev`
- **API Testing**: Backend API is available at `http://localhost:3001/api`

## Next Steps

Once you have the application running:

1. **Explore the Dashboard** - See your account statistics
2. **Add more Telegram accounts** - Test with multiple accounts
3. **Try member scraping** - Start with public groups
4. **Test messaging features** - Send messages to yourself first
5. **Check the Settings** - Customize your profile

## Production Deployment

When you're ready to deploy to a VPS:

1. See `DEPLOYMENT.md` for detailed deployment instructions
2. Use environment variables for production settings
3. Set up SSL certificates for HTTPS
4. Configure a reverse proxy (Nginx recommended)
5. Use PM2 for process management

## Security Notes

- **Never share your API credentials**
- **Use strong passwords**
- **Keep your JWT secret secure**
- **Don't commit `.env` files to version control**
- **Use HTTPS in production**

## Features Overview

### ✅ What's Working
- User registration and authentication
- Telegram account management
- Member scraping from groups
- Single and bulk messaging
- Basic dashboard and statistics
- CSV export functionality

### 🚧 Advanced Features (Future)
- Scheduled messaging
- Advanced member filtering
- Group management tools
- Proxy rotation
- Advanced analytics
- API webhooks

Enjoy using your Telegram Management System! 🚀
