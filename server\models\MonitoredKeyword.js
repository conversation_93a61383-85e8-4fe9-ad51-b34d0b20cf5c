const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class MonitoredKeyword extends Model {}

MonitoredKeyword.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  monitorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'KeywordMonitors',
      key: 'id'
    }
  },
  keywordText: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  matchType: {
    type: DataTypes.ENUM('exact', 'contains', 'regex'),
    allowNull: false,
    defaultValue: 'contains'
  },
  caseSensitive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  priority: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 5,
    comment: 'Priority level 1-10, higher is more important'
  },
  detectionCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  lastDetected: {
    type: DataTypes.DATE,
    allowNull: true
  },
  autoRespondEnabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  autoResponseText: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  autoResponseDelay: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Delay in seconds before sending auto-response'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'MonitoredKeyword',
  tableName: 'monitored_keywords',
  timestamps: true
});

module.exports = MonitoredKeyword; 