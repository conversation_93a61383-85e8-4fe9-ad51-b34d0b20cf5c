const { Telegram<PERSON><PERSON>, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');
const { UserActivity, TelegramAccount, ScrapedMember } = require('../models');
const { Op } = require('sequelize');

class UserActivityService {
  constructor() {
    this.activeTrackers = new Map();
    console.log('User Activity Service initialized');
  }

  /**
   * Start tracking user activity in a group
   */
  async startTracking(accountId, groupId, options = {}) {
    try {
      const trackerId = `${accountId}-${groupId}`;
      
      // Check if already tracking
      if (this.activeTrackers.has(trackerId)) {
        return { success: true, message: 'Already tracking this group' };
      }

      const account = await TelegramAccount.findByPk(accountId);
      if (!account || !account.sessionString) {
        return { success: false, error: 'Account not found or not authenticated' };
      }

      // Initialize Telegram client
      const apiId = parseInt(process.env.TELEGRAM_API_ID);
      const apiHash = process.env.TELEGRAM_API_HASH;
      
      const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);
      await client.connect();

      // Get group entity
      const group = await client.getEntity(groupId);
      if (!group) {
        await client.disconnect();
        return { success: false, error: 'Group not found' };
      }

      // Start tracking
      client.addEventHandler(async (event) => {
        try {
          await this.handleNewMessage(event, account.userId, accountId, group.id.toString());
        } catch (error) {
          console.error(`Error handling message for tracker ${trackerId}:`, error);
        }
      }, new Api.events.NewMessage({ chats: [group] }));

      // Store client in active trackers map
      this.activeTrackers.set(trackerId, {
        client,
        account,
        group,
        options,
        startTime: new Date()
      });

      console.log(`Started user activity tracking for ${trackerId}`);
      return { 
        success: true, 
        message: 'Started user activity tracking',
        groupTitle: group.title || group.username
      };
    } catch (error) {
      console.error(`Error starting tracking for account ${accountId}, group ${groupId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle new message for activity tracking
   */
  async handleNewMessage(event, userId, accountId, groupId) {
    try {
      const message = event.message;
      if (!message) return;

      // Get sender
      const sender = await message.getSender();
      if (!sender || !sender.id) return;

      const senderId = sender.id.toString();
      const messageDate = new Date(message.date * 1000);
      
      // Update or create activity record
      await this.updateUserActivity(userId, accountId, groupId, senderId, {
        messageCount: 1,
        lastMessageDate: messageDate,
        messageData: {
          id: message.id.toString(),
          text: message.message || '',
          hasMedia: !!message.media,
          mediaType: message.media ? message.media.className : null,
          replyToMsgId: message.replyTo ? message.replyTo.replyToMsgId : null
        }
      });

      // Update or create scraped member if not exists
      await this.updateOrCreateMember(userId, accountId, groupId, sender);
    } catch (error) {
      console.error('Error handling message for activity tracking:', error);
    }
  }

  /**
   * Update user activity record
   */
  async updateUserActivity(userId, accountId, groupId, senderId, activityData) {
    try {
      // Find existing activity
      let activity = await UserActivity.findOne({
        where: {
          userId,
          telegramAccountId: accountId,
          groupId,
          memberId: senderId
        }
      });

      if (activity) {
        // Update existing activity
        await activity.update({
          messageCount: activity.messageCount + (activityData.messageCount || 0),
          reactionCount: activity.reactionCount + (activityData.reactionCount || 0),
          lastActive: new Date(),
          lastMessageDate: activityData.lastMessageDate || activity.lastMessageDate,
          lastReactionDate: activityData.lastReactionDate || activity.lastReactionDate,
          // Add message to history (limited to last 50)
          messageHistory: activityData.messageData ? 
            [activityData.messageData, ...activity.messageHistory.slice(0, 49)] : 
            activity.messageHistory
        });
      } else {
        // Create new activity record
        await UserActivity.create({
          userId,
          telegramAccountId: accountId,
          groupId,
          memberId: senderId,
          messageCount: activityData.messageCount || 0,
          reactionCount: activityData.reactionCount || 0,
          firstSeen: new Date(),
          lastActive: new Date(),
          lastMessageDate: activityData.lastMessageDate || null,
          lastReactionDate: activityData.lastReactionDate || null,
          messageHistory: activityData.messageData ? [activityData.messageData] : [],
          engagementScore: 0
        });
      }
    } catch (error) {
      console.error('Error updating user activity:', error);
    }
  }

  /**
   * Update or create member record
   */
  async updateOrCreateMember(userId, accountId, groupId, sender) {
    try {
      // Check if member already exists
      let member = await ScrapedMember.findOne({
        where: { 
          userId,
          telegramId: sender.id.toString()
        }
      });

      if (!member) {
        // Create new member record
        await ScrapedMember.create({
          userId,
          telegramAccountId: accountId,
          telegramId: sender.id.toString(),
          username: sender.username,
          firstName: sender.firstName,
          lastName: sender.lastName,
          isBot: sender.bot || false,
          isVerified: sender.verified || false,
          isPremium: sender.premium || false,
          sourceGroupId: groupId,
          sourceGroupTitle: '',  // Will be updated later
          sourceGroupUsername: '',  // Will be updated later
          status: 'active'
        });
      } else {
        // Update existing member with latest info
        await member.update({
          username: sender.username || member.username,
          firstName: sender.firstName || member.firstName,
          lastName: sender.lastName || member.lastName,
          isBot: sender.bot || member.isBot,
          isVerified: sender.verified || member.isVerified,
          isPremium: sender.premium || member.isPremium,
          lastActivity: new Date()
        });
      }
    } catch (error) {
      console.error('Error updating member record:', error);
    }
  }

  /**
   * Stop tracking user activity
   */
  async stopTracking(accountId, groupId) {
    try {
      const trackerId = `${accountId}-${groupId}`;
      const tracker = this.activeTrackers.get(trackerId);
      
      if (!tracker) {
        return { success: false, error: 'No active tracking for this group' };
      }
      
      // Disconnect client
      await tracker.client.disconnect();
      this.activeTrackers.delete(trackerId);
      
      return { success: true, message: 'Stopped user activity tracking' };
    } catch (error) {
      console.error(`Error stopping tracking for account ${accountId}, group ${groupId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get tracking status
   */
  getTrackingStatus(accountId, groupId) {
    const trackerId = `${accountId}-${groupId}`;
    const tracker = this.activeTrackers.get(trackerId);
    
    if (!tracker) {
      return { active: false };
    }
    
    return {
      active: true,
      startTime: tracker.startTime,
      groupTitle: tracker.group.title || tracker.group.username,
      uptime: Math.floor((new Date() - tracker.startTime) / 1000)
    };
  }

  /**
   * Calculate engagement scores for all members in a group
   */
  async calculateEngagementScores(userId, groupId) {
    try {
      const activities = await UserActivity.findAll({
        where: {
          userId,
          groupId
        },
        order: [['messageCount', 'DESC']]
      });
      
      if (activities.length === 0) {
        return { success: false, error: 'No activity data found for this group' };
      }
      
      // Get max values for normalization
      const maxMessages = Math.max(...activities.map(a => a.messageCount));
      const maxReactions = Math.max(...activities.map(a => a.reactionCount));
      const now = new Date();
      
      // Calculate recency score (more recent = higher score)
      const oldestActivity = Math.min(
        ...activities.map(a => a.lastActive ? now - a.lastActive : now - a.firstSeen)
      );
      
      // Update engagement scores
      let updated = 0;
      for (const activity of activities) {
        // Calculate components (0-100 scale)
        const messageScore = maxMessages > 0 ? (activity.messageCount / maxMessages) * 50 : 0;
        const reactionScore = maxReactions > 0 ? (activity.reactionCount / maxReactions) * 20 : 0;
        
        // Recency score (0-30)
        const timeSinceLastActive = activity.lastActive ? 
          now - activity.lastActive : 
          now - activity.firstSeen;
        const recencyScore = oldestActivity > 0 ? 
          (1 - (timeSinceLastActive / oldestActivity)) * 30 : 
          0;
        
        // Calculate total score (0-100)
        const totalScore = Math.min(100, Math.round(messageScore + reactionScore + recencyScore));
        
        // Update the record
        await activity.update({
          engagementScore: totalScore,
          lastScoreUpdate: now
        });
        
        updated++;
      }
      
      return { 
        success: true, 
        message: `Updated ${updated} engagement scores`,
        memberCount: activities.length
      };
    } catch (error) {
      console.error(`Error calculating engagement scores for group ${groupId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get most active users in a group
   */
  async getMostActiveUsers(userId, groupId, limit = 10) {
    try {
      const activities = await UserActivity.findAll({
        where: {
          userId,
          groupId
        },
        order: [['engagementScore', 'DESC']],
        limit: limit,
        include: [{
          model: ScrapedMember,
          as: 'member',
          attributes: ['id', 'username', 'firstName', 'lastName', 'telegramId']
        }]
      });
      
      return activities;
    } catch (error) {
      console.error(`Error getting most active users for group ${groupId}:`, error);
      return [];
    }
  }

  /**
   * Get inactive users in a group (no activity for X days)
   */
  async getInactiveUsers(userId, groupId, dayThreshold = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - dayThreshold);
      
      const activities = await UserActivity.findAll({
        where: {
          userId,
          groupId,
          lastActive: {
            [Op.lt]: cutoffDate
          }
        },
        order: [['lastActive', 'ASC']],
        include: [{
          model: ScrapedMember,
          as: 'member',
          attributes: ['id', 'username', 'firstName', 'lastName', 'telegramId']
        }]
      });
      
      return activities;
    } catch (error) {
      console.error(`Error getting inactive users for group ${groupId}:`, error);
      return [];
    }
  }
}

module.exports = new UserActivityService(); 