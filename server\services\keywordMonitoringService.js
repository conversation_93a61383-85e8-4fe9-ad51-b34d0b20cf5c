const { Telegram<PERSON>pi, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');
const { KeywordMonitor, TelegramAccount, MonitoredKeyword } = require('../models');
const { Op } = require('sequelize');

class KeywordMonitoringService {
  constructor() {
    this.activeMonitors = new Map();
    console.log('Keyword Monitoring Service initialized');
  }

  /**
   * Start monitoring for keywords in specified groups
   */
  async startMonitoring(monitorId) {
    try {
      // Check if already monitoring
      if (this.activeMonitors.has(monitorId)) {
        return { success: true, message: 'Already monitoring' };
      }

      const monitor = await KeywordMonitor.findByPk(monitorId, {
        include: ['telegramAccount', 'keywords']
      });

      if (!monitor) {
        return { success: false, error: 'Monitor not found' };
      }

      const account = monitor.telegramAccount;
      if (!account || !account.sessionString) {
        return { success: false, error: 'Account not found or not authenticated' };
      }

      if (!monitor.keywords || monitor.keywords.length === 0) {
        return { success: false, error: 'No keywords defined for this monitor' };
      }

      // Initialize Telegram client
      const apiId = parseInt(process.env.TELEGRAM_API_ID);
      const apiHash = process.env.TELEGRAM_API_HASH;
      
      const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);
      await client.connect();

      // Get groups to monitor
      let groups = [];
      if (monitor.monitorAllGroups) {
        // Get all dialogs (chats) the user is part of
        const dialogs = await client.getDialogs();
        groups = dialogs.filter(dialog => 
          dialog.entity.className === 'Channel' || 
          dialog.entity.className === 'Chat' ||
          dialog.entity.className === 'Megagroup'
        ).map(dialog => dialog.entity);
      } else if (monitor.targetGroups && monitor.targetGroups.length > 0) {
        // Get specific groups
        for (const groupId of monitor.targetGroups) {
          try {
            const group = await client.getEntity(groupId);
            groups.push(group);
          } catch (error) {
            console.error(`Failed to get group ${groupId}:`, error);
          }
        }
      }

      if (groups.length === 0) {
        await client.disconnect();
        return { success: false, error: 'No groups to monitor' };
      }

      // Start listening for new messages
      client.addEventHandler(async (event) => {
        try {
          await this.handleNewMessage(event, monitor, client);
        } catch (error) {
          console.error(`Error handling message for monitor ${monitorId}:`, error);
        }
      }, new Api.events.NewMessage());

      // Store client in active monitors map
      this.activeMonitors.set(monitorId, {
        client,
        monitor,
        groups,
        startTime: new Date()
      });

      // Update monitor status
      await monitor.update({
        status: 'active',
        lastStarted: new Date()
      });

      console.log(`Started keyword monitoring for monitor ${monitorId}`);
      return { 
        success: true, 
        message: 'Started keyword monitoring',
        groupCount: groups.length
      };
    } catch (error) {
      console.error(`Error starting monitoring for monitor ${monitorId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle new incoming message and check for keywords
   */
  async handleNewMessage(event, monitor, client) {
    const message = event.message;
    
    // Skip own messages
    if (message.fromId?.userId === client.session.userId) {
      return;
    }

    // Get message text
    const messageText = message.message || '';
    if (!messageText) return;

    // Check against each keyword
    for (const keyword of monitor.keywords) {
      try {
        const matches = this.checkMessageForKeyword(messageText, keyword);
        
        if (matches) {
          // Get chat and sender information
          const chat = await message.getChat();
          const sender = await message.getSender();
          
          // Create detection record
          await this.createKeywordDetection({
            monitorId: monitor.id,
            keywordId: keyword.id,
            messageId: message.id.toString(),
            messageText,
            chatId: chat.id.toString(),
            chatTitle: chat.title || chat.username || 'Private Chat',
            chatType: this.getChatType(chat),
            senderId: sender ? sender.id.toString() : null,
            senderName: this.getSenderName(sender),
            senderUsername: sender ? sender.username : null,
            messageDate: new Date(message.date * 1000),
            matchedText: matches.matchedText,
            messageLink: this.generateMessageLink(chat, message.id)
          });
          
          // Perform notification if configured
          if (monitor.notificationsEnabled) {
            await this.sendNotification(monitor, keyword, message, chat, sender);
          }
          
          // Update keyword stats
          await keyword.increment('detectionCount');
          await keyword.update({ lastDetected: new Date() });
          
          // Auto-respond if configured
          if (keyword.autoRespondEnabled && keyword.autoResponseText) {
            await this.sendAutoResponse(client, message, keyword);
          }
        }
      } catch (error) {
        console.error(`Error processing keyword ${keyword.id}:`, error);
      }
    }
  }

  /**
   * Check if message contains keyword
   */
  checkMessageForKeyword(messageText, keyword) {
    const { keywordText, matchType, caseSensitive } = keyword;
    
    // Prepare message text based on case sensitivity
    const processedMessage = caseSensitive ? messageText : messageText.toLowerCase();
    const processedKeyword = caseSensitive ? keywordText : keywordText.toLowerCase();
    
    let isMatch = false;
    let matchedText = null;
    
    switch (matchType) {
      case 'exact':
        isMatch = processedMessage === processedKeyword;
        if (isMatch) matchedText = keywordText;
        break;
        
      case 'contains':
        isMatch = processedMessage.includes(processedKeyword);
        if (isMatch) matchedText = keywordText;
        break;
        
      case 'regex':
        try {
          const regex = new RegExp(keywordText, caseSensitive ? '' : 'i');
          const matches = messageText.match(regex);
          isMatch = !!matches;
          if (isMatch && matches.length > 0) {
            matchedText = matches[0];
          }
        } catch (error) {
          console.error('Invalid regex in keyword:', error);
        }
        break;
        
      default:
        isMatch = processedMessage.includes(processedKeyword);
        if (isMatch) matchedText = keywordText;
    }
    
    return isMatch ? { isMatch, matchedText } : false;
  }

  /**
   * Create keyword detection record
   */
  async createKeywordDetection(detectionData) {
    try {
      const { KeywordDetection } = require('../models');
      await KeywordDetection.create(detectionData);
    } catch (error) {
      console.error('Error creating keyword detection:', error);
    }
  }

  /**
   * Get chat type
   */
  getChatType(chat) {
    if (!chat) return 'unknown';
    
    if (chat.className === 'Channel') {
      return 'channel';
    } else if (chat.className === 'Chat') {
      return 'group';
    } else if (chat.className === 'User') {
      return 'private';
    } else if (chat.megagroup) {
      return 'supergroup';
    }
    
    return chat.className.toLowerCase();
  }

  /**
   * Get sender name
   */
  getSenderName(sender) {
    if (!sender) return 'Unknown';
    
    if (sender.firstName && sender.lastName) {
      return `${sender.firstName} ${sender.lastName}`;
    } else if (sender.firstName) {
      return sender.firstName;
    } else if (sender.title) {
      return sender.title;
    }
    
    return sender.username || 'Unknown';
  }

  /**
   * Generate message link
   */
  generateMessageLink(chat, messageId) {
    if (!chat || !chat.username) return null;
    
    return `https://t.me/${chat.username}/${messageId}`;
  }

  /**
   * Send notification about keyword detection
   */
  async sendNotification(monitor, keyword, message, chat, sender) {
    try {
      if (!monitor.notificationMethod || !monitor.notificationTarget) {
        return;
      }
      
      const chatName = chat.title || chat.username || 'Private Chat';
      const senderName = this.getSenderName(sender);
      const messageLink = this.generateMessageLink(chat, message.id);
      
      const notificationText = `🔍 Keyword detected: "${keyword.keywordText}"\n\n` +
        `💬 Message: ${message.message.substring(0, 100)}${message.message.length > 100 ? '...' : ''}\n` +
        `👤 From: ${senderName}\n` +
        `👥 In: ${chatName}\n` +
        `🕒 At: ${new Date(message.date * 1000).toLocaleString()}\n` +
        (messageLink ? `🔗 Link: ${messageLink}` : '');
      
      switch (monitor.notificationMethod) {
        case 'telegram':
          // Send Telegram message to specified user/group
          const client = this.activeMonitors.get(monitor.id).client;
          await client.sendMessage(monitor.notificationTarget, { message: notificationText });
          break;
          
        case 'webhook':
          // Send webhook notification
          const axios = require('axios');
          await axios.post(monitor.notificationTarget, {
            keyword: keyword.keywordText,
            message: message.message,
            chat: chatName,
            sender: senderName,
            timestamp: message.date,
            link: messageLink
          });
          break;
      }
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  /**
   * Send auto-response to message
   */
  async sendAutoResponse(client, message, keyword) {
    try {
      if (!keyword.autoResponseText) return;
      
      // Apply delay if specified
      if (keyword.autoResponseDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, keyword.autoResponseDelay * 1000));
      }
      
      // Send the response
      await client.sendMessage(message.peerId, {
        message: keyword.autoResponseText,
        replyTo: message.id
      });
    } catch (error) {
      console.error('Error sending auto-response:', error);
    }
  }

  /**
   * Stop monitoring
   */
  async stopMonitoring(monitorId) {
    try {
      const activeMonitor = this.activeMonitors.get(monitorId);
      if (!activeMonitor) {
        return { success: false, error: 'No active monitoring found' };
      }
      
      // Disconnect client
      await activeMonitor.client.disconnect();
      this.activeMonitors.delete(monitorId);
      
      // Update monitor status
      await activeMonitor.monitor.update({
        status: 'stopped',
        lastStopped: new Date()
      });
      
      return { success: true, message: 'Stopped keyword monitoring' };
    } catch (error) {
      console.error(`Error stopping monitoring for monitor ${monitorId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(monitorId) {
    const activeMonitor = this.activeMonitors.get(monitorId);
    if (!activeMonitor) {
      return { active: false };
    }
    
    return {
      active: true,
      startTime: activeMonitor.startTime,
      groupCount: activeMonitor.groups.length,
      keywordCount: activeMonitor.monitor.keywords.length,
      uptime: Math.floor((new Date() - activeMonitor.startTime) / 1000)
    };
  }

  /**
   * Get all active monitors
   */
  getActiveMonitors() {
    return Array.from(this.activeMonitors.keys());
  }
}

module.exports = new KeywordMonitoringService(); 