const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const MessageTemplate = sequelize.define('MessageTemplate', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  variables: {
    type: DataTypes.JSON,
    defaultValue: [],
    comment: 'Available variables like {firstName}, {lastName}, etc.'
  },
  category: {
    type: DataTypes.ENUM('welcome', 'promotion', 'notification', 'custom'),
    defaultValue: 'custom'
  },
  mediaType: {
    type: DataTypes.ENUM('text', 'photo', 'video', 'document', 'audio'),
    defaultValue: 'text'
  },
  mediaPath: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  usageCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastUsed: {
    type: DataTypes.DATE,
    allowNull: true
  }
});

// Instance methods
MessageTemplate.prototype.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsed = new Date();
  return this.save();
};

MessageTemplate.prototype.renderContent = function(variables = {}) {
  let content = this.content;
  
  // Replace template variables
  Object.keys(variables).forEach(key => {
    const regex = new RegExp(`{${key}}`, 'g');
    content = content.replace(regex, variables[key] || '');
  });
  
  return content;
};

module.exports = MessageTemplate;
