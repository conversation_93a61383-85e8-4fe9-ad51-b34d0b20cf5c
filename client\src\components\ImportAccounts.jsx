import React, { useState } from 'react';
import { Upload, FileText, Database, AlertCircle, CheckCircle, X } from 'lucide-react';

const ImportAccounts = ({ onClose, onImportSuccess }) => {
  const [importType, setImportType] = useState('json');
  const [file, setFile] = useState(null);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [accountName, setAccountName] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    setFile(selectedFile);
    setError('');
    setResult(null);
  };

  const handleImport = async () => {
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    if (importType === 'tdata' && !phoneNumber) {
      setError('Phone number is required for tdata import');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      const formData = new FormData();
      
      if (importType === 'json') {
        formData.append('accountFile', file);
      } else if (importType === 'tdata') {
        formData.append('tdataFiles', file);
        formData.append('phoneNumber', phoneNumber);
        formData.append('accountName', accountName);
      } else if (importType === 'bulk') {
        formData.append('accountsFile', file);
      }

      const endpoint = importType === 'json' 
        ? '/api/telegram/accounts/import-json'
        : importType === 'tdata'
        ? '/api/telegram/accounts/import-tdata'
        : '/api/telegram/accounts/bulk-import';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
        if (onImportSuccess) {
          onImportSuccess(data);
        }
      } else {
        setError(data.error || 'Import failed');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getFileAccept = () => {
    switch (importType) {
      case 'json':
      case 'bulk':
        return '.json';
      case 'tdata':
        return '*';
      default:
        return '.json';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Import Telegram Accounts</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        {/* Import Type Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Import Type
          </label>
          <select
            value={importType}
            onChange={(e) => setImportType(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="json">Single Account (JSON)</option>
            <option value="bulk">Multiple Accounts (JSON Array)</option>
            <option value="tdata">Telegram Desktop (tdata)</option>
          </select>
        </div>

        {/* File Upload */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select File
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
            <input
              type="file"
              onChange={handleFileChange}
              accept={getFileAccept()}
              className="hidden"
              id="file-upload"
              multiple={importType === 'tdata'}
            />
            <label htmlFor="file-upload" className="cursor-pointer">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
              <p className="text-sm text-gray-600">
                {file ? file.name : 'Click to select file(s)'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {importType === 'json' && 'JSON file with account data'}
                {importType === 'bulk' && 'JSON file with array of accounts'}
                {importType === 'tdata' && 'Telegram Desktop tdata files'}
              </p>
            </label>
          </div>
        </div>

        {/* Additional Fields for tdata */}
        {importType === 'tdata' && (
          <>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number *
              </label>
              <input
                type="text"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder="+**********"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Account Name (Optional)
              </label>
              <input
                type="text"
                value={accountName}
                onChange={(e) => setAccountName(e.target.value)}
                placeholder="My Account"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            <div className="flex items-center">
              <AlertCircle size={16} className="mr-2" />
              {error}
            </div>
          </div>
        )}

        {/* Success Display */}
        {result && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            <div className="flex items-center mb-2">
              <CheckCircle size={16} className="mr-2" />
              {result.message}
            </div>
            {result.results && (
              <div className="text-sm">
                <p>Imported: {result.results.imported}</p>
                {result.results.duplicates > 0 && (
                  <p>Duplicates: {result.results.duplicates}</p>
                )}
                {result.results.failed > 0 && (
                  <p>Failed: {result.results.failed}</p>
                )}
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleImport}
            disabled={loading || !file}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Importing...
              </>
            ) : (
              <>
                <Database size={16} className="mr-2" />
                Import
              </>
            )}
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-4 text-xs text-gray-500">
          <p className="font-medium mb-1">Supported formats:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>JSON: Single account with sessionString and phoneNumber</li>
            <li>Bulk JSON: Array of account objects</li>
            <li>tdata: Telegram Desktop session files (experimental)</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ImportAccounts;
