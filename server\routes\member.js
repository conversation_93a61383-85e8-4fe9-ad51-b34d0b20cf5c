const express = require('express');
const { Op } = require('sequelize');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { TelegramAccount, ScrapedMember } = require('../models');
const { TelegramApi, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');
const memberScrapingService = require('../services/memberScrapingService');
const multer = require('multer');
const fs = require('fs').promises;
const csv = require('csv-parser');
const path = require('path');

const router = express.Router();

// Get scraped members
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 50, search, sourceGroup } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { userId: req.user.id };
    
    if (search) {
      whereClause[Op.or] = [
        { username: { [Op.like]: `%${search}%` } },
        { firstName: { [Op.like]: `%${search}%` } },
        { lastName: { [Op.like]: `%${search}%` } }
      ];
    }

    if (sourceGroup) {
      whereClause.sourceGroupUsername = sourceGroup;
    }

    const { count, rows } = await ScrapedMember.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['id', 'accountName', 'phoneNumber']
      }]
    });

    res.json({
      members: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get members error:', error);
    res.status(500).json({ error: 'Failed to fetch members' });
  }
});

// Scrape members from group
router.post('/scrape', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const {
      accountId,
      groupUsername,
      limit = 1000,
      includeInactive = false,
      skipBots = true
    } = req.body;

    if (!accountId || !groupUsername) {
      return res.status(400).json({ error: 'Account ID and group username are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    if (!account.sessionString) {
      return res.status(400).json({ error: 'Account not verified' });
    }

    // Check if user has API credentials
    if (!req.user.telegramApiId || !req.user.telegramApiHash) {
      return res.status(400).json({
        error: 'Please configure your Telegram API credentials first',
        requiresApiSetup: true
      });
    }

    try {
      // Use the improved scraping service with user's credentials
      const result = await memberScrapingService.scrapeMembers(account, groupUsername, {
        limit: parseInt(limit),
        includeInactive,
        skipBots,
        user: req.user, // Pass user for API credentials
        onProgress: (current, total) => {
          // Could emit progress via socket.io here
          console.log(`Scraping progress: ${current}/${total}`);
        }
      });

      res.json({
        message: `Successfully scraped ${result.scrapedCount} new members`,
        scrapedCount: result.scrapedCount,
        totalFound: result.totalFound,
        duplicates: result.duplicates,
        errors: result.errors.length > 0 ? result.errors : undefined
      });

    } catch (telegramError) {
      console.error('Telegram scraping error:', telegramError);

      if (telegramError.message.includes('CHAT_ADMIN_REQUIRED')) {
        res.status(400).json({ error: 'Admin rights required to access group members. You need to be an admin or the group must allow member list access.' });
      } else if (telegramError.message.includes('USERNAME_NOT_OCCUPIED')) {
        res.status(400).json({ error: 'Group not found. Please check the username and try again.' });
      } else if (telegramError.message.includes('USERNAME_INVALID')) {
        res.status(400).json({ error: 'Invalid group username format.' });
      } else if (telegramError.message.includes('CHANNEL_PRIVATE')) {
        res.status(400).json({ error: 'This is a private group. You need to join the group first.' });
      } else if (telegramError.message.includes('FLOOD_WAIT')) {
        const waitTime = telegramError.message.match(/FLOOD_WAIT_(\d+)/);
        res.status(429).json({ error: `Rate limited. Please wait ${waitTime ? waitTime[1] : '60'} seconds before trying again.` });
      } else if (telegramError.message.includes('AUTH_KEY_INVALID')) {
        res.status(401).json({ error: 'Account session expired. Please re-verify your account.' });
      } else {
        res.status(500).json({
          error: 'Failed to scrape members from group',
          details: telegramError.message
        });
      }
    }

  } catch (error) {
    console.error('Scrape members error:', error);
    res.status(500).json({ error: 'Failed to scrape members' });
  }
});

// Get group information
router.post('/group-info', authenticateToken, async (req, res) => {
  try {
    const { accountId, groupUsername } = req.body;

    if (!accountId || !groupUsername) {
      return res.status(400).json({ error: 'Account ID and group username are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    if (!account.sessionString) {
      return res.status(400).json({ error: 'Account not verified' });
    }

    // Check if user has API credentials
    if (!req.user.telegramApiId || !req.user.telegramApiHash) {
      return res.status(400).json({
        error: 'Please configure your Telegram API credentials first',
        requiresApiSetup: true
      });
    }

    try {
      const groupInfo = await memberScrapingService.getGroupInfo(account, groupUsername, req.user);
      res.json({ groupInfo });
    } catch (error) {
      console.error('Get group info error:', error);

      if (error.message.includes('Group not found')) {
        res.status(404).json({ error: 'Group not found. Please check the username.' });
      } else if (error.message.includes('CHANNEL_PRIVATE')) {
        res.status(400).json({ error: 'This is a private group. You need to join the group first.' });
      } else {
        res.status(500).json({ error: 'Failed to get group information' });
      }
    }

  } catch (error) {
    console.error('Group info error:', error);
    res.status(500).json({ error: 'Failed to get group information' });
  }
});

// Add members to group
router.post('/add', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { accountId, groupUsername, memberIds, delay = 30 } = req.body;

    if (!accountId || !groupUsername || !memberIds || !Array.isArray(memberIds)) {
      return res.status(400).json({ error: 'Account ID, group username, and member IDs are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    // Check daily limits
    await account.resetDailyCounters();
    if (account.dailyMembersAdded + memberIds.length > req.limits.dailyAdds) {
      return res.status(403).json({ 
        error: `Daily add limit exceeded. You can add ${req.limits.dailyAdds - account.dailyMembersAdded} more members today.` 
      });
    }

    // Initialize Telegram client
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    
    const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);

    try {
      await client.connect();

      const group = await client.getEntity(groupUsername);
      
      let addedCount = 0;
      const errors = [];

      for (const memberId of memberIds) {
        try {
          const member = await ScrapedMember.findOne({
            where: { id: memberId, userId: req.user.id }
          });

          if (!member) {
            errors.push(`Member ${memberId} not found`);
            continue;
          }

          // Try to add member
          await client.invoke(new Api.channels.InviteToChannel({
            channel: group,
            users: [member.telegramId]
          }));

          // Update member record
          const addedGroups = member.addedToGroups || [];
          addedGroups.push({
            groupId: group.id.toString(),
            groupTitle: group.title,
            groupUsername: groupUsername,
            addedAt: new Date()
          });

          await member.update({ addedToGroups: addedGroups });
          await account.incrementMemberCount();

          addedCount++;

          // Delay between adds
          if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay * 1000));
          }

        } catch (addError) {
          console.error(`Failed to add member ${memberId}:`, addError);
          errors.push(`Failed to add member ${memberId}: ${addError.message}`);
        }
      }

      await client.disconnect();

      res.json({
        message: `Successfully added ${addedCount} members`,
        addedCount,
        totalAttempted: memberIds.length,
        errors: errors.length > 0 ? errors : undefined
      });

    } catch (telegramError) {
      await client.disconnect();
      console.error('Telegram add error:', telegramError);
      res.status(500).json({ error: 'Failed to add members to group' });
    }

  } catch (error) {
    console.error('Add members error:', error);
    res.status(500).json({ error: 'Failed to add members' });
  }
});

// Export members to CSV
router.get('/export', authenticateToken, async (req, res) => {
  try {
    const { sourceGroup } = req.query;
    
    const whereClause = { userId: req.user.id };
    if (sourceGroup) {
      whereClause.sourceGroupUsername = sourceGroup;
    }

    const members = await ScrapedMember.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']]
    });

    // Convert to CSV format
    const csvHeader = 'ID,Username,First Name,Last Name,Phone,Telegram ID,Source Group,Status,Created At\n';
    const csvData = members.map(member => 
      `${member.id},"${member.username || ''}","${member.firstName || ''}","${member.lastName || ''}","${member.phoneNumber || ''}",${member.telegramId},"${member.sourceGroupTitle || ''}","${member.status}","${member.createdAt}"`
    ).join('\n');

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="scraped_members.csv"');
    res.send(csvHeader + csvData);

  } catch (error) {
    console.error('Export members error:', error);
    res.status(500).json({ error: 'Failed to export members' });
  }
});

// Configure multer for member imports
const upload = multer({
  dest: 'uploads/temp/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.json', '.csv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext) || file.mimetype === 'application/json' || file.mimetype === 'text/csv') {
      cb(null, true);
    } else {
      cb(new Error('Only JSON and CSV files are allowed'));
    }
  }
});

// Import members from JSON/CSV file
router.post('/import', authenticateToken, checkSubscription, upload.single('membersFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { sourceGroupTitle, sourceGroupUsername } = req.body;
    const fileExtension = path.extname(req.file.originalname).toLowerCase();

    let membersData = [];

    try {
      if (fileExtension === '.json') {
        // Parse JSON file
        const fileContent = await fs.readFile(req.file.path, 'utf8');
        const jsonData = JSON.parse(fileContent);
        membersData = Array.isArray(jsonData) ? jsonData : [jsonData];
      } else if (fileExtension === '.csv') {
        // Parse CSV file
        membersData = await parseCSVFile(req.file.path);
      }
    } catch (parseError) {
      await fs.unlink(req.file.path);
      return res.status(400).json({ error: 'Invalid file format or corrupted file' });
    }

    if (membersData.length === 0) {
      await fs.unlink(req.file.path);
      return res.status(400).json({ error: 'No valid member data found in file' });
    }

    const results = {
      imported: 0,
      duplicates: 0,
      errors: []
    };

    for (const [index, memberData] of membersData.entries()) {
      try {
        // Validate required fields
        if (!memberData.telegramId && !memberData.username) {
          results.errors.push(`Row ${index + 1}: Missing telegramId or username`);
          continue;
        }

        // Check if member already exists
        const whereClause = { userId: req.user.id };
        if (memberData.telegramId) {
          whereClause.telegramId = memberData.telegramId.toString();
        } else if (memberData.username) {
          whereClause.username = memberData.username;
        }

        const existingMember = await ScrapedMember.findOne({ where: whereClause });

        if (existingMember) {
          results.duplicates++;
          continue;
        }

        // Create member record
        await ScrapedMember.create({
          userId: req.user.id,
          telegramAccountId: null, // Imported members don't have associated account
          telegramId: memberData.telegramId?.toString() || null,
          username: memberData.username || null,
          firstName: memberData.firstName || memberData.first_name || null,
          lastName: memberData.lastName || memberData.last_name || null,
          phoneNumber: memberData.phoneNumber || memberData.phone || null,
          isBot: memberData.isBot || memberData.is_bot || false,
          isVerified: memberData.isVerified || memberData.is_verified || false,
          isPremium: memberData.isPremium || memberData.is_premium || false,
          sourceGroupId: memberData.sourceGroupId || null,
          sourceGroupTitle: sourceGroupTitle || memberData.sourceGroupTitle || 'Imported',
          sourceGroupUsername: sourceGroupUsername || memberData.sourceGroupUsername || null,
          status: 'imported',
          notes: 'Imported from file'
        });

        results.imported++;

      } catch (memberError) {
        results.errors.push(`Row ${index + 1}: ${memberError.message}`);
      }
    }

    // Clean up uploaded file
    await fs.unlink(req.file.path);

    res.json({
      message: `Import completed. ${results.imported} members imported, ${results.duplicates} duplicates skipped.`,
      results
    });

  } catch (error) {
    // Clean up uploaded file on error
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Failed to clean up file:', unlinkError);
      }
    }

    console.error('Import members error:', error);
    res.status(500).json({ error: 'Failed to import members' });
  }
});

// Helper function to parse CSV file
function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    const stream = require('fs').createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

module.exports = router;
