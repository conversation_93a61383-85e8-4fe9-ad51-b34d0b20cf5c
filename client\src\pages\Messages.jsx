import React, { useState, useEffect } from 'react';
import api from '../services/api';
import toast from 'react-hot-toast';
import {
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';

const Messages = () => {
  const [accounts, setAccounts] = useState([]);
  const [members, setMembers] = useState([]);
  const [messageHistory, setMessageHistory] = useState([]);
  const [activeTab, setActiveTab] = useState('single');
  const [loading, setLoading] = useState(false);
  
  const [singleMessage, setSingleMessage] = useState({
    accountId: '',
    targetType: 'user',
    targetId: '',
    message: ''
  });

  const [bulkMessage, setBulkMessage] = useState({
    accountId: '',
    message: '',
    delay: 30,
    selectedMembers: []
  });

  useEffect(() => {
    fetchAccounts();
    fetchMembers();
    fetchMessageHistory();
  }, []);

  const fetchAccounts = async () => {
    try {
      const response = await api.getTelegramAccounts();
      setAccounts(response.data.accounts.filter(acc => acc.isActive));
    } catch (error) {
      console.error('Failed to fetch accounts:', error);
    }
  };

  const fetchMembers = async () => {
    try {
      const response = await api.getScrapedMembers({ limit: 1000 });
      setMembers(response.data.members);
    } catch (error) {
      console.error('Failed to fetch members:', error);
    }
  };

  const fetchMessageHistory = async () => {
    try {
      const response = await api.getMessageHistory();
      setMessageHistory(response.data.accounts);
    } catch (error) {
      console.error('Failed to fetch message history:', error);
    }
  };

  const handleSendSingleMessage = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      await api.sendMessage(
        singleMessage.accountId,
        singleMessage.targetType,
        singleMessage.targetId,
        singleMessage.message
      );
      
      toast.success('Message sent successfully');
      setSingleMessage({ accountId: '', targetType: 'user', targetId: '', message: '' });
      fetchMessageHistory();
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error(error.response?.data?.error || 'Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  const handleSendBulkMessage = async (e) => {
    e.preventDefault();
    if (bulkMessage.selectedMembers.length === 0) {
      toast.error('Please select members to send messages to');
      return;
    }

    setLoading(true);

    try {
      const targets = bulkMessage.selectedMembers.map(memberId => {
        const member = members.find(m => m.id === memberId);
        return {
          type: 'member',
          id: memberId,
          firstName: member?.firstName,
          lastName: member?.lastName,
          username: member?.username
        };
      });

      await api.sendBulkMessages(
        bulkMessage.accountId,
        targets,
        bulkMessage.message,
        { delay: bulkMessage.delay }
      );
      
      toast.success(`Bulk messages sent to ${targets.length} members`);
      setBulkMessage({ accountId: '', message: '', delay: 30, selectedMembers: [] });
      fetchMessageHistory();
    } catch (error) {
      console.error('Failed to send bulk messages:', error);
      toast.error(error.response?.data?.error || 'Failed to send bulk messages');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectMember = (memberId) => {
    setBulkMessage(prev => ({
      ...prev,
      selectedMembers: prev.selectedMembers.includes(memberId)
        ? prev.selectedMembers.filter(id => id !== memberId)
        : [...prev.selectedMembers, memberId]
    }));
  };

  const handleSelectAllMembers = () => {
    setBulkMessage(prev => ({
      ...prev,
      selectedMembers: prev.selectedMembers.length === members.length 
        ? [] 
        : members.map(m => m.id)
    }));
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
        <p className="mt-1 text-sm text-gray-600">
          Send messages to individuals or groups of members
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('single')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'single'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Single Message
          </button>
          <button
            onClick={() => setActiveTab('bulk')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'bulk'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Bulk Messages
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'history'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Message History
          </button>
        </nav>
      </div>

      {/* Single Message Tab */}
      {activeTab === 'single' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Send Single Message</h3>
          <form onSubmit={handleSendSingleMessage} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">Telegram Account</label>
                <select
                  required
                  className="mt-1 input-field"
                  value={singleMessage.accountId}
                  onChange={(e) => setSingleMessage({...singleMessage, accountId: e.target.value})}
                >
                  <option value="">Select account</option>
                  {accounts.map(account => (
                    <option key={account.id} value={account.id}>
                      {account.accountName} ({account.phoneNumber})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Target Type</label>
                <select
                  className="mt-1 input-field"
                  value={singleMessage.targetType}
                  onChange={(e) => setSingleMessage({...singleMessage, targetType: e.target.value})}
                >
                  <option value="user">User</option>
                  <option value="group">Group</option>
                  <option value="channel">Channel</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Target {singleMessage.targetType === 'user' ? 'Username/ID' : 'Username'}
              </label>
              <input
                type="text"
                required
                className="mt-1 input-field"
                placeholder={singleMessage.targetType === 'user' ? '@username or user_id' : '@groupname'}
                value={singleMessage.targetId}
                onChange={(e) => setSingleMessage({...singleMessage, targetId: e.target.value})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Message</label>
              <textarea
                required
                rows={4}
                className="mt-1 input-field"
                placeholder="Enter your message..."
                value={singleMessage.message}
                onChange={(e) => setSingleMessage({...singleMessage, message: e.target.value})}
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              ) : (
                <PaperAirplaneIcon className="h-5 w-5 mr-2" />
              )}
              Send Message
            </button>
          </form>
        </div>
      )}

      {/* Bulk Messages Tab */}
      {activeTab === 'bulk' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Bulk Message Settings</h3>
            <form onSubmit={handleSendBulkMessage} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Telegram Account</label>
                  <select
                    required
                    className="mt-1 input-field"
                    value={bulkMessage.accountId}
                    onChange={(e) => setBulkMessage({...bulkMessage, accountId: e.target.value})}
                  >
                    <option value="">Select account</option>
                    {accounts.map(account => (
                      <option key={account.id} value={account.id}>
                        {account.accountName} ({account.phoneNumber})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Delay Between Messages (seconds)</label>
                  <input
                    type="number"
                    min="1"
                    max="300"
                    className="mt-1 input-field"
                    value={bulkMessage.delay}
                    onChange={(e) => setBulkMessage({...bulkMessage, delay: parseInt(e.target.value)})}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Message</label>
                <textarea
                  required
                  rows={4}
                  className="mt-1 input-field"
                  placeholder="Enter your message... Use {firstName}, {lastName}, {username} for personalization"
                  value={bulkMessage.message}
                  onChange={(e) => setBulkMessage({...bulkMessage, message: e.target.value})}
                />
                <p className="mt-1 text-xs text-gray-500">
                  You can use variables: {'{firstName}'}, {'{lastName}'}, {'{username}'}
                </p>
              </div>

              <button
                type="submit"
                disabled={loading || bulkMessage.selectedMembers.length === 0}
                className="btn-primary flex items-center"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                ) : (
                  <PaperAirplaneIcon className="h-5 w-5 mr-2" />
                )}
                Send to {bulkMessage.selectedMembers.length} Members
              </button>
            </form>
          </div>

          {/* Member Selection */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Select Members</h3>
              <button
                onClick={handleSelectAllMembers}
                className="btn-secondary text-sm"
              >
                {bulkMessage.selectedMembers.length === members.length ? 'Deselect All' : 'Select All'}
              </button>
            </div>

            {members.length === 0 ? (
              <div className="text-center py-6">
                <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No members available</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Scrape some members first to send bulk messages.
                </p>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto">
                <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
                  {members.map((member) => (
                    <label key={member.id} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <input
                        type="checkbox"
                        checked={bulkMessage.selectedMembers.includes(member.id)}
                        onChange={() => handleSelectMember(member.id)}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {member.firstName} {member.lastName}
                        </div>
                        <div className="text-xs text-gray-500">
                          {member.username ? `@${member.username}` : `ID: ${member.telegramId}`}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Message History Tab */}
      {activeTab === 'history' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Message Statistics</h3>
          {messageHistory.length === 0 ? (
            <div className="text-center py-6">
              <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No message history</h3>
              <p className="mt-1 text-sm text-gray-500">
                Start sending messages to see statistics here.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Account
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Messages Today
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Messages
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Activity
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {messageHistory.map((account) => (
                    <tr key={account.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {account.accountName}
                        </div>
                        <div className="text-sm text-gray-500">{account.phoneNumber}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {account.dailyMessagesSent || 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {account.totalMessagesSent || 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {account.lastActivity ? new Date(account.lastActivity).toLocaleString() : 'Never'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Messages;
