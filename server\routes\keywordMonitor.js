const express = require('express');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { KeywordMonitor, MonitoredKeyword, KeywordDetection, TelegramAccount } = require('../models');
const keywordMonitoringService = require('../services/keywordMonitoringService');
const { Op } = require('sequelize');

const router = express.Router();

// Get all keyword monitors for user
router.get('/monitors', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 50, status } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { userId: req.user.id };
    if (status) whereClause.status = status;

    const { count, rows } = await KeywordMonitor.findAndCountAll({
      where: whereClause,
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['id', 'accountName', 'phoneNumber']
      }],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    // Get active status for each monitor
    const monitorsWithStatus = await Promise.all(rows.map(async (monitor) => {
      const status = keywordMonitoringService.getMonitoringStatus(monitor.id);
      return {
        ...monitor.toJSON(),
        isCurrentlyActive: status.active
      };
    }));

    res.json({
      monitors: monitorsWithStatus,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get keyword monitors error:', error);
    res.status(500).json({ error: 'Failed to fetch keyword monitors' });
  }
});

// Create new keyword monitor
router.post('/monitors', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const {
      telegramAccountId,
      name,
      description,
      monitorAllGroups,
      targetGroups,
      excludedGroups,
      notificationsEnabled,
      notificationMethod,
      notificationTarget
    } = req.body;

    if (!telegramAccountId || !name) {
      return res.status(400).json({ error: 'Account ID and monitor name are required' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    // Create monitor
    const monitor = await KeywordMonitor.create({
      userId: req.user.id,
      telegramAccountId,
      name,
      description,
      monitorAllGroups: monitorAllGroups || false,
      targetGroups: targetGroups || [],
      excludedGroups: excludedGroups || [],
      notificationsEnabled: notificationsEnabled !== false,
      notificationMethod,
      notificationTarget
    });

    res.status(201).json({
      message: 'Keyword monitor created successfully',
      monitor
    });
  } catch (error) {
    console.error('Create keyword monitor error:', error);
    res.status(500).json({ error: 'Failed to create keyword monitor' });
  }
});

// Update keyword monitor
router.put('/monitors/:id', authenticateToken, async (req, res) => {
  try {
    const monitorId = req.params.id;
    const {
      name,
      description,
      monitorAllGroups,
      targetGroups,
      excludedGroups,
      notificationsEnabled,
      notificationMethod,
      notificationTarget
    } = req.body;

    const monitor = await KeywordMonitor.findOne({
      where: { id: monitorId, userId: req.user.id }
    });

    if (!monitor) {
      return res.status(404).json({ error: 'Monitor not found' });
    }

    await monitor.update({
      name,
      description,
      monitorAllGroups,
      targetGroups,
      excludedGroups,
      notificationsEnabled,
      notificationMethod,
      notificationTarget
    });

    res.json({
      message: 'Keyword monitor updated successfully',
      monitor
    });
  } catch (error) {
    console.error('Update keyword monitor error:', error);
    res.status(500).json({ error: 'Failed to update keyword monitor' });
  }
});

// Delete keyword monitor
router.delete('/monitors/:id', authenticateToken, async (req, res) => {
  try {
    const monitorId = req.params.id;

    const monitor = await KeywordMonitor.findOne({
      where: { id: monitorId, userId: req.user.id }
    });

    if (!monitor) {
      return res.status(404).json({ error: 'Monitor not found' });
    }

    // Stop monitoring if active
    await keywordMonitoringService.stopMonitoring(monitorId);

    // Delete associated keywords
    await MonitoredKeyword.destroy({
      where: { monitorId }
    });

    // Delete monitor
    await monitor.destroy();

    res.json({ message: 'Keyword monitor deleted successfully' });
  } catch (error) {
    console.error('Delete keyword monitor error:', error);
    res.status(500).json({ error: 'Failed to delete keyword monitor' });
  }
});

// Get keywords for a monitor
router.get('/monitors/:id/keywords', authenticateToken, async (req, res) => {
  try {
    const monitorId = req.params.id;

    const monitor = await KeywordMonitor.findOne({
      where: { id: monitorId, userId: req.user.id }
    });

    if (!monitor) {
      return res.status(404).json({ error: 'Monitor not found' });
    }

    const keywords = await MonitoredKeyword.findAll({
      where: { monitorId },
      order: [['priority', 'DESC'], ['createdAt', 'DESC']]
    });

    res.json({ keywords });
  } catch (error) {
    console.error('Get keywords error:', error);
    res.status(500).json({ error: 'Failed to fetch keywords' });
  }
});

// Add keyword to monitor
router.post('/monitors/:id/keywords', authenticateToken, async (req, res) => {
  try {
    const monitorId = req.params.id;
    const {
      keywordText,
      description,
      matchType,
      caseSensitive,
      priority,
      autoRespondEnabled,
      autoResponseText,
      autoResponseDelay
    } = req.body;

    if (!keywordText) {
      return res.status(400).json({ error: 'Keyword text is required' });
    }

    const monitor = await KeywordMonitor.findOne({
      where: { id: monitorId, userId: req.user.id }
    });

    if (!monitor) {
      return res.status(404).json({ error: 'Monitor not found' });
    }

    // Create keyword
    const keyword = await MonitoredKeyword.create({
      monitorId,
      keywordText,
      description,
      matchType: matchType || 'contains',
      caseSensitive: caseSensitive || false,
      priority: priority || 5,
      autoRespondEnabled: autoRespondEnabled || false,
      autoResponseText,
      autoResponseDelay: autoResponseDelay || 0
    });

    res.status(201).json({
      message: 'Keyword added successfully',
      keyword
    });
  } catch (error) {
    console.error('Add keyword error:', error);
    res.status(500).json({ error: 'Failed to add keyword' });
  }
});

// Update keyword
router.put('/keywords/:id', authenticateToken, async (req, res) => {
  try {
    const keywordId = req.params.id;
    const {
      keywordText,
      description,
      matchType,
      caseSensitive,
      priority,
      autoRespondEnabled,
      autoResponseText,
      autoResponseDelay,
      isActive
    } = req.body;

    const keyword = await MonitoredKeyword.findByPk(keywordId, {
      include: [{
        model: KeywordMonitor,
        as: 'monitor',
        where: { userId: req.user.id }
      }]
    });

    if (!keyword) {
      return res.status(404).json({ error: 'Keyword not found' });
    }

    await keyword.update({
      keywordText,
      description,
      matchType,
      caseSensitive,
      priority,
      autoRespondEnabled,
      autoResponseText,
      autoResponseDelay,
      isActive
    });

    res.json({
      message: 'Keyword updated successfully',
      keyword
    });
  } catch (error) {
    console.error('Update keyword error:', error);
    res.status(500).json({ error: 'Failed to update keyword' });
  }
});

// Delete keyword
router.delete('/keywords/:id', authenticateToken, async (req, res) => {
  try {
    const keywordId = req.params.id;

    const keyword = await MonitoredKeyword.findByPk(keywordId, {
      include: [{
        model: KeywordMonitor,
        as: 'monitor',
        where: { userId: req.user.id }
      }]
    });

    if (!keyword) {
      return res.status(404).json({ error: 'Keyword not found' });
    }

    await keyword.destroy();

    res.json({ message: 'Keyword deleted successfully' });
  } catch (error) {
    console.error('Delete keyword error:', error);
    res.status(500).json({ error: 'Failed to delete keyword' });
  }
});

// Start monitoring
router.post('/monitors/:id/start', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const monitorId = req.params.id;

    const monitor = await KeywordMonitor.findOne({
      where: { id: monitorId, userId: req.user.id }
    });

    if (!monitor) {
      return res.status(404).json({ error: 'Monitor not found' });
    }

    const result = await keywordMonitoringService.startMonitoring(monitorId);
    
    if (result.success) {
      res.json({ message: result.message, details: result });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Start monitoring error:', error);
    res.status(500).json({ error: 'Failed to start monitoring' });
  }
});

// Stop monitoring
router.post('/monitors/:id/stop', authenticateToken, async (req, res) => {
  try {
    const monitorId = req.params.id;

    const monitor = await KeywordMonitor.findOne({
      where: { id: monitorId, userId: req.user.id }
    });

    if (!monitor) {
      return res.status(404).json({ error: 'Monitor not found' });
    }

    const result = await keywordMonitoringService.stopMonitoring(monitorId);
    
    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Stop monitoring error:', error);
    res.status(500).json({ error: 'Failed to stop monitoring' });
  }
});

// Get monitoring status
router.get('/monitors/:id/status', authenticateToken, async (req, res) => {
  try {
    const monitorId = req.params.id;

    const monitor = await KeywordMonitor.findOne({
      where: { id: monitorId, userId: req.user.id }
    });

    if (!monitor) {
      return res.status(404).json({ error: 'Monitor not found' });
    }

    const status = keywordMonitoringService.getMonitoringStatus(monitorId);
    res.json(status);
  } catch (error) {
    console.error('Get monitoring status error:', error);
    res.status(500).json({ error: 'Failed to get monitoring status' });
  }
});

// Get detections
router.get('/detections', authenticateToken, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 50, 
      monitorId, 
      keywordId,
      isHandled,
      startDate,
      endDate,
      chatId,
      senderId
    } = req.query;
    
    const offset = (page - 1) * limit;

    // Build where clause
    const whereClause = {};
    
    if (monitorId) {
      whereClause.monitorId = monitorId;
    } else {
      // If no specific monitor ID, get all monitors for this user
      const userMonitors = await KeywordMonitor.findAll({
        where: { userId: req.user.id },
        attributes: ['id']
      });
      
      whereClause.monitorId = {
        [Op.in]: userMonitors.map(m => m.id)
      };
    }
    
    if (keywordId) whereClause.keywordId = keywordId;
    if (isHandled !== undefined) whereClause.isHandled = isHandled === 'true';
    if (chatId) whereClause.chatId = chatId;
    if (senderId) whereClause.senderId = senderId;
    
    if (startDate || endDate) {
      whereClause.messageDate = {};
      if (startDate) whereClause.messageDate[Op.gte] = new Date(startDate);
      if (endDate) whereClause.messageDate[Op.lte] = new Date(endDate);
    }

    const { count, rows } = await KeywordDetection.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: MonitoredKeyword,
          as: 'keyword',
          attributes: ['id', 'keywordText', 'matchType']
        },
        {
          model: KeywordMonitor,
          as: 'monitor',
          attributes: ['id', 'name']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['messageDate', 'DESC']]
    });

    res.json({
      detections: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get detections error:', error);
    res.status(500).json({ error: 'Failed to fetch detections' });
  }
});

// Mark detection as handled
router.put('/detections/:id/handle', authenticateToken, async (req, res) => {
  try {
    const detectionId = req.params.id;
    const { notes } = req.body;

    const detection = await KeywordDetection.findByPk(detectionId, {
      include: [{
        model: KeywordMonitor,
        as: 'monitor',
        where: { userId: req.user.id }
      }]
    });

    if (!detection) {
      return res.status(404).json({ error: 'Detection not found' });
    }

    await detection.update({
      isHandled: true,
      handledAt: new Date(),
      notes
    });

    res.json({
      message: 'Detection marked as handled',
      detection
    });
  } catch (error) {
    console.error('Handle detection error:', error);
    res.status(500).json({ error: 'Failed to handle detection' });
  }
});

module.exports = router; 