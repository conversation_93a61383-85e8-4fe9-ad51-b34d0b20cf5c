import React, { useState, useEffect } from 'react';
import api from '../services/api';
import toast from 'react-hot-toast';
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

const Members = () => {
  const [members, setMembers] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    search: '',
    sourceGroup: '',
    page: 1,
    limit: 50
  });
  const [showScrapeModal, setShowScrapeModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [scrapeData, setScrapeData] = useState({
    accountId: '',
    groupUsername: '',
    limit: 1000
  });
  const [addData, setAddData] = useState({
    accountId: '',
    groupUsername: '',
    delay: 30
  });

  useEffect(() => {
    fetchMembers();
    fetchAccounts();
  }, [filters]);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const response = await api.getScrapedMembers(filters);
      setMembers(response.data.members);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Failed to fetch members:', error);
      toast.error('Failed to fetch members');
    } finally {
      setLoading(false);
    }
  };

  const fetchAccounts = async () => {
    try {
      const response = await api.getTelegramAccounts();
      setAccounts(response.data.accounts.filter(acc => acc.isActive));
    } catch (error) {
      console.error('Failed to fetch accounts:', error);
    }
  };

  const handleScrapeMembers = async (e) => {
    e.preventDefault();
    try {
      const response = await api.scrapeMembers(
        scrapeData.accountId,
        scrapeData.groupUsername,
        { limit: scrapeData.limit }
      );
      
      toast.success(`Successfully scraped ${response.data.scrapedCount} members`);
      setShowScrapeModal(false);
      setScrapeData({ accountId: '', groupUsername: '', limit: 1000 });
      fetchMembers();
    } catch (error) {
      console.error('Failed to scrape members:', error);
      toast.error(error.response?.data?.error || 'Failed to scrape members');
    }
  };

  const handleAddMembers = async (e) => {
    e.preventDefault();
    if (selectedMembers.length === 0) {
      toast.error('Please select members to add');
      return;
    }

    try {
      const response = await api.addMembersToGroup(
        addData.accountId,
        addData.groupUsername,
        selectedMembers,
        { delay: addData.delay }
      );
      
      toast.success(`Successfully added ${response.data.addedCount} members`);
      setShowAddModal(false);
      setSelectedMembers([]);
      setAddData({ accountId: '', groupUsername: '', delay: 30 });
    } catch (error) {
      console.error('Failed to add members:', error);
      toast.error(error.response?.data?.error || 'Failed to add members');
    }
  };

  const handleExportMembers = async () => {
    try {
      const response = await api.exportMembers(filters);
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'scraped_members.csv');
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      toast.success('Members exported successfully');
    } catch (error) {
      console.error('Failed to export members:', error);
      toast.error('Failed to export members');
    }
  };

  const handleSelectMember = (memberId) => {
    setSelectedMembers(prev => 
      prev.includes(memberId) 
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  const handleSelectAll = () => {
    if (selectedMembers.length === members.length) {
      setSelectedMembers([]);
    } else {
      setSelectedMembers(members.map(m => m.id));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Scraped Members</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage scraped Telegram group members
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowScrapeModal(true)}
            className="btn-primary flex items-center"
          >
            <UserGroupIcon className="h-5 w-5 mr-2" />
            Scrape Members
          </button>
          {selectedMembers.length > 0 && (
            <button
              onClick={() => setShowAddModal(true)}
              className="btn-secondary flex items-center"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Add to Group ({selectedMembers.length})
            </button>
          )}
          <button
            onClick={handleExportMembers}
            className="btn-secondary flex items-center"
          >
            <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card mb-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div>
            <label className="block text-sm font-medium text-gray-700">Search</label>
            <div className="mt-1 relative">
              <input
                type="text"
                className="input-field pl-10"
                placeholder="Search username, name..."
                value={filters.search}
                onChange={(e) => setFilters({...filters, search: e.target.value, page: 1})}
              />
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Source Group</label>
            <input
              type="text"
              className="mt-1 input-field"
              placeholder="Group username"
              value={filters.sourceGroup}
              onChange={(e) => setFilters({...filters, sourceGroup: e.target.value, page: 1})}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Per Page</label>
            <select
              className="mt-1 input-field"
              value={filters.limit}
              onChange={(e) => setFilters({...filters, limit: parseInt(e.target.value), page: 1})}
            >
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>
      </div>

      {/* Members Table */}
      {members.length === 0 ? (
        <div className="text-center py-12">
          <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No members found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Start by scraping members from a Telegram group.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowScrapeModal(true)}
              className="btn-primary"
            >
              Scrape Members
            </button>
          </div>
        </div>
      ) : (
        <div className="card overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedMembers.length === members.length}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Member
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source Group
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Added Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {members.map((member) => (
                  <tr key={member.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedMembers.includes(member.id)}
                        onChange={() => handleSelectMember(member.id)}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {member.firstName} {member.lastName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {member.username ? `@${member.username}` : `ID: ${member.telegramId}`}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {member.sourceGroupTitle || member.sourceGroupUsername}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                        member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {member.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(member.createdAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setFilters({...filters, page: Math.max(1, filters.page - 1)})}
                  disabled={filters.page === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setFilters({...filters, page: Math.min(pagination.pages, filters.page + 1)})}
                  disabled={filters.page === pagination.pages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{((filters.page - 1) * filters.limit) + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(filters.page * filters.limit, pagination.total)}
                    </span>{' '}
                    of <span className="font-medium">{pagination.total}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        onClick={() => setFilters({...filters, page})}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          page === filters.page
                            ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    ))}
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Scrape Members Modal */}
      {showScrapeModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={() => setShowScrapeModal(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleScrapeMembers}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Scrape Group Members</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Telegram Account</label>
                      <select
                        required
                        className="mt-1 input-field"
                        value={scrapeData.accountId}
                        onChange={(e) => setScrapeData({...scrapeData, accountId: e.target.value})}
                      >
                        <option value="">Select account</option>
                        {accounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.accountName} ({account.phoneNumber})
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Group Username</label>
                      <input
                        type="text"
                        required
                        className="mt-1 input-field"
                        placeholder="groupname or @groupname"
                        value={scrapeData.groupUsername}
                        onChange={(e) => setScrapeData({...scrapeData, groupUsername: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Limit</label>
                      <input
                        type="number"
                        min="1"
                        max="10000"
                        className="mt-1 input-field"
                        value={scrapeData.limit}
                        onChange={(e) => setScrapeData({...scrapeData, limit: parseInt(e.target.value)})}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button type="submit" className="btn-primary sm:ml-3">
                    Start Scraping
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowScrapeModal(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Add Members Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={() => setShowAddModal(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleAddMembers}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Add Members to Group ({selectedMembers.length} selected)
                  </h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Telegram Account</label>
                      <select
                        required
                        className="mt-1 input-field"
                        value={addData.accountId}
                        onChange={(e) => setAddData({...addData, accountId: e.target.value})}
                      >
                        <option value="">Select account</option>
                        {accounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.accountName} ({account.phoneNumber})
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Target Group Username</label>
                      <input
                        type="text"
                        required
                        className="mt-1 input-field"
                        placeholder="groupname or @groupname"
                        value={addData.groupUsername}
                        onChange={(e) => setAddData({...addData, groupUsername: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Delay Between Adds (seconds)</label>
                      <input
                        type="number"
                        min="1"
                        max="300"
                        className="mt-1 input-field"
                        value={addData.delay}
                        onChange={(e) => setAddData({...addData, delay: parseInt(e.target.value)})}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button type="submit" className="btn-primary sm:ml-3">
                    Add Members
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Members;
