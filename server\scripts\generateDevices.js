const fs = require('fs');
const path = require('path');

// Base device templates for massive generation
const deviceTemplates = {
  android: {
    brands: [
      'Samsung', 'Google', 'OnePlus', 'Xiaomi', 'Huawei', 'Oppo', 'Vivo', 'Sony', 'Motorola',
      'Nokia', 'LG', 'Realme', 'Honor', 'TCL', 'ZTE', 'Asus', 'Nothing', 'Fairphone',
      'Blackview', 'Ulefone', 'Doogee', 'Cubot', 'Oukitel', 'Umidigi', 'AGM', 'Cat',
      'Crosscall', 'Gigaset', 'Kyocera', 'Sonim', 'Caterpillar', 'Land Rover', 'Panasonic',
      'Zebra', 'Honeywell', 'Datalogic', 'Chainway', 'Point Mobile', 'Unitech', 'CipherLab',
      'Bluebird', 'Newland', 'Urovo', 'iData', 'Casio', 'Getac', 'Juniper Systems', 'Trimble',
      'Spectralink', 'Ascom', 'Energizer', 'Unihertz', 'Planet Computers', 'F(x)tec',
      'Cosmo Communicator', 'Astro Slide', 'YotaPhone', 'His<PERSON>', 'Sharp', 'Alcatel',
      'Meizu', 'Infinix', 'Tecno', 'Itel', 'Gionee', 'Coolpad', 'LeEco', 'Vernee',
      'Elephone', 'Bluboo', 'Homtom', 'Leagoo', 'Maze', 'Nomu', 'Poptel', 'Conquest',
      'Blackshark', 'Red Magic', 'ROG Phone', 'Legion Phone', 'iQOO', 'Poco', 'Redmi',
      'Mi', 'OnePlus Nord', 'Galaxy', 'Pixel', 'Xperia', 'Moto', 'Edge', 'Reno', 'Find',
      'V Series', 'X Series', 'Y Series', 'A Series', 'M Series', 'F Series', 'Note',
      'Pro', 'Max', 'Ultra', 'Lite', 'Plus', 'Mini', 'Compact', 'XL', 'SE'
    ],
    models: [
      'Galaxy S{num}', 'Galaxy Note {num}', 'Galaxy A{num}', 'Galaxy M{num}', 'Galaxy F{num}',
      'Galaxy Z{num}', 'Galaxy Tab S{num}', 'Galaxy Watch{num}', 'Galaxy Buds{num}',
      'Pixel {num}', 'Pixel {num} Pro', 'Pixel {num}a', 'Pixel {num} XL', 'Pixel Fold',
      'Pixel Watch', 'Pixel Buds', 'Pixel Tablet', 'Pixel Stand',
      'OnePlus {num}', 'OnePlus {num} Pro', 'OnePlus {num}T', 'OnePlus {num}R', 'Nord {num}',
      'Nord CE {num}', 'Nord N{num}', 'OnePlus Pad', 'OnePlus Watch', 'OnePlus Buds',
      'Mi {num}', 'Mi {num} Pro', 'Mi {num} Ultra', 'Mi {num} Lite', 'Redmi Note {num}',
      'Redmi {num}', 'Redmi K{num}', 'Poco F{num}', 'Poco X{num}', 'Poco M{num}',
      'Mi Mix {num}', 'Mi Max {num}', 'Mi Play', 'Mi Pad {num}', 'Mi Band {num}',
      'P{num}', 'P{num} Pro', 'Mate {num}', 'Mate {num} Pro', 'Nova {num}',
      'MatePad {num}', 'MateBook {num}', 'Watch GT {num}', 'FreeBuds {num}',
      'Reno {num}', 'Reno {num} Pro', 'Find X{num}', 'Find N{num}', 'A{num}',
      'K{num}', 'F{num}', 'R{num}', 'Pad Air', 'Watch {num}', 'Enco {num}',
      'X{num}', 'X{num} Pro', 'V{num}', 'V{num} Pro', 'Y{num}', 'S{num}',
      'T{num}', 'U{num}', 'Z{num}', 'iQOO {num}', 'iQOO Neo {num}', 'iQOO Z{num}',
      'Xperia {num}', 'Xperia {num} Pro', 'Xperia {num} Compact', 'Xperia {num} Mark',
      'WH-{num}', 'WF-{num}', 'SRS-{num}', 'FX{num}', 'MDR-{num}',
      'Edge {num}', 'Moto G{num}', 'Moto E{num}', 'Razr {num}', 'Moto X{num}',
      'ThinkPhone', 'Moto Tab G{num}', 'Moto Watch {num}', 'Moto Buds {num}',
      'G{num}', 'X{num}', '{num}.{num}', 'C{num}', '{num} Plus', '{num} Lite',
      'Wing', 'Velvet', 'V{num} ThinQ', 'G{num} ThinQ', 'K{num}', 'Stylo {num}',
      'Gram {num}', 'UltraGear {num}', 'Tone Free {num}', 'PuriCare {num}',
      'GT {num}', 'GT Neo {num}', '{num} Pro', '{num} Pro+', 'C{num}', 'Narzo {num}',
      'GT Master {num}', 'GT Explorer {num}', 'Book {num}', 'Pad {num}', 'Watch {num}',
      '{num}T', '{num}s', '{num}e', '{num}i', '{num}x', '{num}z', '{num}a', '{num}c'
    ],
    androidVersions: [
      '14', '13', '12', '11', '10', '9', '8.1', '8.0', '7.1', '7.0'
    ],
    telegramVersions: [
      '9.5.1', '9.4.2', '9.3.1', '9.2.4', '9.1.3', '9.0.2', '8.9.1', '8.8.1', '8.7.2'
    ]
  },
  ios: {
    models: [
      'iPhone 15 Pro Max', 'iPhone 15 Pro', 'iPhone 15 Plus', 'iPhone 15',
      'iPhone 14 Pro Max', 'iPhone 14 Pro', 'iPhone 14 Plus', 'iPhone 14',
      'iPhone 13 Pro Max', 'iPhone 13 Pro', 'iPhone 13 mini', 'iPhone 13',
      'iPhone 12 Pro Max', 'iPhone 12 Pro', 'iPhone 12 mini', 'iPhone 12',
      'iPhone 11 Pro Max', 'iPhone 11 Pro', 'iPhone 11',
      'iPhone XS Max', 'iPhone XS', 'iPhone XR', 'iPhone X',
      'iPhone 8 Plus', 'iPhone 8', 'iPhone SE 3rd Gen', 'iPhone SE 2nd Gen',
      'iPad Pro 12.9 6th Gen', 'iPad Pro 12.9 5th Gen', 'iPad Pro 11 4th Gen', 'iPad Pro 11 3rd Gen',
      'iPad Air 5th Gen', 'iPad Air 4th Gen', 'iPad 10th Gen', 'iPad 9th Gen',
      'iPad mini 6th Gen', 'iPad mini 5th Gen'
    ],
    deviceIds: [
      'iPhone15,3', 'iPhone15,2', 'iPhone14,8', 'iPhone14,7',
      'iPhone14,3', 'iPhone14,2', 'iPhone14,5', 'iPhone14,4',
      'iPhone13,4', 'iPhone13,3', 'iPhone13,1', 'iPhone13,2',
      'iPhone12,5', 'iPhone12,3', 'iPhone12,1',
      'iPhone11,6', 'iPhone11,2', 'iPhone11,8', 'iPhone10,6',
      'iPhone10,5', 'iPhone10,4', 'iPhone14,6', 'iPhone12,8',
      'iPad13,18', 'iPad13,8', 'iPad13,7', 'iPad13,4',
      'iPad13,16', 'iPad13,1', 'iPad12,1', 'iPad12,2',
      'iPad14,1', 'iPad11,1'
    ],
    iosVersions: [
      '17.0', '16.6', '16.2', '16.0', '15.7', '15.4', '15.0', '14.8', '14.1', '13.7'
    ],
    telegramVersions: [
      '9.5.1', '9.4.2', '9.3.1', '9.2.4', '9.1.3', '9.0.2', '8.9.1', '8.8.1'
    ]
  },
  desktop: {
    brands: [
      'MacBook Pro', 'MacBook Air', 'iMac', 'Mac Studio', 'Mac Pro', 'Mac mini',
      'Dell XPS', 'Dell Inspiron', 'Dell Latitude', 'Dell Precision', 'Dell Alienware',
      'HP Spectre', 'HP EliteBook', 'HP Pavilion', 'HP Envy', 'HP Omen', 'HP ZBook',
      'Lenovo ThinkPad', 'Lenovo IdeaPad', 'Lenovo Legion', 'Lenovo Yoga', 'Lenovo ThinkBook',
      'Asus ZenBook', 'Asus VivoBook', 'Asus ROG', 'Asus TUF', 'Asus ExpertBook', 'Asus ProArt',
      'Acer Swift', 'Acer Aspire', 'Acer Predator', 'Acer Nitro', 'Acer ConceptD', 'Acer TravelMate',
      'MSI GS', 'MSI GP', 'MSI Modern', 'MSI Creator', 'MSI Prestige', 'MSI Summit',
      'Razer Blade', 'Razer Book', 'Surface Laptop', 'Surface Pro', 'Surface Book', 'Surface Studio',
      'Framework Laptop', 'System76', 'Purism Librem', 'Tuxedo', 'Star Labs', 'Slimbook',
      'Pine64 Pinebook', 'Huawei MateBook', 'LG Gram', 'Samsung Galaxy Book'
    ],
    models: [
      '{num}', 'Pro {num}', 'Air {num}', '{num} M1', '{num} M2', '{num} Intel',
      '{num} 9{num}20', '{num} 9{num}30', '{num} 9{num}40', '{num} 9{num}50',
      '{num} 3{num}20', '{num} 5{num}20', '{num} 7{num}20', '{num} 9{num}20',
      'x360 {num}', '850 G{num}', '15-eh{num}000', '13-ba{num}000', '15-en{num}000',
      'X1 Carbon', 'T{num} Gen {num}', '5 15ITL05', '5 15ACH6H', '9i 14ITL5',
      '{num} UX425EA', '{num} X515EA', 'Strix G15 G5{num}3', 'Gaming A15', 'B9450FA',
      '{num} SF314-43', '{num} A515-56', 'Helios 300', '{num} AN515-57', '{num} CN715-71',
      'Stealth 10UH', 'Leopard 11UH', '{num} B11MO', '{num} A10SFS', '{num} A11SCX',
      '{num} Advanced', '{num} 2021', '{num} 2020', '{num} 4', '{num} 8', '{num} 3', '{num} 2',
      'Laptop', 'Galago Pro', 'Oryx Pro', '{num}', 'InfinityBook Pro {num}', 'StarBook Mk V',
      'Pro X {num}', 'Pinebook Pro', 'MateBook X Pro', 'MateBook {num}', 'Gram {num}Z90P', 'Galaxy Book{num}'
    ],
    systems: [
      'Windows 11', 'Windows 10', 'macOS 13', 'macOS 12', 'macOS 11', 'macOS 10.15',
      'Ubuntu 22.04', 'Ubuntu 20.04', 'Pop!_OS 22.04', 'Fedora 38', 'Debian 11',
      'Arch Linux', 'Manjaro', 'openSUSE 15.4', 'elementary OS 6', 'KDE neon 22.04',
      'Linux Mint 21', 'Zorin OS 16', 'EndeavourOS', 'Garuda Linux'
    ],
    versions: [
      '11.0.22000', '10.0.19044', '13.0.0', '12.6.0', '11.0.0', '10.15.0',
      '22.04.0', '20.04.0', '38.0.0', '11.0.0', '21.0.0', '15.4.0', '6.1.0', '16.0.0'
    ],
    telegramVersions: [
      '4.6.0', '4.5.2', '4.4.1', '4.3.4', '4.2.2', '4.1.1'
    ]
  }
};

function generateRandomDevices(count = 25000) {
  const devices = [];

  for (let i = 0; i < count; i++) {
    // Create realistic platform distribution for 25K devices
    let platform;
    const platformRand = i % 100;
    if (platformRand < 65) {
      platform = 'android'; // 65% Android (most popular)
    } else if (platformRand < 85) {
      platform = 'ios'; // 20% iOS
    } else {
      platform = 'desktop'; // 15% Desktop/Laptop
    }

    const template = deviceTemplates[platform];

    let device;
    if (platform === 'android') {
      device = generateAndroidDevice(template, i);
    } else if (platform === 'ios') {
      device = generateIOSDevice(template, i);
    } else if (platform === 'desktop') {
      device = generateDesktopDevice(template, i);
    }

    if (device) {
      devices.push(device);
    }
  }

  return devices;
}

function generateAndroidDevice(template, index) {
  const brand = template.brands[index % template.brands.length];
  const modelTemplate = template.models[index % template.models.length];
  const androidVersion = template.androidVersions[index % template.androidVersions.length];
  const telegramVersion = template.telegramVersions[index % template.telegramVersions.length];

  // Generate model number variations optimized for 25K devices
  const num1 = (index % 100) + 1; // Balanced range for quality variation
  const num2 = (index % 30) + 1;
  const num3 = (index % 8) + 1;
  const num4 = (index % 15) + 1;

  // Create more realistic model variations
  let modelName = modelTemplate.replace(/{num}/g, num1);

  // Add multiple layers of variations for massive diversity
  if (index % 7 === 0) {
    modelName = modelName.replace(/(\d+)/, `$1 ${['Plus', 'Pro', 'Max', 'Ultra', 'Lite', 'SE', 'FE', 'Neo'][num3 % 8]}`);
  }
  if (index % 11 === 0) {
    modelName = modelName.replace(/(\d+)/, `$1${['a', 'e', 's', 't', 'x', 'z', 'i', 'o', 'u'][num2 % 9]}`);
  }
  if (index % 13 === 0) {
    modelName = `${modelName} ${['5G', '4G', 'LTE', 'WiFi', 'Dual SIM'][num4 % 5]}`;
  }
  if (index % 17 === 0) {
    const year = 2018 + (index % 6);
    modelName = `${modelName} ${year}`;
  }
  if (index % 19 === 0) {
    const storage = [32, 64, 128, 256, 512, 1024][num3 % 6];
    modelName = `${modelName} ${storage}GB`;
  }

  // Generate device model ID with more variation
  const modelId = generateModelId(brand, modelName, index);

  // Add version variations with more granularity
  const versionVariation = (index % 10);
  const patchVersion = (index % 20);
  const fullVersion = `${androidVersion}.${versionVariation}.${patchVersion}`;

  return `${brand} ${modelName}|${modelId}|Android ${androidVersion}|${fullVersion}|${telegramVersion}|en|en-US|android`;
}

function generateIOSDevice(template, index) {
  const model = template.models[index % template.models.length];
  const deviceId = template.deviceIds[index % template.deviceIds.length];
  const iosVersion = template.iosVersions[index % template.iosVersions.length];
  const telegramVersion = template.telegramVersions[index % template.telegramVersions.length];

  // Add version variations for iOS
  const versionVariation = (index % 10);
  const fullVersion = `${iosVersion}.${versionVariation}`;

  const system = model.includes('iPad') ? `iPadOS ${iosVersion}` : `iOS ${iosVersion}`;

  // Create device ID variations
  let finalDeviceId = deviceId;
  if (index % 13 === 0) {
    finalDeviceId = deviceId.replace(/(\d+),(\d+)/, `$1,${(index % 9) + 1}`);
  }

  return `${model}|${finalDeviceId}|${system}|${fullVersion}|${telegramVersion}|en|en-US|ios`;
}

function generateDesktopDevice(template, index) {
  const brand = template.brands[index % template.brands.length];
  const modelTemplate = template.models[index % template.models.length];
  const system = template.systems[index % template.systems.length];
  const version = template.versions[index % template.versions.length];
  const telegramVersion = template.telegramVersions[index % template.telegramVersions.length];

  // Generate model variations with more diversity
  const num1 = (index % 30) + 1;
  const num2 = (index % 10) + 1;
  const year = 2018 + (index % 6); // 2018-2023

  let modelName = modelTemplate.replace(/{num}/g, num1);

  // Add year variations for laptops/desktops
  if (index % 17 === 0) {
    modelName = `${modelName} ${year}`;
  }

  // Add generation markers
  if (index % 19 === 0) {
    modelName = `${modelName} Gen ${num2 % 5 + 1}`;
  }

  // Create more realistic model IDs for desktops
  const modelId = `${brand.replace(/\s+/g, '')} ${modelName.replace(/\s+/g, '')}`;

  return `${brand} ${modelName}|${modelId}|${system}|${version}|${telegramVersion}|en|en-US|desktop`;
}

function generateModelId(brand, model, index) {
  const brandCodes = {
    'Samsung': 'SM-',
    'Google': 'Pixel ',
    'OnePlus': 'CPH',
    'Xiaomi': '22',
    'Huawei': 'ELS-',
    'Oppo': 'CPH',
    'Vivo': 'V2',
    'Sony': 'XQ-',
    'Motorola': 'XT',
    'Nokia': 'TA-',
    'LG': 'LM-',
    'Realme': 'RMX'
  };
  
  const code = brandCodes[brand] || brand.substring(0, 2).toUpperCase() + '-';
  const suffix = String(1000 + (index % 9000)).substring(1);
  
  if (brand === 'Google') {
    return code + model.split(' ')[1] || code + 'Device';
  }
  
  return code + suffix + (brand === 'Samsung' ? 'B' : '');
}

// Generate devices and append to file
function appendDevicesToFile(count = 80000) {
  console.log(`🚀 Generating ${count.toLocaleString()} additional devices...`);

  const newDevices = generateRandomDevices(count);
  const deviceLines = newDevices.join('\n');

  const filePath = path.join(__dirname, '../data/devices.txt');

  // Append to existing file
  fs.appendFileSync(filePath, '\n' + deviceLines);

  console.log(`✅ Added ${newDevices.length.toLocaleString()} devices to database`);

  // Count total lines
  const totalLines = fs.readFileSync(filePath, 'utf8').split('\n').filter(line => line.trim()).length;
  console.log(`📱 Total devices in database: ${totalLines.toLocaleString()}`);
}

// Generate fresh 25K device database
function generateFreshDatabase() {
  console.log('🚀 Creating fresh 25,000 device database...');

  const devices = generateRandomDevices(25000);
  const deviceLines = devices.join('\n');

  const filePath = path.join(__dirname, '../data/devices.txt');

  // Replace existing file with new optimized database
  fs.writeFileSync(filePath, deviceLines);

  console.log(`✅ Created ${devices.length.toLocaleString()} devices in database`);
  console.log(`📱 Total devices in database: ${devices.length.toLocaleString()}`);
}

// Run if called directly
if (require.main === module) {
  // Create optimized 25K device database
  generateFreshDatabase();
}

module.exports = { generateRandomDevices, appendDevicesToFile };
