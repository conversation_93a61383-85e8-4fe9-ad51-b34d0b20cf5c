const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:3002/api';
const SESSION_DATA_PATH = './+855715258054/+855715258054.json';
const TDATA_PATH = './+855715258054/tdata';

// User credentials - you'll need to login first or provide a valid JWT token
const USER_CREDENTIALS = {
  email: '<EMAIL>', // Change this to your email
  password: 'your-password' // Change this to your password
};

async function loginUser() {
  try {
    console.log('🔐 Logging in user...');
    const response = await axios.post(`${API_BASE_URL}/auth/login`, USER_CREDENTIALS);
    
    if (response.data.token) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      throw new Error('No token received');
    }
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    throw error;
  }
}

async function importTelegramSession(token) {
  try {
    console.log('📱 Reading session data...');
    
    // Read the JSON session file
    const sessionData = JSON.parse(fs.readFileSync(SESSION_DATA_PATH, 'utf8'));
    
    console.log('📋 Session data loaded:');
    console.log(`   Phone: ${sessionData.phone}`);
    console.log(`   Username: ${sessionData.username}`);
    console.log(`   First Name: ${sessionData.first_name}`);
    console.log(`   API ID: ${sessionData.app_id}`);
    
    // Prepare the import data
    const importData = {
      sessionData: {
        session_string: sessionData.session_file || sessionData.phone,
        api_id: sessionData.app_id,
        api_hash: sessionData.app_hash,
        phone: sessionData.phone,
        first_name: sessionData.first_name,
        last_name: sessionData.last_name,
        username: sessionData.username,
        device: sessionData.device,
        app_version: sessionData.app_version,
        sdk: sessionData.sdk,
        lang_code: sessionData.lang_code,
        system_lang_code: sessionData.system_lang_code
      },
      accountName: `${sessionData.first_name || 'Account'} (${sessionData.phone})`
    };
    
    console.log('📤 Importing session to system...');
    
    // Import via JSON endpoint
    const response = await axios.post(`${API_BASE_URL}/session-import/json`, importData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log('✅ Session imported successfully!');
      console.log(`   Account ID: ${response.data.account.id}`);
      console.log(`   Account Name: ${response.data.account.accountName}`);
      console.log(`   Phone Number: ${response.data.account.phoneNumber}`);
      console.log(`   Status: ${response.data.account.status}`);
      
      return response.data.account;
    } else {
      throw new Error(response.data.message || 'Import failed');
    }
    
  } catch (error) {
    console.error('❌ Session import failed:', error.response?.data?.error || error.message);
    throw error;
  }
}

async function verifyImportedAccount(token, accountId) {
  try {
    console.log('🔍 Verifying imported account...');
    
    const response = await axios.get(`${API_BASE_URL}/telegram/accounts/${accountId}/status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('📊 Account status:', response.data.status);
    return response.data;
    
  } catch (error) {
    console.error('⚠️ Account verification failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Telegram session import...\n');
    
    // Check if session file exists
    if (!fs.existsSync(SESSION_DATA_PATH)) {
      throw new Error(`Session file not found: ${SESSION_DATA_PATH}`);
    }
    
    // Check if tdata folder exists
    if (!fs.existsSync(TDATA_PATH)) {
      console.log('⚠️ TData folder not found, but continuing with JSON import');
    }
    
    // Step 1: Login
    const token = await loginUser();
    
    // Step 2: Import session
    const account = await importTelegramSession(token);
    
    // Step 3: Verify account (optional)
    await verifyImportedAccount(token, account.id);
    
    console.log('\n🎉 Import process completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Open your web application at http://localhost:3000');
    console.log('2. Go to "Telegram Accounts" section');
    console.log('3. Your imported account should be visible and ready to use');
    console.log('4. You can now use features like member scraping, messaging, etc.');
    
  } catch (error) {
    console.error('\n💥 Import process failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the server is running (npm run dev)');
    console.log('2. Update USER_CREDENTIALS in this script with valid login details');
    console.log('3. Ensure the session file path is correct');
    console.log('4. Check if you have a user account created in the system');
  }
}

// Run the import
main();
