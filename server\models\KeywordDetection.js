const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class KeywordDetection extends Model {}

KeywordDetection.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  monitorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'KeywordMonitors',
      key: 'id'
    }
  },
  keywordId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'MonitoredKeywords',
      key: 'id'
    }
  },
  messageId: {
    type: DataTypes.STRING,
    allowNull: false
  },
  messageText: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  chatId: {
    type: DataTypes.STRING,
    allowNull: false
  },
  chatTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  chatType: {
    type: DataTypes.STRING,
    allowNull: false
  },
  senderId: {
    type: DataTypes.STRING,
    allowNull: true
  },
  senderName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  senderUsername: {
    type: DataTypes.STRING,
    allowNull: true
  },
  messageDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  matchedText: {
    type: DataTypes.STRING,
    allowNull: true
  },
  messageLink: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isHandled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  handledAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'KeywordDetection',
  tableName: 'keyword_detections',
  timestamps: true,
  indexes: [
    {
      fields: ['monitor_id', 'message_date']
    },
    {
      fields: ['keyword_id']
    },
    {
      fields: ['chat_id']
    },
    {
      fields: ['sender_id']
    },
    {
      fields: ['is_handled']
    }
  ]
});

module.exports = KeywordDetection; 