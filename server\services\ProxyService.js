const axios = require('axios');
const { SocksProxyAgent } = require('socks-proxy-agent');
const { HttpsProxyAgent } = require('https-proxy-agent');

class ProxyService {
  constructor() {
    this.proxyPools = {
      residential: [], // High-quality residential proxies
      datacenter: [],  // Fast datacenter proxies
      mobile: []       // Mobile carrier proxies
    };
    
    this.proxyProviders = {
      // Popular proxy providers (users can add their own)
      brightdata: {
        endpoint: 'rotating-residential.brightdata.com',
        port: 22225,
        type: 'residential'
      },
      smartproxy: {
        endpoint: 'gate.smartproxy.com',
        port: 10000,
        type: 'residential'
      },
      oxylabs: {
        endpoint: 'pr.oxylabs.io',
        port: 7777,
        type: 'residential'
      }
    };

    this.geoLocations = [
      'US', 'UK', 'CA', 'AU', 'DE', 'FR', 'NL', 'IT', 'ES', 'SE',
      'NO', 'DK', 'FI', 'CH', 'AT', 'BE', 'IE', 'PT', 'GR', 'CZ'
    ];
  }

  /**
   * Add user's proxy configuration
   */
  async addUserProxy(userId, proxyConfig) {
    try {
      const { ProxyConfig } = require('../models');
      
      // Validate proxy before saving
      const isValid = await this.testProxy(proxyConfig);
      if (!isValid) {
        throw new Error('Proxy validation failed');
      }

      const proxy = await ProxyConfig.create({
        userId: userId,
        name: proxyConfig.name || 'Custom Proxy',
        type: proxyConfig.type || 'http',
        host: proxyConfig.host,
        port: proxyConfig.port,
        username: proxyConfig.username,
        password: proxyConfig.password,
        country: proxyConfig.country || 'US',
        isActive: true,
        lastTested: new Date(),
        responseTime: proxyConfig.responseTime || 0
      });

      return proxy;
    } catch (error) {
      console.error('Error adding user proxy:', error);
      throw error;
    }
  }

  /**
   * Get available proxies for user
   */
  async getUserProxies(userId, filters = {}) {
    try {
      const { ProxyConfig } = require('../models');
      
      const whereClause = {
        userId: userId,
        isActive: true
      };

      if (filters.country) {
        whereClause.country = filters.country;
      }

      if (filters.type) {
        whereClause.type = filters.type;
      }

      const proxies = await ProxyConfig.findAll({
        where: whereClause,
        order: [['responseTime', 'ASC'], ['lastTested', 'DESC']]
      });

      return proxies;
    } catch (error) {
      console.error('Error getting user proxies:', error);
      return [];
    }
  }

  /**
   * Get optimal proxy for account
   */
  async getOptimalProxy(userId, accountId, preferences = {}) {
    try {
      // Get user's proxies
      const userProxies = await this.getUserProxies(userId, preferences);
      
      if (userProxies.length === 0) {
        console.warn(`No proxies available for user ${userId}`);
        return null;
      }

      // Check if account has assigned proxy
      const { TelegramAccount } = require('../models');
      const account = await TelegramAccount.findByPk(accountId);
      
      if (account && account.proxyId) {
        const assignedProxy = userProxies.find(p => p.id === account.proxyId);
        if (assignedProxy) {
          return this.formatProxyConfig(assignedProxy);
        }
      }

      // Assign new proxy based on strategy
      const strategy = preferences.strategy || 'round_robin';
      let selectedProxy;

      switch (strategy) {
        case 'fastest':
          selectedProxy = userProxies[0]; // Already sorted by response time
          break;
        case 'random':
          selectedProxy = userProxies[Math.floor(Math.random() * userProxies.length)];
          break;
        case 'geographic':
          selectedProxy = this.selectByGeography(userProxies, preferences.targetCountry);
          break;
        default: // round_robin
          selectedProxy = await this.selectRoundRobin(userId, userProxies);
      }

      // Assign proxy to account
      if (account && selectedProxy) {
        await account.update({ proxyId: selectedProxy.id });
      }

      return selectedProxy ? this.formatProxyConfig(selectedProxy) : null;
    } catch (error) {
      console.error('Error getting optimal proxy:', error);
      return null;
    }
  }

  /**
   * Format proxy configuration for Telegram client
   */
  formatProxyConfig(proxy) {
    return {
      socksType: proxy.type === 'socks5' ? 5 : (proxy.type === 'socks4' ? 4 : null),
      addr: proxy.host,
      port: proxy.port,
      username: proxy.username,
      password: proxy.password,
      rdns: true, // Use remote DNS
      
      // Additional metadata
      country: proxy.country,
      responseTime: proxy.responseTime,
      proxyId: proxy.id
    };
  }

  /**
   * Test proxy connectivity and speed
   */
  async testProxy(proxyConfig, timeout = 10000) {
    try {
      const startTime = Date.now();
      
      // Create proxy agent
      let agent;
      if (proxyConfig.type === 'socks5' || proxyConfig.type === 'socks4') {
        const proxyUrl = `socks${proxyConfig.type === 'socks5' ? '5' : '4'}://${
          proxyConfig.username ? `${proxyConfig.username}:${proxyConfig.password}@` : ''
        }${proxyConfig.host}:${proxyConfig.port}`;
        agent = new SocksProxyAgent(proxyUrl);
      } else {
        const proxyUrl = `http://${
          proxyConfig.username ? `${proxyConfig.username}:${proxyConfig.password}@` : ''
        }${proxyConfig.host}:${proxyConfig.port}`;
        agent = new HttpsProxyAgent(proxyUrl);
      }

      // Test with multiple endpoints
      const testEndpoints = [
        'https://httpbin.org/ip',
        'https://api.ipify.org?format=json',
        'https://ipinfo.io/json'
      ];

      for (const endpoint of testEndpoints) {
        try {
          const response = await axios.get(endpoint, {
            httpsAgent: agent,
            httpAgent: agent,
            timeout: timeout,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
          });

          if (response.status === 200) {
            const responseTime = Date.now() - startTime;
            proxyConfig.responseTime = responseTime;
            
            // Extract IP information
            const data = response.data;
            proxyConfig.externalIp = data.ip || data.query || 'unknown';
            
            return true;
          }
        } catch (error) {
          console.warn(`Proxy test failed for endpoint ${endpoint}:`, error.message);
          continue;
        }
      }

      return false;
    } catch (error) {
      console.error('Proxy test error:', error);
      return false;
    }
  }

  /**
   * Rotate proxy for account
   */
  async rotateProxy(userId, accountId, reason = 'manual') {
    try {
      const { TelegramAccount } = require('../models');
      const account = await TelegramAccount.findByPk(accountId);
      
      if (!account || account.userId !== userId) {
        throw new Error('Account not found or access denied');
      }

      // Get current proxy
      const currentProxyId = account.proxyId;
      
      // Get available proxies (excluding current one)
      const availableProxies = await this.getUserProxies(userId);
      const otherProxies = availableProxies.filter(p => p.id !== currentProxyId);
      
      if (otherProxies.length === 0) {
        throw new Error('No alternative proxies available');
      }

      // Select new proxy
      const newProxy = otherProxies[Math.floor(Math.random() * otherProxies.length)];
      
      // Update account
      await account.update({ 
        proxyId: newProxy.id,
        proxyRotatedAt: new Date(),
        proxyRotationReason: reason
      });

      // Log rotation
      console.log(`Proxy rotated for account ${accountId}: ${currentProxyId} → ${newProxy.id} (${reason})`);

      return this.formatProxyConfig(newProxy);
    } catch (error) {
      console.error('Error rotating proxy:', error);
      throw error;
    }
  }

  /**
   * Select proxy by geography
   */
  selectByGeography(proxies, targetCountry) {
    if (!targetCountry) {
      return proxies[0];
    }

    const countryProxies = proxies.filter(p => p.country === targetCountry);
    if (countryProxies.length > 0) {
      return countryProxies[Math.floor(Math.random() * countryProxies.length)];
    }

    // Fallback to any proxy
    return proxies[0];
  }

  /**
   * Round-robin proxy selection
   */
  async selectRoundRobin(userId, proxies) {
    try {
      // Simple round-robin based on last used proxy
      const { TelegramAccount } = require('../models');
      const lastAccount = await TelegramAccount.findOne({
        where: { userId: userId },
        order: [['updatedAt', 'DESC']]
      });

      if (!lastAccount || !lastAccount.proxyId) {
        return proxies[0];
      }

      const lastProxyIndex = proxies.findIndex(p => p.id === lastAccount.proxyId);
      const nextIndex = (lastProxyIndex + 1) % proxies.length;
      
      return proxies[nextIndex];
    } catch (error) {
      console.error('Error in round-robin selection:', error);
      return proxies[0];
    }
  }

  /**
   * Monitor proxy health
   */
  async monitorProxyHealth() {
    try {
      const { ProxyConfig } = require('../models');
      const proxies = await ProxyConfig.findAll({
        where: { isActive: true }
      });

      for (const proxy of proxies) {
        const isHealthy = await this.testProxy(proxy, 5000);
        
        if (!isHealthy) {
          await proxy.update({
            isActive: false,
            lastError: 'Health check failed',
            lastTested: new Date()
          });
          
          console.warn(`Proxy ${proxy.id} marked as inactive due to health check failure`);
        } else {
          await proxy.update({
            lastTested: new Date(),
            lastError: null
          });
        }
      }
    } catch (error) {
      console.error('Error monitoring proxy health:', error);
    }
  }

  /**
   * Get proxy statistics
   */
  async getProxyStats(userId) {
    try {
      const { ProxyConfig, TelegramAccount } = require('../models');
      
      const stats = await ProxyConfig.findAll({
        where: { userId: userId },
        include: [{
          model: TelegramAccount,
          as: 'accounts',
          attributes: ['id', 'accountName', 'isActive']
        }],
        attributes: [
          'id', 'name', 'country', 'type', 'isActive', 
          'responseTime', 'lastTested', 'lastError'
        ]
      });

      return stats.map(proxy => ({
        ...proxy.toJSON(),
        accountCount: proxy.accounts ? proxy.accounts.length : 0,
        activeAccountCount: proxy.accounts ? proxy.accounts.filter(a => a.isActive).length : 0
      }));
    } catch (error) {
      console.error('Error getting proxy stats:', error);
      return [];
    }
  }
}

module.exports = new ProxyService();
