const axios = require('axios');
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// Configuration
const API_BASE_URL = 'http://localhost:3002/api';
const PHONE_FOLDER = './+************';  // Your phone number folder
const TDATA_FOLDER = path.join(PHONE_FOLDER, 'tdata');

// User credentials - use existing user
const USER_CREDENTIALS = {
  email: 'YaeIsTired',  // Existing user from logs
  password: 'password123'  // You'll need the actual password
};

async function loginUser() {
  try {
    console.log('🔐 Logging in as existing user...');
    const response = await axios.post(`${API_BASE_URL}/auth/login`, USER_CREDENTIALS);
    
    if (response.data.token) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      throw new Error('No token received');
    }
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    throw error;
  }
}

async function setApiCredentials(token) {
  try {
    console.log('🔑 Setting API credentials...');
    
    const response = await axios.post(`${API_BASE_URL}/users/api-credentials`, {
      telegramApiId: '2040',
      telegramApiHash: 'b18441a1ff607e10a989891a5462e627'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ API credentials set');
    return response.data;
    
  } catch (error) {
    console.error('❌ Failed to set API credentials:', error.response?.data?.error || error.message);
    // Continue anyway - might already be set
  }
}

async function createTDataZip() {
  try {
    console.log('📦 Creating TData zip file...');
    
    const zipPath = path.join(__dirname, 'tdata-import.zip');
    
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', { zlib: { level: 9 } });
      
      output.on('close', () => {
        console.log(`✅ TData zip created: ${archive.pointer()} bytes`);
        resolve(zipPath);
      });
      
      archive.on('error', reject);
      
      archive.pipe(output);
      
      // Add the entire phone number folder structure
      // This preserves the +************/tdata/ structure
      archive.directory(PHONE_FOLDER, path.basename(PHONE_FOLDER));
      
      archive.finalize();
    });
    
  } catch (error) {
    console.error('❌ Failed to create zip:', error);
    throw error;
  }
}

async function uploadTDataZip(token, zipPath) {
  try {
    console.log('📤 Uploading TData zip...');
    
    const FormData = require('form-data');
    const formData = new FormData();
    
    // Add the zip file
    formData.append('sessions', fs.createReadStream(zipPath), {
      filename: 'tdata.zip',
      contentType: 'application/zip'
    });
    
    // Add account name
    formData.append('accountName', 'Scot Zitzow (+************) - TData Import');
    
    const response = await axios.post(`${API_BASE_URL}/session-import/upload`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });
    
    if (response.data.success && response.data.results.length > 0) {
      const result = response.data.results[0];
      if (result.success) {
        console.log('✅ TData uploaded and imported successfully!');
        console.log(`   Account ID: ${result.accountId}`);
        console.log(`   Account Name: ${result.accountName}`);
        console.log(`   Phone: ${result.phoneNumber || '+************'}`);
        return { id: result.accountId, accountName: result.accountName };
      } else {
        throw new Error(result.error || 'TData import failed');
      }
    } else {
      throw new Error('TData upload failed');
    }
    
  } catch (error) {
    console.error('❌ TData upload failed:', error.response?.data?.error || error.message);
    throw error;
  }
}

async function verifyTDataImport(token, accountId) {
  try {
    console.log('🔍 Verifying TData import...');
    
    // Check account status
    const statusResponse = await axios.get(`${API_BASE_URL}/telegram/accounts/${accountId}/status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`📊 Account status: ${statusResponse.data.status}`);
    
    // Get account details
    const accountResponse = await axios.get(`${API_BASE_URL}/telegram/accounts`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const account = accountResponse.data.accounts.find(acc => acc.id == accountId);
    if (account) {
      console.log('📱 Account details:');
      console.log(`   Name: ${account.accountName}`);
      console.log(`   Phone: ${account.phoneNumber}`);
      console.log(`   Status: ${account.status}`);
      console.log(`   Active: ${account.isActive ? 'Yes' : 'No'}`);
      console.log(`   Online: ${account.isOnline ? 'Yes' : 'No'}`);
    }
    
    return statusResponse.data.status === 'online' || statusResponse.data.status === 'active';
    
  } catch (error) {
    console.error('❌ Verification failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testMemberScraping(token, accountId) {
  try {
    console.log('🕷️ Testing member scraping capability...');
    
    // Test with a public group
    const testGroup = '@telegram';  // Telegram's official channel
    
    const response = await axios.post(`${API_BASE_URL}/members/scrape`, {
      accountId: accountId,
      groupUsername: testGroup,
      limit: 5,  // Small test
      skipBots: true,
      includeInactive: false
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.scrapedCount > 0) {
      console.log(`✅ Successfully scraped ${response.data.scrapedCount} members!`);
      console.log('🎉 Your TData session is working for member scraping!');
      return true;
    } else {
      console.log('⚠️ No members scraped, but connection works');
      return true;
    }
    
  } catch (error) {
    console.error('❌ Scraping test failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting REAL TData Import Process...\n');
    
    // Validate TData structure
    console.log('📋 Validating TData structure:');
    console.log(`   Phone folder: ${fs.existsSync(PHONE_FOLDER) ? '✅' : '❌'} ${PHONE_FOLDER}`);
    console.log(`   TData folder: ${fs.existsSync(TDATA_FOLDER) ? '✅' : '❌'} ${TDATA_FOLDER}`);
    
    const keyDataPath = path.join(TDATA_FOLDER, 'key_datas');
    console.log(`   key_datas: ${fs.existsSync(keyDataPath) ? '✅' : '❌'} ${keyDataPath}`);
    
    const accountFolders = fs.readdirSync(TDATA_FOLDER)
      .filter(file => /^[A-F0-9]{16}$/.test(file));
    console.log(`   Account folders: ${accountFolders.length > 0 ? '✅' : '❌'} ${accountFolders.join(', ')}`);
    
    if (!fs.existsSync(keyDataPath)) {
      throw new Error('key_datas file is missing - TData is incomplete');
    }
    
    if (accountFolders.length === 0) {
      throw new Error('No account folders found - TData is incomplete');
    }
    
    console.log('\n✅ TData structure is valid!\n');
    
    // Step 1: Login
    const token = await loginUser();
    
    // Step 2: Set API credentials
    await setApiCredentials(token);
    
    // Step 3: Create TData zip
    const zipPath = await createTDataZip();
    
    // Step 4: Upload and import TData
    const account = await uploadTDataZip(token, zipPath);
    
    // Step 5: Verify import
    const isWorking = await verifyTDataImport(token, account.id);
    
    // Step 6: Test scraping (if working)
    if (isWorking) {
      await testMemberScraping(token, account.id);
    }
    
    // Cleanup
    fs.unlinkSync(zipPath);
    
    console.log('\n🎉 TData Import Complete!');
    console.log('\n📝 What you can do now:');
    console.log('1. Open http://localhost:3000');
    console.log('2. Login with your existing account');
    console.log('3. Go to "Telegram Accounts" - your TData account should be active');
    console.log('4. Go to "Members" - start scraping from groups');
    console.log('5. Use "Add Members" to transfer between groups');
    
    console.log('\n🔄 The Complete TData Workflow:');
    console.log('   TData Files → Session Import → Telegram Login → Group Access → Member Scraping → Member Transfer');
    
  } catch (error) {
    console.error('\n💥 TData import failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the server is running (npm run dev)');
    console.log('2. Check that your TData folder structure is complete');
    console.log('3. Verify you have the correct login credentials');
    console.log('4. Check server logs for detailed errors');
  }
}

// Run the TData import
main();
