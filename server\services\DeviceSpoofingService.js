const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class DeviceSpoofingService {
  constructor() {
    this.deviceDatabase = [];
    this.loadDeviceDatabase();

    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0'
    ];

    this.timezones = [
      'America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver',
      'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Europe/Rome',
      'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Kolkata', 'Asia/Dubai',
      'Australia/Sydney', 'Australia/Melbourne', 'Pacific/Auckland'
    ];
  }

  /**
   * Load device database from .txt file
   */
  loadDeviceDatabase() {
    try {
      const deviceFilePath = path.join(__dirname, '../data/devices.txt');
      const deviceData = fs.readFileSync(deviceFilePath, 'utf8');
      const lines = deviceData.split('\n').filter(line => line.trim());

      this.deviceDatabase = lines.map(line => {
        const parts = line.split('|');
        if (parts.length >= 8) {
          return {
            device_name: parts[0].trim(),
            device_model: parts[1].trim(),
            system: parts[2].trim(),
            system_version: parts[3].trim(),
            app_version: parts[4].trim(),
            lang_code: parts[5].trim(),
            system_lang_code: parts[6].trim(),
            lang_pack: parts[7].trim()
          };
        }
        return null;
      }).filter(device => device !== null);

      console.log(`✅ Loaded ${this.deviceDatabase.length} device fingerprints from database`);
    } catch (error) {
      console.error('❌ Error loading device database:', error);
      // Fallback to minimal device set
      this.deviceDatabase = [
        {
          device_name: 'Samsung Galaxy S21',
          device_model: 'SM-G991B',
          system: 'Android 12',
          system_version: '12.0.0',
          app_version: '9.4.2',
          lang_code: 'en',
          system_lang_code: 'en-US',
          lang_pack: 'android'
        }
      ];
    }
  }

  /**
   * Get devices by category for random selection
   */
  getDevicesByCategory(category = null) {
    if (!category) {
      return this.deviceDatabase;
    }

    const categoryMap = {
      'mobile': ['android', 'ios'],
      'desktop': ['desktop'],
      'laptop': ['desktop'], // Laptops are also desktop category
      'android': ['android'],
      'ios': ['ios'],
      'windows': ['desktop'],
      'macos': ['desktop'],
      'linux': ['desktop']
    };

    const platforms = categoryMap[category.toLowerCase()] || [category.toLowerCase()];
    return this.deviceDatabase.filter(device =>
      platforms.includes(device.lang_pack.toLowerCase())
    );
  }

  /**
   * Generate a random device fingerprint for a user account with device type preference
   */
  generateDeviceFingerprint(userId, accountId, preferredCategory = null) {
    // Create deterministic but unique seed for this user+account combo
    const seed = crypto.createHash('sha256')
      .update(`${userId}-${accountId}-device`)
      .digest('hex');

    // Use seed to make reproducible "random" choices
    const seedInt = parseInt(seed.substring(0, 8), 16);

    // Get devices by category (mobile, desktop, laptop, android, ios, etc.)
    let availableDevices = this.getDevicesByCategory(preferredCategory);

    // Fallback to all devices if no matches
    if (availableDevices.length === 0) {
      availableDevices = this.deviceDatabase;
    }

    // Add randomness while keeping deterministic for same user+account
    const randomSeed = parseInt(seed.substring(8, 16), 16);
    const deviceIndex = (seedInt + randomSeed) % availableDevices.length;

    // Choose device template
    const template = availableDevices[deviceIndex];
    
    // Generate unique variations
    const fingerprint = {
      // Device information from database
      device_name: template.device_name,
      device_model: template.device_model,
      system: template.system,
      system_version: template.system_version,
      app_version: template.app_version,
      lang_code: template.lang_code,
      system_lang_code: template.system_lang_code,
      lang_pack: template.lang_pack,

      // Add unique identifiers
      device_id: this.generateDeviceId(seed),
      session_id: this.generateSessionId(seed),
      user_agent: this.userAgents[seedInt % this.userAgents.length],
      timezone: this.timezones[seedInt % this.timezones.length],

      // Add realistic variations
      screen_resolution: this.generateScreenResolution(template.lang_pack, seedInt),
      battery_level: this.generateBatteryLevel(seedInt),
      network_type: this.generateNetworkType(template.lang_pack, seedInt),

      // Telegram-specific
      layer: 158, // Current Telegram layer
      api_id: null, // Will be set from user's API credentials
      api_hash: null, // Will be set from user's API credentials

      // Metadata
      created_at: new Date(),
      platform: template.lang_pack,
      fingerprint_hash: crypto.createHash('md5').update(seed).digest('hex')
    };

    return fingerprint;
  }

  /**
   * Generate device ID
   */
  generateDeviceId(seed) {
    const hash = crypto.createHash('sha256').update(seed + 'device').digest('hex');
    return hash.substring(0, 16).toUpperCase();
  }

  /**
   * Generate session ID
   */
  generateSessionId(seed) {
    const hash = crypto.createHash('sha256').update(seed + 'session').digest('hex');
    return hash.substring(0, 32);
  }

  /**
   * Generate realistic screen resolution based on platform
   */
  generateScreenResolution(platform, seedInt) {
    const resolutions = {
      android: ['1080x2400', '1440x3200', '1080x2340', '720x1600'],
      ios: ['1170x2532', '1284x2778', '828x1792', '1125x2436'],
      desktop: ['1920x1080', '2560x1440', '1366x768', '1536x864']
    };
    
    const platformResolutions = resolutions[platform] || resolutions.desktop;
    return platformResolutions[seedInt % platformResolutions.length];
  }

  /**
   * Generate realistic battery level
   */
  generateBatteryLevel(seedInt) {
    // Generate battery between 20-95% (realistic range)
    return 20 + (seedInt % 76);
  }

  /**
   * Generate network type based on platform
   */
  generateNetworkType(platform, seedInt) {
    const networkTypes = {
      android: ['WiFi', '4G', '5G', 'LTE'],
      ios: ['WiFi', '4G', '5G', 'LTE'],
      desktop: ['WiFi', 'Ethernet', 'WiFi']
    };
    
    const platformNetworks = networkTypes[platform] || networkTypes.desktop;
    return platformNetworks[seedInt % platformNetworks.length];
  }

  /**
   * Get stored device fingerprint for account or generate new one
   */
  async getDeviceFingerprint(userId, accountId, preferredPlatform = null) {
    try {
      // Try to get existing fingerprint from database
      const { TelegramAccount } = require('../models');
      const account = await TelegramAccount.findOne({
        where: { id: accountId, userId: userId }
      });

      if (account && account.deviceFingerprint) {
        return JSON.parse(account.deviceFingerprint);
      }

      // Generate new fingerprint
      const fingerprint = this.generateDeviceFingerprint(userId, accountId, preferredPlatform);
      
      // Store in database
      if (account) {
        await account.update({
          deviceFingerprint: JSON.stringify(fingerprint)
        });
      }

      return fingerprint;
    } catch (error) {
      console.error('Error getting device fingerprint:', error);
      // Fallback to generating without storing
      return this.generateDeviceFingerprint(userId, accountId, preferredPlatform);
    }
  }

  /**
   * Update device fingerprint with new information
   */
  async updateDeviceFingerprint(userId, accountId, updates) {
    try {
      const { TelegramAccount } = require('../models');
      const account = await TelegramAccount.findOne({
        where: { id: accountId, userId: userId }
      });

      if (account && account.deviceFingerprint) {
        const fingerprint = JSON.parse(account.deviceFingerprint);
        const updatedFingerprint = { ...fingerprint, ...updates };
        
        await account.update({
          deviceFingerprint: JSON.stringify(updatedFingerprint)
        });

        return updatedFingerprint;
      }
    } catch (error) {
      console.error('Error updating device fingerprint:', error);
    }
  }

  /**
   * Generate human-like delays for actions
   */
  generateHumanDelay(action = 'default') {
    const delays = {
      typing: { min: 1000, max: 3000 },      // 1-3 seconds
      reading: { min: 2000, max: 8000 },     // 2-8 seconds  
      clicking: { min: 500, max: 1500 },     // 0.5-1.5 seconds
      scrolling: { min: 800, max: 2000 },    // 0.8-2 seconds
      default: { min: 1000, max: 5000 }      // 1-5 seconds
    };

    const range = delays[action] || delays.default;
    return Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;
  }

  /**
   * Generate realistic activity patterns
   */
  generateActivityPattern(userId) {
    const seed = crypto.createHash('sha256')
      .update(`${userId}-activity`)
      .digest('hex');
    const seedInt = parseInt(seed.substring(0, 8), 16);

    return {
      // Active hours (user's typical online time)
      activeHours: {
        start: 7 + (seedInt % 4), // 7-10 AM
        end: 20 + (seedInt % 5)   // 8PM-12AM
      },
      
      // Days of week activity (0 = Sunday)
      activeDays: [1, 2, 3, 4, 5, 6], // Weekdays + Saturday
      
      // Message frequency (messages per hour during active time)
      messageFrequency: {
        min: 5 + (seedInt % 10),  // 5-15 messages/hour
        max: 20 + (seedInt % 30)  // 20-50 messages/hour
      },
      
      // Break patterns
      breakPatterns: {
        shortBreak: { min: 300000, max: 900000 },    // 5-15 minutes
        longBreak: { min: 1800000, max: 7200000 },   // 30 minutes - 2 hours
        dailyBreak: { min: 28800000, max: 43200000 } // 8-12 hours (sleep)
      }
    };
  }
}

module.exports = new DeviceSpoofingService();
