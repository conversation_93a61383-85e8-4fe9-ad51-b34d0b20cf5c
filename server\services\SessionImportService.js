const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { TelegramAccount } = require('../models');

class SessionImportService {
  constructor() {
    this.supportedFormats = ['tdata', 'json', 'session'];
    this.uploadDir = path.join(__dirname, '../uploads/sessions');
    this.ensureUploadDir();
  }

  /**
   * Ensure upload directory exists
   */
  ensureUploadDir() {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  /**
   * Import session from TData folder
   */
  async importTDataSession(userId, tdataPath, accountName) {
    try {
      console.log(`📁 Importing TData session from: ${tdataPath}`);

      // Validate TData folder structure
      const validation = this.validateTDataFolder(tdataPath);
      if (!validation.valid) {
        throw new Error(`Invalid TData folder: ${validation.error}`);
      }

      // Extract session data from TData files
      const sessionData = await this.extractTDataSession(tdataPath);
      
      // Create account record
      const account = await this.createAccountFromSession(userId, sessionData, accountName, 'tdata');
      
      console.log(`✅ TData session imported successfully for account: ${accountName}`);
      return account;

    } catch (error) {
      console.error('❌ TData import failed:', error);
      throw error;
    }
  }

  /**
   * Import session from JSON file
   */
  async importJSONSession(userId, jsonPath, accountName) {
    try {
      console.log(`📄 Importing JSON session from: ${jsonPath}`);

      // Read and parse JSON file
      const jsonData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
      
      // Validate JSON structure
      const validation = this.validateJSONSession(jsonData);
      if (!validation.valid) {
        throw new Error(`Invalid JSON session: ${validation.error}`);
      }

      // Convert JSON to session data
      const sessionData = this.convertJSONToSession(jsonData);
      
      // Create account record
      const account = await this.createAccountFromSession(userId, sessionData, accountName, 'json');
      
      console.log(`✅ JSON session imported successfully for account: ${accountName}`);
      return account;

    } catch (error) {
      console.error('❌ JSON import failed:', error);
      throw error;
    }
  }

  /**
   * Import session from .session file
   */
  async importSessionFile(userId, sessionPath, accountName) {
    try {
      console.log(`🔑 Importing .session file from: ${sessionPath}`);

      // Read session file
      const sessionString = fs.readFileSync(sessionPath, 'utf8');
      
      // Validate session string
      if (!this.validateSessionString(sessionString)) {
        throw new Error('Invalid session string format');
      }

      // Create session data
      const sessionData = {
        session_string: sessionString,
        phone_number: null, // Will be extracted from session
        first_name: null,
        last_name: null,
        username: null,
        telegram_id: null
      };
      
      // Create account record
      const account = await this.createAccountFromSession(userId, sessionData, accountName, 'session');
      
      console.log(`✅ Session file imported successfully for account: ${accountName}`);
      return account;

    } catch (error) {
      console.error('❌ Session file import failed:', error);
      throw error;
    }
  }

  /**
   * Validate TData folder structure
   */
  validateTDataFolder(tdataPath) {
    try {
      if (!fs.existsSync(tdataPath)) {
        return { valid: false, error: 'TData folder does not exist' };
      }

      // Check if this is a phone number folder with tdata subfolder
      if (fs.existsSync(path.join(tdataPath, 'tdata'))) {
        // This is a phone number folder, check the tdata subfolder
        const subTdataPath = path.join(tdataPath, 'tdata');
        
        // Check for key_datas in the tdata subfolder
        if (!fs.existsSync(path.join(subTdataPath, 'key_datas'))) {
          return { 
            valid: false, 
            error: 'Missing key_datas file in tdata subfolder' 
          };
        }
        
        return { valid: true };
      }
      
      // Standard tdata folder - check for key_datas
      if (!fs.existsSync(path.join(tdataPath, 'key_datas'))) {
        return { 
          valid: false, 
          error: 'Missing key_datas file' 
        };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  /**
   * Extract session data from TData folder
   */
  async extractTDataSession(tdataPath) {
    try {
      // Check if this is a phone number folder structure
      const isPhoneNumberFolder = fs.existsSync(path.join(tdataPath, 'tdata'));
      
      // If this is a phone number folder, use the tdata subfolder
      if (isPhoneNumberFolder) {
        console.log(`📱 Detected phone number folder structure: ${path.basename(tdataPath)}`);
        tdataPath = path.join(tdataPath, 'tdata');
      }
      
      // Find the account files (D877F783D5D3EF8C pattern)
      const accountFiles = fs.readdirSync(tdataPath)
        .filter(file => /^[A-F0-9]{16}$/.test(file));
      
      // If no hex account files found, look for other folders that might contain account data
      let accountFolder = null;
      if (accountFiles.length > 0) {
        // Get the most recent account (in case of multiple)
        accountFolder = accountFiles[0];
      }
      
      // Read key_datas file for authentication data
      const keyDataPath = path.join(tdataPath, 'key_datas');
      if (!fs.existsSync(keyDataPath)) {
        throw new Error('key_datas file not found in TData folder');
      }
      const keyData = fs.readFileSync(keyDataPath);
      
      // Read map files for session data
      const mapFiles = fs.readdirSync(tdataPath)
        .filter(file => file.startsWith('map') && file.endsWith('0'));
      
      // Read account-specific files
      let accountData = null;
      if (accountFolder) {
        const accountPath = path.join(tdataPath, accountFolder);
        if (fs.existsSync(accountPath) && fs.statSync(accountPath).isDirectory()) {
          const tgaPath = path.join(accountPath, 'tdata');
          if (fs.existsSync(tgaPath)) {
            accountData = fs.readFileSync(tgaPath);
          }
        }
      }
      
      // Extract phone number from settings (if available) or from folder name
      let phoneNumber = null;
      const settingsPath = path.join(tdataPath, 'settings0');
      if (fs.existsSync(settingsPath)) {
        const settings = fs.readFileSync(settingsPath);
        // Try to extract phone number from settings
        const phoneMatch = settings.toString().match(/\+\d{10,15}/);
        if (phoneMatch) {
          phoneNumber = phoneMatch[0];
        }
      }
      
      // If phone number not found in settings, try to extract from folder name
      if (!phoneNumber && isPhoneNumberFolder) {
        const folderName = path.basename(path.dirname(tdataPath));
        if (folderName.startsWith('+') && /^\+\d+$/.test(folderName)) {
          phoneNumber = folderName;
        }
      }
      
      // Collect all essential files
      const essentialFiles = {};
      
      // Add key_datas
      essentialFiles['key_datas'] = keyData.toString('base64');
      
      // Add all map files
      mapFiles.forEach(file => {
        essentialFiles[file] = fs.readFileSync(path.join(tdataPath, file)).toString('base64');
      });
      
      // Add settings0 if exists
      if (fs.existsSync(settingsPath)) {
        essentialFiles['settings0'] = fs.readFileSync(settingsPath).toString('base64');
      }
      
      // Add config0 if exists
      const configPath = path.join(tdataPath, 'config0');
      if (fs.existsSync(configPath)) {
        essentialFiles['config0'] = fs.readFileSync(configPath).toString('base64');
      }
      
      // Create a structured session data object
      const sessionData = {
        essential_files: essentialFiles,
        map_data: mapFiles.map(file => ({
          name: file,
          data: essentialFiles[file]
        })),
        account_data: accountData ? accountData.toString('base64') : null,
        account_id: accountFolder,
        phone_number: phoneNumber,
        timestamp: Date.now()
      };
      
      // Convert to session string format
      const sessionString = Buffer.from(JSON.stringify(sessionData)).toString('base64');
      
      return {
        session_string: sessionString,
        phone_number: phoneNumber,
        first_name: null,
        last_name: null,
        username: null,
        telegram_id: null,
        tdata_path: tdataPath
      };

    } catch (error) {
      throw new Error(`Failed to extract TData session: ${error.message}`);
    }
  }

  /**
   * Convert TData to session string
   */
  convertTDataToSession(keyData, settings) {
    try {
      // This is a simplified conversion - in production you'd need proper TData parsing
      // For now, we'll create a base64 encoded representation
      const sessionData = {
        key_data: keyData.toString('base64'),
        settings: settings ? settings.toString('base64') : null,
        timestamp: Date.now()
      };

      return Buffer.from(JSON.stringify(sessionData)).toString('base64');

    } catch (error) {
      throw new Error(`TData conversion failed: ${error.message}`);
    }
  }

  /**
   * Validate JSON session structure
   */
  validateJSONSession(jsonData) {
    try {
      // Check for Telethon/Pyrogram format
      if (jsonData.session_string) {
        return { valid: true };
      }
      
      // Check for GramJS format
      if (jsonData.authKey && jsonData.dcId) {
        return { valid: true };
      }
      
      // Check for custom format with API credentials
      if ((jsonData.api_id || jsonData.apiId) && (jsonData.api_hash || jsonData.apiHash)) {
        if (jsonData.session || jsonData.sessionString || jsonData.auth_key || jsonData.authKey) {
          return { valid: true };
        }
      }

      return { 
        valid: false, 
        error: 'Missing required session data. JSON must contain session string or auth key.' 
      };

    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  /**
   * Convert JSON to session data
   */
  convertJSONToSession(jsonData) {
    // Handle different JSON formats
    let sessionString = null;
    
    // Direct session string
    if (jsonData.session_string) {
      sessionString = jsonData.session_string;
    }
    // GramJS format
    else if (jsonData.authKey && jsonData.dcId) {
      const gramJsData = {
        authKey: jsonData.authKey,
        dcId: jsonData.dcId,
        serverAddress: jsonData.serverAddress || null,
        port: jsonData.port || null,
        userAuth: jsonData.userAuth || null
      };
      sessionString = Buffer.from(JSON.stringify(gramJsData)).toString('base64');
    }
    // Custom format
    else if (jsonData.session || jsonData.sessionString || jsonData.auth_key || jsonData.authKey) {
      sessionString = jsonData.session || jsonData.sessionString || 
                     (jsonData.auth_key && Buffer.from(jsonData.auth_key).toString('base64')) ||
                     (jsonData.authKey && Buffer.from(jsonData.authKey).toString('base64'));
    }
    
    return {
      session_string: sessionString,
      phone_number: jsonData.phone_number || jsonData.phoneNumber || null,
      first_name: jsonData.first_name || jsonData.firstName || null,
      last_name: jsonData.last_name || jsonData.lastName || null,
      username: jsonData.username || null,
      telegram_id: jsonData.telegram_id || jsonData.telegramId || jsonData.userId || null,
      api_id: jsonData.api_id || jsonData.apiId || null,
      api_hash: jsonData.api_hash || jsonData.apiHash || null
    };
  }

  /**
   * Validate session string format
   */
  validateSessionString(sessionString) {
    try {
      // Basic validation - check if it's a valid base64 string
      if (!sessionString || typeof sessionString !== 'string') {
        return false;
      }

      // Check if it's base64 encoded
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      
      // Accept both Telethon and GramJS session strings
      if (base64Regex.test(sessionString)) {
        return true;
      }
      
      // Try to parse as JSON (some session strings are JSON)
      try {
        const parsed = JSON.parse(sessionString);
        return parsed && (parsed.dc_id !== undefined || parsed.dcId !== undefined);
      } catch (e) {
        // Not JSON, continue checking
      }
      
      // Check if it's a hexadecimal string (some session formats)
      const hexRegex = /^[0-9a-fA-F]+$/;
      if (hexRegex.test(sessionString) && sessionString.length >= 64) {
        return true;
      }
      
      return false;

    } catch (error) {
      return false;
    }
  }

  /**
   * Create account from session data
   */
  async createAccountFromSession(userId, sessionData, accountName, importType) {
    try {
      // Generate device fingerprint for the account
      const deviceSpoofingService = require('./DeviceSpoofingService');
      const deviceFingerprint = deviceSpoofingService.generateDeviceFingerprint(userId, Date.now());

      // Create account record with proper phone number handling
      const phoneNumber = sessionData.phone_number || `1000000${Date.now().toString().slice(-6)}`;

      const account = await TelegramAccount.create({
        userId: userId,
        phoneNumber: phoneNumber,
        accountName: accountName,
        sessionString: sessionData.session_string,
        deviceFingerprint: JSON.stringify(deviceFingerprint),
        status: 'active', // Set as active since it's imported
        isActive: true,
        firstName: sessionData.first_name,
        lastName: sessionData.last_name,
        username: sessionData.username,
        telegramId: sessionData.telegram_id,
        notes: `Imported from ${importType} on ${new Date().toISOString()}`
      });

      return account;

    } catch (error) {
      throw new Error(`Failed to create account: ${error.message}`);
    }
  }

  /**
   * Get import statistics
   */
  async getImportStats(userId) {
    try {
      const accounts = await TelegramAccount.findAll({
        where: { userId: userId },
        attributes: ['status', 'notes']
      });

      const importedAccounts = accounts.filter(acc => 
        acc.status === 'imported' || acc.notes?.includes('Imported from')
      );

      const stats = {
        total_accounts: accounts.length,
        imported_accounts: importedAccounts.length,
        import_types: {
          tdata: importedAccounts.filter(acc => acc.notes?.includes('tdata')).length,
          json: importedAccounts.filter(acc => acc.notes?.includes('json')).length,
          session: importedAccounts.filter(acc => acc.notes?.includes('session')).length
        }
      };

      return stats;

    } catch (error) {
      throw new Error(`Failed to get import stats: ${error.message}`);
    }
  }

  /**
   * Clean up uploaded files
   */
  cleanupUploadedFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`🗑️ Cleaned up uploaded file: ${filePath}`);
      }
    } catch (error) {
      console.error('❌ Failed to cleanup file:', error);
    }
  }

  /**
   * Validate imported session by attempting connection
   */
  async validateImportedSession(accountId) {
    try {
      // Get account data
      const account = await TelegramAccount.findByPk(accountId);
      if (!account) {
        throw new Error('Account not found');
      }
      
      // For now, we'll mark it as validated without connecting
      // In a production environment, you would connect to Telegram here
      await account.update({
        status: 'active',
        isActive: true
      });

      return { valid: true, message: 'Session validated successfully' };

    } catch (error) {
      await TelegramAccount.update(
        { status: 'invalid' },
        { where: { id: accountId } }
      );

      return { valid: false, message: error.message };
    }
  }
}

module.exports = new SessionImportService();
