const User = require('./User');
const TelegramAccount = require('./TelegramAccount');
const ScrapedMember = require('./ScrapedMember');
const AutomationTask = require('./AutomationTask');
const MessageTemplate = require('./MessageTemplate');
const ProxyConfig = require('./ProxyConfig');
const GroupManagement = require('./GroupManagement');
const Analytics = require('./Analytics');
const Payment = require('./Payment');
const AccountHealth = require('./AccountHealth');
const AutoReplyRule = require('./AutoReplyRule');
const KeywordMonitor = require('./KeywordMonitor');
const MonitoredKeyword = require('./MonitoredKeyword');
const KeywordDetection = require('./KeywordDetection');
const UserActivity = require('./UserActivity');

// Define associations
User.hasMany(TelegramAccount, { foreignKey: 'userId', as: 'telegramAccounts' });
TelegramAccount.belongsTo(User, { foreignKey: 'userId', as: 'user' });

User.hasMany(ScrapedMember, { foreignKey: 'userId', as: 'scrapedMembers' });
ScrapedMember.belongsTo(User, { foreignKey: 'userId', as: 'user' });

TelegramAccount.hasMany(ScrapedMember, { foreignKey: 'telegramAccountId', as: 'scrapedMembers' });
ScrapedMember.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

User.hasMany(AutomationTask, { foreignKey: 'userId', as: 'automationTasks' });
AutomationTask.belongsTo(User, { foreignKey: 'userId', as: 'user' });

TelegramAccount.hasMany(AutomationTask, { foreignKey: 'telegramAccountId', as: 'automationTasks' });
AutomationTask.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

User.hasMany(MessageTemplate, { foreignKey: 'userId', as: 'messageTemplates' });
MessageTemplate.belongsTo(User, { foreignKey: 'userId', as: 'user' });

User.hasMany(ProxyConfig, { foreignKey: 'userId', as: 'proxyConfigs' });
ProxyConfig.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Proxy associations with TelegramAccount
TelegramAccount.belongsTo(ProxyConfig, { foreignKey: 'proxyId', as: 'proxy' });
ProxyConfig.hasMany(TelegramAccount, { foreignKey: 'proxyId', as: 'accounts' });

User.hasMany(GroupManagement, { foreignKey: 'userId', as: 'groupManagements' });
GroupManagement.belongsTo(User, { foreignKey: 'userId', as: 'user' });

TelegramAccount.hasMany(GroupManagement, { foreignKey: 'telegramAccountId', as: 'groupManagements' });
GroupManagement.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

// Analytics associations
User.hasMany(Analytics, { foreignKey: 'userId', as: 'analytics' });
Analytics.belongsTo(User, { foreignKey: 'userId', as: 'user' });

TelegramAccount.hasMany(Analytics, { foreignKey: 'telegramAccountId', as: 'analytics' });
Analytics.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

// Payment associations
User.hasMany(Payment, { foreignKey: 'userId', as: 'payments' });
Payment.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Account Health associations
TelegramAccount.hasOne(AccountHealth, { foreignKey: 'telegramAccountId', as: 'health' });
AccountHealth.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

// Auto Reply Rule associations
User.hasMany(AutoReplyRule, { foreignKey: 'userId', as: 'autoReplyRules' });
AutoReplyRule.belongsTo(User, { foreignKey: 'userId', as: 'user' });

TelegramAccount.hasMany(AutoReplyRule, { foreignKey: 'telegramAccountId', as: 'autoReplyRules' });
AutoReplyRule.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

// Keyword Monitor associations
User.hasMany(KeywordMonitor, { foreignKey: 'userId', as: 'keywordMonitors' });
KeywordMonitor.belongsTo(User, { foreignKey: 'userId', as: 'user' });

TelegramAccount.hasMany(KeywordMonitor, { foreignKey: 'telegramAccountId', as: 'keywordMonitors' });
KeywordMonitor.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

// Monitored Keyword associations
KeywordMonitor.hasMany(MonitoredKeyword, { foreignKey: 'monitorId', as: 'keywords' });
MonitoredKeyword.belongsTo(KeywordMonitor, { foreignKey: 'monitorId', as: 'monitor' });

// Keyword Detection associations
KeywordMonitor.hasMany(KeywordDetection, { foreignKey: 'monitorId', as: 'detections' });
KeywordDetection.belongsTo(KeywordMonitor, { foreignKey: 'monitorId', as: 'monitor' });

MonitoredKeyword.hasMany(KeywordDetection, { foreignKey: 'keywordId', as: 'detections' });
KeywordDetection.belongsTo(MonitoredKeyword, { foreignKey: 'keywordId', as: 'keyword' });

// User Activity associations
User.hasMany(UserActivity, { foreignKey: 'userId', as: 'userActivities' });
UserActivity.belongsTo(User, { foreignKey: 'userId', as: 'user' });

TelegramAccount.hasMany(UserActivity, { foreignKey: 'telegramAccountId', as: 'userActivities' });
UserActivity.belongsTo(TelegramAccount, { foreignKey: 'telegramAccountId', as: 'telegramAccount' });

// Associate UserActivity with ScrapedMember
UserActivity.belongsTo(ScrapedMember, {
  foreignKey: 'memberId',
  targetKey: 'telegramId',
  as: 'member',
  constraints: false
});

module.exports = {
  User,
  TelegramAccount,
  ScrapedMember,
  AutomationTask,
  MessageTemplate,
  ProxyConfig,
  GroupManagement,
  Analytics,
  Payment,
  AccountHealth,
  AutoReplyRule,
  KeywordMonitor,
  MonitoredKeyword,
  KeywordDetection,
  UserActivity
};
