#!/usr/bin/env node

/**
 * Setup script for advanced features
 * This script helps initialize the new advanced features
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up Advanced Features for Telegram Management System...\n');

// Check if we're in the right directory
if (!fs.existsSync('./server/package.json')) {
  console.error('❌ Please run this script from the project root directory');
  process.exit(1);
}

// Function to check if a command exists
function commandExists(command) {
  try {
    execSync(`which ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Function to create directory if it doesn't exist
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  }
}

// Function to copy file if it doesn't exist
function copyIfNotExists(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.copyFileSync(src, dest);
    console.log(`✅ Created: ${dest}`);
  } else {
    console.log(`⚠️  Already exists: ${dest}`);
  }
}

console.log('1. Checking prerequisites...');

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
if (majorVersion < 16) {
  console.error('❌ Node.js 16 or higher is required');
  process.exit(1);
}
console.log(`✅ Node.js version: ${nodeVersion}`);

// Check if Redis is available (optional)
if (commandExists('redis-server')) {
  console.log('✅ Redis is available');
} else {
  console.log('⚠️  Redis not found - some features will be limited');
}

console.log('\n2. Setting up directories...');

// Create necessary directories
const directories = [
  './server/logs',
  './server/uploads',
  './server/backups',
  './server/temp'
];

directories.forEach(ensureDir);

console.log('\n3. Setting up configuration files...');

// Copy environment file
copyIfNotExists('./server/.env.example', './server/.env');

console.log('\n4. Installing dependencies...');

try {
  console.log('Installing server dependencies...');
  execSync('cd server && npm install', { stdio: 'inherit' });
  
  console.log('Installing client dependencies...');
  execSync('cd client && npm install', { stdio: 'inherit' });
  
  console.log('✅ Dependencies installed successfully');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

console.log('\n5. Setting up database...');

try {
  // Run database migrations/sync
  console.log('Syncing database...');
  execSync('cd server && node -e "require(\'./config/database\').sequelize.sync({force: false}).then(() => console.log(\'Database synced\')).catch(console.error)"', { stdio: 'inherit' });
  console.log('✅ Database setup complete');
} catch (error) {
  console.error('❌ Database setup failed:', error.message);
}

console.log('\n6. Generating configuration summary...');

const configSummary = `
# Telegram Management System - Advanced Features Setup Complete

## 🎉 Setup Summary

### ✅ Features Enabled:
- Enhanced Analytics & Reporting
- Payment Integration (Stripe)
- Redis Caching & Session Management
- Account Health Monitoring
- Automated Backup System
- Advanced Security Features

### 📋 Next Steps:

1. **Configure Environment Variables** (server/.env):
   - Add your Telegram API credentials
   - Configure Stripe keys for payments
   - Set up Redis connection (optional)
   - Configure email settings

2. **Start Redis** (optional but recommended):
   \`\`\`bash
   redis-server
   \`\`\`

3. **Start the Application**:
   \`\`\`bash
   # Terminal 1 - Start server
   cd server && npm run dev
   
   # Terminal 2 - Start client
   cd client && npm run dev
   \`\`\`

4. **Access the Application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Health Check: http://localhost:3001/api/health

### 🔧 Configuration Files Created:
- server/.env (environment variables)
- server/logs/ (log directory)
- server/uploads/ (file upload directory)
- server/backups/ (backup storage)

### 📊 New Features Available:
- Analytics Dashboard: /analytics
- Billing Management: /billing
- Account Health Monitoring: Integrated in analytics
- Backup Management: Admin panel

### 🚀 Production Deployment:
- Use Docker Compose for production deployment
- Configure SSL certificates
- Set up monitoring and logging
- Configure automated backups

For detailed documentation, see:
- ADVANCED_FEATURES.md
- README.md
- DEPLOYMENT.md

Happy coding! 🎉
`;

fs.writeFileSync('./SETUP_COMPLETE.md', configSummary);

console.log('\n🎉 Advanced Features Setup Complete!');
console.log('\n📋 Summary:');
console.log('✅ All dependencies installed');
console.log('✅ Configuration files created');
console.log('✅ Database initialized');
console.log('✅ Directories created');
console.log('\n📖 Next steps documented in: SETUP_COMPLETE.md');
console.log('\n🚀 Your Telegram Management System is now enterprise-ready!');
console.log('\nTo start the application:');
console.log('1. Configure server/.env with your API keys');
console.log('2. Run: cd server && npm run dev');
console.log('3. Run: cd client && npm run dev (in another terminal)');
console.log('4. Visit: http://localhost:3000');

console.log('\n🎯 All advanced features are now available:');
console.log('- 📊 Enhanced Analytics Dashboard');
console.log('- 💳 Complete Payment Integration');
console.log('- 🔧 Redis Caching & Sessions');
console.log('- 🏥 Account Health Monitoring');
console.log('- 💾 Automated Backup System');
console.log('- 🔒 Advanced Security Features');
console.log('\nEnjoy your enterprise-grade Telegram Management System! 🚀');
