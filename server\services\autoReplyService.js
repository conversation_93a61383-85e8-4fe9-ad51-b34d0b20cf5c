const { Telegram<PERSON>pi, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');
const { AutoReplyRule, TelegramAccount } = require('../models');
const { Op } = require('sequelize');

class AutoReplyService {
  constructor() {
    this.activeListeners = new Map();
    console.log('Auto Reply Service initialized');
  }

  /**
   * Start message monitoring for a specific account
   */
  async startMonitoring(accountId) {
    try {
      // Check if already monitoring
      if (this.activeListeners.has(accountId)) {
        return { success: true, message: 'Already monitoring this account' };
      }

      const account = await TelegramAccount.findByPk(accountId);
      if (!account || !account.sessionString) {
        return { success: false, error: 'Account not found or not authenticated' };
      }

      // Get active rules for this account
      const rules = await AutoReplyRule.findAll({
        where: {
          telegramAccountId: accountId,
          isActive: true
        }
      });

      if (rules.length === 0) {
        return { success: false, error: 'No active auto-reply rules found' };
      }

      // Initialize Telegram client
      const apiId = parseInt(process.env.TELEGRAM_API_ID);
      const apiHash = process.env.TELEGRAM_API_HASH;
      
      const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);
      await client.connect();

      // Start listening for new messages
      client.addEventHandler(async (event) => {
        try {
          await this.handleNewMessage(event, rules, client, account);
        } catch (error) {
          console.error(`Error handling message for account ${accountId}:`, error);
        }
      }, new Api.events.NewMessage());

      // Store client in active listeners map
      this.activeListeners.set(accountId, {
        client,
        account,
        rules,
        startTime: new Date()
      });

      console.log(`Started auto-reply monitoring for account ${accountId}`);
      return { success: true, message: 'Started monitoring for auto-replies' };
    } catch (error) {
      console.error(`Error starting monitoring for account ${accountId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle new incoming message and check against rules
   */
  async handleNewMessage(event, rules, client, account) {
    const message = event.message;
    
    // Skip own messages
    if (message.fromId?.userId === client.session.userId) {
      return;
    }

    // Get message text
    const messageText = message.message || '';
    if (!messageText) return;

    // Check against each rule
    for (const rule of rules) {
      try {
        const shouldReply = this.checkMessageAgainstRule(messageText, rule);
        
        if (shouldReply) {
          // Apply delay if specified
          if (rule.replyDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, rule.replyDelay * 1000));
          }
          
          // Send the reply
          await client.sendMessage(message.peerId, {
            message: this.formatReplyMessage(rule.replyText, message),
            replyTo: rule.includeReplyToOriginal ? message.id : undefined
          });
          
          // Update statistics
          await account.incrementMessageCount();
          await rule.increment('triggerCount');
          await rule.update({ lastTriggered: new Date() });
          
          // If rule is set to only trigger once per conversation, exit
          if (rule.triggerOnce) {
            break;
          }
        }
      } catch (error) {
        console.error(`Error processing rule ${rule.id}:`, error);
      }
    }
  }

  /**
   * Check if message matches rule criteria
   */
  checkMessageAgainstRule(messageText, rule) {
    const { triggerType, triggerValue, caseSensitive } = rule;
    
    // Prepare message text based on case sensitivity
    const processedMessage = caseSensitive ? messageText : messageText.toLowerCase();
    const processedTrigger = caseSensitive ? triggerValue : triggerValue.toLowerCase();
    
    switch (triggerType) {
      case 'exact_match':
        return processedMessage === processedTrigger;
        
      case 'contains':
        return processedMessage.includes(processedTrigger);
        
      case 'starts_with':
        return processedMessage.startsWith(processedTrigger);
        
      case 'ends_with':
        return processedMessage.endsWith(processedTrigger);
        
      case 'regex':
        try {
          const regex = new RegExp(triggerValue, caseSensitive ? '' : 'i');
          return regex.test(messageText);
        } catch (error) {
          console.error('Invalid regex in rule:', error);
          return false;
        }
        
      default:
        return false;
    }
  }

  /**
   * Format reply message with variables
   */
  formatReplyMessage(replyTemplate, originalMessage) {
    let formattedReply = replyTemplate;
    
    // Replace variables in the template
    formattedReply = formattedReply.replace(/\{sender\}/g, originalMessage.sender?.firstName || 'User');
    formattedReply = formattedReply.replace(/\{message\}/g, originalMessage.message || '');
    formattedReply = formattedReply.replace(/\{time\}/g, new Date().toLocaleTimeString());
    formattedReply = formattedReply.replace(/\{date\}/g, new Date().toLocaleDateString());
    
    return formattedReply;
  }

  /**
   * Stop monitoring for an account
   */
  async stopMonitoring(accountId) {
    try {
      const listener = this.activeListeners.get(accountId);
      if (!listener) {
        return { success: false, error: 'No active monitoring for this account' };
      }
      
      // Disconnect client
      await listener.client.disconnect();
      this.activeListeners.delete(accountId);
      
      return { success: true, message: 'Stopped monitoring for auto-replies' };
    } catch (error) {
      console.error(`Error stopping monitoring for account ${accountId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(accountId) {
    const listener = this.activeListeners.get(accountId);
    if (!listener) {
      return { active: false };
    }
    
    return {
      active: true,
      startTime: listener.startTime,
      ruleCount: listener.rules.length,
      uptime: Math.floor((new Date() - listener.startTime) / 1000)
    };
  }

  /**
   * Update rules for an active listener
   */
  async updateRules(accountId) {
    const listener = this.activeListeners.get(accountId);
    if (!listener) {
      return { success: false, error: 'No active monitoring for this account' };
    }
    
    // Get updated rules
    const updatedRules = await AutoReplyRule.findAll({
      where: {
        telegramAccountId: accountId,
        isActive: true
      }
    });
    
    // Update rules in the listener
    listener.rules = updatedRules;
    
    return { 
      success: true, 
      message: 'Updated auto-reply rules',
      ruleCount: updatedRules.length
    };
  }
}

module.exports = new AutoReplyService(); 