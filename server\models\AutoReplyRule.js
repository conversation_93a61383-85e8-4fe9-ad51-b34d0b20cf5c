const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class AutoReplyRule extends Model {}

AutoReplyRule.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  ruleName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  triggerType: {
    type: DataTypes.ENUM('exact_match', 'contains', 'starts_with', 'ends_with', 'regex'),
    allowNull: false,
    defaultValue: 'contains'
  },
  triggerValue: {
    type: DataTypes.STRING,
    allowNull: false
  },
  replyText: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  caseSensitive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  replyDelay: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Delay in seconds before sending reply'
  },
  includeReplyToOriginal: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  triggerOnce: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'If true, only trigger once per conversation'
  },
  triggerCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  lastTriggered: {
    type: DataTypes.DATE,
    allowNull: true
  },
  targetChats: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Optional list of chat IDs where this rule applies'
  },
  excludedChats: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Optional list of chat IDs where this rule does not apply'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'AutoReplyRule',
  tableName: 'auto_reply_rules',
  timestamps: true
});

module.exports = AutoReplyRule; 