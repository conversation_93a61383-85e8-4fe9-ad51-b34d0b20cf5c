const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const http = require('http');
const socketIo = require('socket.io');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const telegramRoutes = require('./routes/telegram');
const userRoutes = require('./routes/user');
const memberRoutes = require('./routes/member');
const messageRoutes = require('./routes/message');
const automationRoutes = require('./routes/automation');
const toolsRoutes = require('./routes/tools');
const analyticsRoutes = require('./routes/analytics');
const paymentRoutes = require('./routes/payment');
const backupRoutes = require('./routes/backup');
const autoReplyRoutes = require('./routes/autoReply');
const keywordMonitorRoutes = require('./routes/keywordMonitor');
const userActivityRoutes = require('./routes/userActivity');

// Import database
const { sequelize } = require('./config/database');

// Import socket handlers
const socketHandler = require('./services/socketService');

// Import Redis service
const redisService = require('./services/redisService');

// Import backup service
const backupService = require('./services/backupService');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Socket.io middleware
app.use((req, res, next) => {
  req.io = io;
  next();
});

// Health check route
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/telegram', telegramRoutes);
app.use('/api/user', userRoutes);
app.use('/api/members', memberRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/automation', automationRoutes);
app.use('/api/tools', toolsRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/backup', backupRoutes);
app.use('/api/auto-reply', autoReplyRoutes);
app.use('/api/keyword-monitor', keywordMonitorRoutes);
app.use('/api/user-activity', userActivityRoutes);
app.use('/api/security', require('./routes/security'));
app.use('/api/session-import', require('./routes/sessionImport'));

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

// Serve static files from React build
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/dist')));

  // Handle React routing, return all requests to React app
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/dist/index.html'));
  });
} else {
  // Development mode - show API info on root
  app.get('/', (req, res) => {
    res.json({
      message: 'Telegram Management System API',
      version: '1.0.0',
      status: 'Development',
      frontend: 'http://localhost:3000',
      api: 'http://localhost:3001/api',
      health: 'http://localhost:3001/api/health',
      docs: {
        endpoints: [
          'GET /api/health - Health check',
          'POST /api/auth/register - User registration',
          'POST /api/auth/login - User login',
          'GET /api/telegram/accounts - Get Telegram accounts',
          'POST /api/telegram/accounts - Add Telegram account',
          'GET /api/members - Get scraped members',
          'POST /api/members/scrape - Scrape group members',
          'POST /api/messages/send - Send message',
          'GET /api/automation/tasks - Get automation tasks',
          'POST /api/tools/check-username - Check username availability',
          'GET /api/auto-reply/rules - Get auto-reply rules',
          'POST /api/auto-reply/monitor/start - Start auto-reply monitoring',
          'GET /api/keyword-monitor/monitors - Get keyword monitors',
          'POST /api/keyword-monitor/monitors/:id/start - Start keyword monitoring',
          'POST /api/user-activity/track/start - Start user activity tracking',
          'GET /api/user-activity/most-active - Get most active users'
        ]
      }
    });
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler for API routes only
app.use('/api/*', (req, res) => {
  res.status(404).json({ error: 'API route not found' });
});

// Socket.io connection handling
socketHandler(io);

// Start automation service
const automationService = require('./services/automationService');

// Initialize auto-reply service
const autoReplyService = require('./services/autoReplyService');

// Initialize keyword monitoring service
const keywordMonitoringService = require('./services/keywordMonitoringService');

// Initialize user activity service
const userActivityService = require('./services/userActivityService');

// Database connection and server start
const PORT = process.env.PORT || 3001;

async function startServer() {
  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    await sequelize.sync({ force: false, alter: false });

    // Add missing columns manually
    try {
      await sequelize.query(`ALTER TABLE telegram_accounts ADD COLUMN phone_code_hash VARCHAR(255);`);
      console.log('Added phone_code_hash column');
    } catch (error) {
      if (!error.message.includes('duplicate column name')) {
        console.log('phone_code_hash column already exists or error:', error.message);
      }
    }

    // Add API credential columns to users table
    try {
      await sequelize.query(`ALTER TABLE users ADD COLUMN telegram_api_id VARCHAR(255);`);
      await sequelize.query(`ALTER TABLE users ADD COLUMN telegram_api_hash VARCHAR(255);`);
      await sequelize.query(`ALTER TABLE users ADD COLUMN api_credentials_verified TINYINT(1) DEFAULT 0;`);
      await sequelize.query(`ALTER TABLE users ADD COLUMN api_credentials_verified_at DATETIME;`);
      console.log('Added API credential columns to users table');
    } catch (error) {
      if (!error.message.includes('duplicate column name')) {
        console.log('API credential columns already exist or error:', error.message);
      }
    }
    console.log('Database synchronized.');

    // Initialize Redis connection (disabled for now)
    console.log('Redis disabled - system running without Redis cache');

    // Automation service is already initialized
    console.log('Automation service loaded');

    // Schedule automatic backups in production
    if (process.env.NODE_ENV === 'production') {
      backupService.scheduleAutoBackups();
      console.log('Automatic backups scheduled');
    }

    server.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV}`);
      console.log(`Redis: ${redisService.isReady() ? 'Connected' : 'Disconnected'}`);
    });
  } catch (error) {
    console.error('Unable to start server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
