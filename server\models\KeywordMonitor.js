const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class KeywordMonitor extends Model {}

KeywordMonitor.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'active', 'stopped', 'error'),
    allowNull: false,
    defaultValue: 'pending'
  },
  monitorAllGroups: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  targetGroups: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of group IDs or usernames to monitor'
  },
  excludedGroups: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of group IDs or usernames to exclude'
  },
  notificationsEnabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  notificationMethod: {
    type: DataTypes.ENUM('telegram', 'webhook', 'email'),
    allowNull: true
  },
  notificationTarget: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Telegram username, webhook URL, or email address'
  },
  lastStarted: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastStopped: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastError: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'KeywordMonitor',
  tableName: 'keyword_monitors',
  timestamps: true
});

module.exports = KeywordMonitor; 