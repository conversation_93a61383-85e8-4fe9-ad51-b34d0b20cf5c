const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Analytics = sequelize.define('Analytics', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  eventType: {
    type: DataTypes.ENUM(
      'message_sent',
      'member_added',
      'member_scraped',
      'group_joined',
      'group_left',
      'automation_executed',
      'login',
      'account_added',
      'account_banned',
      'account_limited',
      'payment_made',
      'subscription_upgraded',
      'subscription_expired'
    ),
    allowNull: false
  },
  eventData: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  success: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  timestamp: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
});

// Static methods for analytics
Analytics.logEvent = async function(eventData) {
  try {
    return await this.create(eventData);
  } catch (error) {
    console.error('Failed to log analytics event:', error);
  }
};

Analytics.getStats = async function(userId, options = {}) {
  const { startDate, endDate, eventType, telegramAccountId } = options;
  
  const whereClause = { userId };
  
  if (startDate && endDate) {
    whereClause.timestamp = {
      [sequelize.Op.between]: [startDate, endDate]
    };
  }
  
  if (eventType) {
    whereClause.eventType = eventType;
  }
  
  if (telegramAccountId) {
    whereClause.telegramAccountId = telegramAccountId;
  }
  
  const stats = await this.findAll({
    where: whereClause,
    attributes: [
      'eventType',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.literal('CASE WHEN success = 1 THEN 1 ELSE 0 END')), 'successCount'],
      [sequelize.fn('SUM', sequelize.literal('CASE WHEN success = 0 THEN 1 ELSE 0 END')), 'failureCount']
    ],
    group: ['eventType'],
    raw: true
  });
  
  return stats;
};

Analytics.getDailyStats = async function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const stats = await this.findAll({
    where: {
      userId,
      timestamp: {
        [sequelize.Op.gte]: startDate
      }
    },
    attributes: [
      [sequelize.fn('DATE', sequelize.col('timestamp')), 'date'],
      'eventType',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.literal('CASE WHEN success = 1 THEN 1 ELSE 0 END')), 'successCount']
    ],
    group: [sequelize.fn('DATE', sequelize.col('timestamp')), 'eventType'],
    order: [[sequelize.fn('DATE', sequelize.col('timestamp')), 'ASC']],
    raw: true
  });
  
  return stats;
};

Analytics.getAccountPerformance = async function(userId) {
  const stats = await this.findAll({
    where: { userId, telegramAccountId: { [sequelize.Op.not]: null } },
    attributes: [
      'telegramAccountId',
      'eventType',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.literal('CASE WHEN success = 1 THEN 1 ELSE 0 END')), 'successCount'],
      [sequelize.fn('AVG', sequelize.literal('CASE WHEN success = 1 THEN 1.0 ELSE 0.0 END')), 'successRate']
    ],
    group: ['telegramAccountId', 'eventType'],
    raw: true
  });
  
  return stats;
};

module.exports = Analytics;
