# Database Configuration
DATABASE_URL=sqlite:./database.sqlite

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Telegram API Configuration
TELEGRAM_API_ID=your_telegram_api_id
TELEGRAM_API_HASH=your_telegram_api_hash

# Server Configuration
PORT=3001
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Redis Configuration (Optional but recommended for production)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Stripe Payment Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# 2Captcha Configuration (for captcha solving)
TWOCAPTCHA_API_KEY=your_2captcha_api_key

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=7

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=86400

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Proxy Configuration
DEFAULT_PROXY_TIMEOUT=10000
MAX_PROXY_RETRIES=3

# Account Health Monitoring
HEALTH_CHECK_INTERVAL=3600000
WARMUP_ENABLED=true

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=90
EXPORT_MAX_RECORDS=100000

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_DIR=uploads

# Performance Configuration
DB_CONNECTION_POOL_MAX=10
DB_CONNECTION_POOL_MIN=2
QUEUE_CONCURRENCY=5

# Feature Flags
ENABLE_REDIS=true
ENABLE_ANALYTICS=true
ENABLE_PAYMENTS=true
ENABLE_BACKUPS=true
ENABLE_HEALTH_MONITORING=true
ENABLE_CAPTCHA_SOLVING=false

# Development Configuration
DEBUG_MODE=false
ENABLE_CORS=true
TRUST_PROXY=false
