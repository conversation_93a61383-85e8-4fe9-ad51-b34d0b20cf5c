{"name": "telegram-management-server", "version": "1.0.0", "description": "Backend server for Telegram management system", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest"}, "dependencies": {"2captcha": "^2.1.0", "archiver": "^5.3.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "extract-zip": "^2.0.1", "helmet": "^7.1.0", "https-proxy-agent": "^7.0.6", "input": "^1.0.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "redis": "^4.6.11", "rimraf": "^6.0.1", "sequelize": "^6.35.2", "socket.io": "^4.7.4", "socks-proxy-agent": "^8.0.5", "sqlite3": "^5.1.6", "stripe": "^12.9.0", "telegram": "^2.19.8"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}