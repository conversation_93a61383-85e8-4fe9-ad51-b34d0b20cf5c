const { AccountHealth, TelegramAccount, Analytics } = require('../models');
const { TelegramApi, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');

class AccountHealthService {
  // Initialize health monitoring for a new account
  static async initializeHealthMonitoring(telegramAccountId) {
    try {
      const existingHealth = await AccountHealth.findOne({
        where: { telegramAccountId }
      });

      if (existingHealth) {
        return existingHealth;
      }

      return await AccountHealth.create({
        telegramAccountId,
        healthScore: 100,
        riskLevel: 'low',
        lastHealthCheck: new Date()
      });
    } catch (error) {
      console.error('Initialize health monitoring error:', error);
      throw error;
    }
  }

  // Perform comprehensive health check
  static async performHealthCheck(telegramAccountId) {
    try {
      const account = await TelegramAccount.findByPk(telegramAccountId, {
        include: [{ model: AccountHealth, as: 'health' }]
      });

      if (!account) {
        throw new Error('Account not found');
      }

      let health = account.health;
      if (!health) {
        health = await this.initializeHealthMonitoring(telegramAccountId);
      }

      // Check account status via Telegram API
      const accountStatus = await this.checkTelegramAccountStatus(account);
      
      // Update activity rates
      await this.updateActivityRates(health, account);
      
      // Update health score based on recent activity
      await this.analyzeRecentActivity(health, account);
      
      // Update health score
      await health.updateHealthScore();
      
      // Generate recommendations
      await health.generateRecommendations();

      return health;
    } catch (error) {
      console.error('Health check error:', error);
      throw error;
    }
  }

  // Check Telegram account status
  static async checkTelegramAccountStatus(account) {
    try {
      if (!account.sessionString) {
        return { status: 'not_verified', canConnect: false };
      }

      const apiId = parseInt(process.env.TELEGRAM_API_ID);
      const apiHash = process.env.TELEGRAM_API_HASH;
      
      const client = new TelegramApi(
        new StringSession(account.sessionString),
        apiId,
        apiHash,
        { connectionRetries: 2, timeout: 10000 }
      );

      try {
        await client.connect();
        const me = await client.getMe();
        await client.disconnect();

        // Update account info if needed
        if (account.telegramId !== me.id.toString()) {
          await account.update({
            telegramId: me.id.toString(),
            firstName: me.firstName,
            lastName: me.lastName,
            username: me.username,
            lastActivity: new Date()
          });
        }

        return { 
          status: 'active', 
          canConnect: true,
          userInfo: me
        };
      } catch (error) {
        console.error('Telegram connection error:', error);
        
        // Check for specific error types
        if (error.message.includes('AUTH_KEY_INVALID')) {
          await account.update({ status: 'banned', isActive: false });
          return { status: 'banned', canConnect: false, error: 'AUTH_KEY_INVALID' };
        } else if (error.message.includes('USER_DEACTIVATED')) {
          await account.update({ status: 'banned', isActive: false });
          return { status: 'banned', canConnect: false, error: 'USER_DEACTIVATED' };
        } else if (error.message.includes('FLOOD_WAIT')) {
          await account.update({ status: 'limited' });
          return { status: 'limited', canConnect: false, error: 'FLOOD_WAIT' };
        }

        return { status: 'error', canConnect: false, error: error.message };
      }
    } catch (error) {
      console.error('Check account status error:', error);
      return { status: 'error', canConnect: false, error: error.message };
    }
  }

  // Update activity rates
  static async updateActivityRates(health, account) {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Get recent analytics
    const recentAnalytics = await Analytics.findAll({
      where: {
        telegramAccountId: account.id,
        timestamp: { [Analytics.sequelize.Op.gte]: oneHourAgo }
      }
    });

    const messageEvents = recentAnalytics.filter(a => a.eventType === 'message_sent');
    const addEvents = recentAnalytics.filter(a => a.eventType === 'member_added');

    await health.update({
      messagesPerHour: messageEvents.length,
      addsPerHour: addEvents.length
    });
  }

  // Analyze recent activity for patterns
  static async analyzeRecentActivity(health, account) {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const recentAnalytics = await Analytics.findAll({
      where: {
        telegramAccountId: account.id,
        timestamp: { [Analytics.sequelize.Op.gte]: oneDayAgo }
      },
      order: [['timestamp', 'DESC']]
    });

    // Count consecutive failures
    let consecutiveFailures = 0;
    for (const event of recentAnalytics) {
      if (!event.success) {
        consecutiveFailures++;
      } else {
        break;
      }
    }

    // Analyze activity patterns
    const hourlyActivity = {};
    recentAnalytics.forEach(event => {
      const hour = event.timestamp.getHours();
      if (!hourlyActivity[hour]) {
        hourlyActivity[hour] = { total: 0, successful: 0 };
      }
      hourlyActivity[hour].total++;
      if (event.success) {
        hourlyActivity[hour].successful++;
      }
    });

    await health.update({
      consecutiveFailures,
      activityPattern: hourlyActivity
    });
  }

  // Record various events that affect health
  static async recordBan(telegramAccountId, reason = '') {
    const health = await AccountHealth.findOne({
      where: { telegramAccountId }
    });

    if (health) {
      await health.recordBan();
      
      // Update account status
      await TelegramAccount.update(
        { status: 'banned', isActive: false },
        { where: { id: telegramAccountId } }
      );
    }
  }

  static async recordLimit(telegramAccountId, reason = '') {
    const health = await AccountHealth.findOne({
      where: { telegramAccountId }
    });

    if (health) {
      await health.recordLimit();
      
      // Update account status
      await TelegramAccount.update(
        { status: 'limited' },
        { where: { id: telegramAccountId } }
      );
    }
  }

  static async recordWarning(telegramAccountId, reason = '') {
    const health = await AccountHealth.findOne({
      where: { telegramAccountId }
    });

    if (health) {
      await health.recordWarning();
    }
  }

  static async recordSuccess(telegramAccountId) {
    const health = await AccountHealth.findOne({
      where: { telegramAccountId }
    });

    if (health) {
      await health.recordSuccess();
    }
  }

  // Start account warming process
  static async startWarmup(telegramAccountId) {
    const health = await AccountHealth.findOne({
      where: { telegramAccountId }
    });

    if (health && !health.isWarmingUp) {
      await health.update({
        isWarmingUp: true,
        warmupStartDate: new Date(),
        warmupPhase: 1,
        nextWarmupAction: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours
      });

      return this.scheduleWarmupActions(telegramAccountId);
    }
  }

  // Schedule warmup actions
  static async scheduleWarmupActions(telegramAccountId) {
    const warmupPlan = [
      { phase: 1, duration: 2, actions: ['join_1_group', 'send_2_messages'] },
      { phase: 2, duration: 4, actions: ['join_2_groups', 'send_5_messages'] },
      { phase: 3, duration: 8, actions: ['join_3_groups', 'send_10_messages'] },
      { phase: 4, duration: 12, actions: ['normal_activity'] },
      { phase: 5, duration: 24, actions: ['full_activity'] }
    ];

    // This would integrate with your automation system
    // to schedule the actual warmup actions
    return warmupPlan;
  }

  // Get health summary for all user accounts
  static async getHealthSummary(userId) {
    return await AccountHealth.getHealthSummary(userId);
  }

  // Run health checks for all accounts
  static async runHealthChecksForAllAccounts() {
    const accounts = await TelegramAccount.findAll({
      where: { isActive: true },
      include: [{ model: AccountHealth, as: 'health' }]
    });

    const results = [];
    for (const account of accounts) {
      try {
        const health = await this.performHealthCheck(account.id);
        results.push({ accountId: account.id, health, success: true });
      } catch (error) {
        results.push({ 
          accountId: account.id, 
          error: error.message, 
          success: false 
        });
      }
    }

    return results;
  }

  // Get recommendations for improving account health
  static async getHealthRecommendations(telegramAccountId) {
    const health = await AccountHealth.findOne({
      where: { telegramAccountId },
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['accountName', 'phoneNumber', 'status']
      }]
    });

    if (!health) {
      return { recommendations: [], healthScore: 0 };
    }

    await health.generateRecommendations();
    
    return {
      healthScore: health.healthScore,
      riskLevel: health.riskLevel,
      recommendations: health.recommendations,
      account: health.telegramAccount
    };
  }
}

module.exports = AccountHealthService;
