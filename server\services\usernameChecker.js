const { Telegram<PERSON><PERSON>, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');

class UsernameChecker {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  async checkUsername(username, telegramAccount) {
    // Remove @ if present
    const cleanUsername = username.replace('@', '');
    
    // Check cache first
    const cacheKey = `${cleanUsername}_${telegramAccount.id}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.result;
    }

    try {
      const apiId = parseInt(process.env.TELEGRAM_API_ID);
      const apiHash = process.env.TELEGRAM_API_HASH;
      const client = new TelegramApi(new StringSession(telegramAccount.sessionString), apiId, apiHash);

      await client.connect();

      try {
        // Try to resolve the username
        const result = await client.invoke(new Api.contacts.ResolveUsername({
          username: cleanUsername
        }));

        const isAvailable = false;
        let entityType = 'unknown';
        let entityInfo = null;

        if (result.users && result.users.length > 0) {
          const user = result.users[0];
          entityType = 'user';
          entityInfo = {
            id: user.id.toString(),
            firstName: user.firstName,
            lastName: user.lastName,
            isBot: user.bot || false,
            isVerified: user.verified || false,
            isPremium: user.premium || false
          };
        } else if (result.chats && result.chats.length > 0) {
          const chat = result.chats[0];
          entityType = chat.megagroup ? 'supergroup' : chat.broadcast ? 'channel' : 'group';
          entityInfo = {
            id: chat.id.toString(),
            title: chat.title,
            memberCount: chat.participantsCount,
            isVerified: chat.verified || false
          };
        }

        const checkResult = {
          username: cleanUsername,
          isAvailable,
          entityType,
          entityInfo,
          checkedAt: new Date()
        };

        // Cache the result
        this.cache.set(cacheKey, {
          result: checkResult,
          timestamp: Date.now()
        });

        await client.disconnect();
        return checkResult;

      } catch (error) {
        await client.disconnect();
        
        if (error.message.includes('USERNAME_NOT_OCCUPIED')) {
          const checkResult = {
            username: cleanUsername,
            isAvailable: true,
            entityType: null,
            entityInfo: null,
            checkedAt: new Date()
          };

          // Cache the result
          this.cache.set(cacheKey, {
            result: checkResult,
            timestamp: Date.now()
          });

          return checkResult;
        }
        
        throw error;
      }

    } catch (error) {
      console.error('Username check error:', error);
      throw new Error(`Failed to check username: ${error.message}`);
    }
  }

  async checkMultipleUsernames(usernames, telegramAccount) {
    const results = [];
    
    for (const username of usernames) {
      try {
        const result = await this.checkUsername(username, telegramAccount);
        results.push(result);
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        results.push({
          username: username.replace('@', ''),
          isAvailable: null,
          entityType: null,
          entityInfo: null,
          error: error.message,
          checkedAt: new Date()
        });
      }
    }
    
    return results;
  }

  generateUsernameVariations(baseUsername, count = 10) {
    const variations = [];
    const base = baseUsername.replace('@', '').toLowerCase();
    
    // Add numbers
    for (let i = 1; i <= count; i++) {
      variations.push(`${base}${i}`);
      variations.push(`${base}_${i}`);
      variations.push(`${i}${base}`);
    }
    
    // Add common suffixes
    const suffixes = ['_official', '_real', '_new', '_pro', '_vip', '_bot', '_channel'];
    suffixes.forEach(suffix => {
      variations.push(`${base}${suffix}`);
    });
    
    // Add random numbers
    for (let i = 0; i < 5; i++) {
      const randomNum = Math.floor(Math.random() * 9999);
      variations.push(`${base}${randomNum}`);
    }
    
    return variations.slice(0, count);
  }

  async findAvailableUsernames(baseUsername, telegramAccount, count = 10) {
    const variations = this.generateUsernameVariations(baseUsername, count * 2);
    const results = await this.checkMultipleUsernames(variations, telegramAccount);
    
    return results
      .filter(result => result.isAvailable === true)
      .slice(0, count);
  }

  clearCache() {
    this.cache.clear();
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

module.exports = new UsernameChecker();
