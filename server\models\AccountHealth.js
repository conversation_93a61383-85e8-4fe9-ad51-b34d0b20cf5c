const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AccountHealth = sequelize.define('AccountHealth', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  healthScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 100,
    validate: {
      min: 0,
      max: 100
    }
  },
  riskLevel: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    defaultValue: 'low'
  },
  lastHealthCheck: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  warningCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  banCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  limitCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastBanDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastLimitDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  consecutiveFailures: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  messagesPerHour: {
    type: DataTypes.FLOAT,
    defaultValue: 0
  },
  addsPerHour: {
    type: DataTypes.FLOAT,
    defaultValue: 0
  },
  activityPattern: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  recommendations: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  isWarmingUp: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  warmupStartDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  warmupPhase: {
    type: DataTypes.INTEGER,
    defaultValue: 0 // 0-5 phases
  },
  nextWarmupAction: {
    type: DataTypes.DATE,
    allowNull: true
  }
});

// Instance methods
AccountHealth.prototype.updateHealthScore = function() {
  let score = 100;
  
  // Deduct points for bans and limits
  score -= this.banCount * 20;
  score -= this.limitCount * 10;
  score -= this.warningCount * 5;
  score -= this.consecutiveFailures * 3;
  
  // Deduct points for high activity rates
  if (this.messagesPerHour > 50) score -= 15;
  else if (this.messagesPerHour > 30) score -= 10;
  else if (this.messagesPerHour > 20) score -= 5;
  
  if (this.addsPerHour > 20) score -= 20;
  else if (this.addsPerHour > 10) score -= 10;
  else if (this.addsPerHour > 5) score -= 5;
  
  // Recent bans/limits are more critical
  const now = new Date();
  if (this.lastBanDate) {
    const daysSinceBan = (now - this.lastBanDate) / (1000 * 60 * 60 * 24);
    if (daysSinceBan < 1) score -= 30;
    else if (daysSinceBan < 7) score -= 20;
    else if (daysSinceBan < 30) score -= 10;
  }
  
  if (this.lastLimitDate) {
    const daysSinceLimit = (now - this.lastLimitDate) / (1000 * 60 * 60 * 24);
    if (daysSinceLimit < 1) score -= 20;
    else if (daysSinceLimit < 7) score -= 10;
    else if (daysSinceLimit < 30) score -= 5;
  }
  
  this.healthScore = Math.max(0, Math.min(100, score));
  
  // Update risk level
  if (this.healthScore >= 80) this.riskLevel = 'low';
  else if (this.healthScore >= 60) this.riskLevel = 'medium';
  else if (this.healthScore >= 30) this.riskLevel = 'high';
  else this.riskLevel = 'critical';
  
  this.lastHealthCheck = new Date();
  return this.save();
};

AccountHealth.prototype.recordBan = function() {
  this.banCount += 1;
  this.lastBanDate = new Date();
  this.consecutiveFailures += 1;
  return this.updateHealthScore();
};

AccountHealth.prototype.recordLimit = function() {
  this.limitCount += 1;
  this.lastLimitDate = new Date();
  this.consecutiveFailures += 1;
  return this.updateHealthScore();
};

AccountHealth.prototype.recordWarning = function() {
  this.warningCount += 1;
  this.consecutiveFailures += 1;
  return this.updateHealthScore();
};

AccountHealth.prototype.recordSuccess = function() {
  this.consecutiveFailures = 0;
  return this.updateHealthScore();
};

AccountHealth.prototype.generateRecommendations = function() {
  const recommendations = [];
  
  if (this.healthScore < 50) {
    recommendations.push({
      type: 'critical',
      message: 'Account health is critical. Consider pausing all activities.',
      action: 'pause_activities'
    });
  }
  
  if (this.banCount > 0) {
    recommendations.push({
      type: 'warning',
      message: 'Account has been banned before. Use with extreme caution.',
      action: 'reduce_activity'
    });
  }
  
  if (this.messagesPerHour > 30) {
    recommendations.push({
      type: 'warning',
      message: 'Message rate is too high. Reduce sending frequency.',
      action: 'reduce_message_rate'
    });
  }
  
  if (this.addsPerHour > 10) {
    recommendations.push({
      type: 'warning',
      message: 'Member adding rate is too high. Increase delays.',
      action: 'reduce_add_rate'
    });
  }
  
  if (!this.isWarmingUp && this.healthScore > 80) {
    recommendations.push({
      type: 'info',
      message: 'Account is healthy. Consider starting warmup routine.',
      action: 'start_warmup'
    });
  }
  
  this.recommendations = recommendations;
  return this.save();
};

// Static methods
AccountHealth.checkAllAccounts = async function() {
  const accounts = await this.findAll({
    include: [{
      model: require('./TelegramAccount'),
      where: { isActive: true }
    }]
  });
  
  for (const health of accounts) {
    await health.updateHealthScore();
    await health.generateRecommendations();
  }
  
  return accounts;
};

AccountHealth.getHealthSummary = async function(userId) {
  const { TelegramAccount } = require('./index');
  
  const healthData = await this.findAll({
    include: [{
      model: TelegramAccount,
      where: { userId },
      attributes: ['id', 'accountName', 'phoneNumber', 'isActive']
    }],
    order: [['healthScore', 'ASC']]
  });
  
  const summary = {
    total: healthData.length,
    healthy: healthData.filter(h => h.healthScore >= 80).length,
    warning: healthData.filter(h => h.healthScore >= 60 && h.healthScore < 80).length,
    critical: healthData.filter(h => h.healthScore < 60).length,
    averageScore: healthData.reduce((sum, h) => sum + h.healthScore, 0) / healthData.length || 0
  };
  
  return { summary, accounts: healthData };
};

module.exports = AccountHealth;
