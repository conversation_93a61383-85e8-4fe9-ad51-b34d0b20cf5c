# 🔧 Telegram API Setup Guide

## ❌ **Current Issue: "Failed to send verification code"**

The error occurs because the Telegram API credentials are not properly configured. Here's how to fix it:

## 🚀 **Step 1: Get Telegram API Credentials**

### **1.1 Visit Telegram API Website**
Go to: **https://my.telegram.org/apps**

### **1.2 Login with Your Phone Number**
- Enter your phone number (the one associated with your Telegram account)
- You'll receive a verification code via Telegram
- Enter the code to login

### **1.3 Create a New Application**
- Click **"Create new application"**
- Fill in the required information:
  - **App title**: `Telegram Management System`
  - **Short name**: `tg-management`
  - **URL**: `http://localhost:3000` (or your domain)
  - **Platform**: `Web`
  - **Description**: `Telegram automation and management system`

### **1.4 Get Your Credentials**
After creating the app, you'll see:
- **API ID**: A number (e.g., `********`)
- **API Hash**: A string (e.g., `abcdef********90abcdef********90`)

## 🔧 **Step 2: Configure Your Environment**

### **2.1 Update .env File**
Open `server/.env` and replace the placeholder values:

```env
# BEFORE (placeholder values)
TELEGRAM_API_ID=12345
TELEGRAM_API_HASH=your-telegram-api-hash

# AFTER (your actual values)
TELEGRAM_API_ID=********
TELEGRAM_API_HASH=abcdef********90abcdef********90
```

### **2.2 Example .env Configuration**
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=telegram_management
DB_USER=postgres
DB_PASSWORD=password

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Telegram API Configuration - REPLACE WITH YOUR VALUES
TELEGRAM_API_ID=********
TELEGRAM_API_HASH=abcdef********90abcdef********90

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Frontend URL
CLIENT_URL=http://localhost:3000
```

## 🔄 **Step 3: Restart the Server**

After updating the .env file:

```bash
cd server
npm run dev
```

## ✅ **Step 4: Test Account Verification**

### **4.1 Add a Telegram Account**
1. Go to your web interface
2. Navigate to "Accounts" section
3. Click "Add Account"
4. Enter a phone number in international format: `+********90`
5. Click "Send Code"

### **4.2 Expected Behavior**
- ✅ **Success**: "Verification code sent to your phone"
- ❌ **Error**: "Telegram API credentials not configured"

### **4.3 Enter Verification Code**
1. Check your Telegram app for the verification code
2. Enter the 5-digit code in the verification field
3. Click "Verify"

### **4.4 Handle 2FA (if enabled)**
If you have two-factor authentication enabled:
1. You'll be prompted for your 2FA password
2. Enter your cloud password
3. Click "Verify"

## 🔍 **Troubleshooting**

### **Error: "Invalid Telegram API credentials"**
- Double-check your API ID and Hash
- Make sure there are no extra spaces
- Ensure you're using the correct values from https://my.telegram.org/apps

### **Error: "Invalid phone number format"**
- Use international format: `+********90`
- Include the country code
- No spaces or special characters except `+`

### **Error: "This phone number is banned"**
- The phone number has been banned by Telegram
- Try with a different phone number
- Contact Telegram support if needed

### **Error: "Too many requests"**
- You've hit Telegram's rate limit
- Wait 10-15 minutes before trying again
- Don't spam the verification requests

### **Error: "FLOOD_WAIT_X"**
- Telegram is rate limiting your requests
- Wait X seconds before trying again
- This is normal behavior for high-frequency requests

## 📱 **Supported Phone Number Formats**

✅ **Correct formats:**
- `+********90`
- `+44********9`
- `+91********9`

❌ **Incorrect formats:**
- `********90` (missing country code)
- `****** 567 890` (spaces)
- `******-567-890` (dashes)
- `+1 (234) 567-890` (parentheses)

## 🔐 **Security Notes**

### **Keep Your Credentials Safe**
- Never share your API ID and Hash
- Don't commit them to public repositories
- Use environment variables in production
- Consider using a secrets management service

### **API Limits**
- Telegram has rate limits for API calls
- Don't exceed 20 requests per minute
- Implement proper delays between requests
- Monitor your usage to avoid bans

## 🚀 **Next Steps After Setup**

Once verification is working:

1. **✅ Add Multiple Accounts**: You can add multiple Telegram accounts
2. **✅ Start Scraping**: Scrape members from groups
3. **✅ Import Data**: Import existing account data
4. **✅ Automation**: Set up automated tasks
5. **✅ Analytics**: Monitor your activities

## 📞 **Support**

If you're still having issues:

1. **Check Server Logs**: Look for detailed error messages
2. **Verify API Credentials**: Double-check your API ID and Hash
3. **Test with Different Phone**: Try a different phone number
4. **Check Telegram Status**: Ensure Telegram services are operational

## 🎉 **Success Indicators**

You'll know it's working when:
- ✅ "Verification code sent to your phone" message appears
- ✅ You receive a code in your Telegram app
- ✅ Account verification completes successfully
- ✅ Account appears in your dashboard with "Active" status

**Once this is set up, your Telegram automation system will be fully operational!** 🚀
