const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProxyConfig = sequelize.define('ProxyConfig', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  host: {
    type: DataTypes.STRING,
    allowNull: false
  },
  port: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 1,
      max: 65535
    }
  },
  username: {
    type: DataTypes.STRING,
    allowNull: true
  },
  password: {
    type: DataTypes.STRING,
    allowNull: true
  },
  proxyType: {
    type: DataTypes.ENUM('http', 'https', 'socks4', 'socks5'),
    defaultValue: 'http'
  },
  country: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  lastTested: {
    type: DataTypes.DATE,
    allowNull: true
  },
  responseTime: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Response time in milliseconds'
  },
  successRate: {
    type: DataTypes.FLOAT,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  totalTests: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  successfulTests: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastError: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  externalIp: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'External IP address when using this proxy'
  },
  provider: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [0, 100]
    }
  },
  maxConcurrentConnections: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    validate: {
      min: 1,
      max: 100
    }
  },
  currentConnections: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  }
});

// Instance methods
ProxyConfig.prototype.testConnection = async function() {
  const axios = require('axios');
  const { HttpsProxyAgent } = require('https-proxy-agent');
  
  try {
    const startTime = Date.now();
    const proxyUrl = `${this.proxyType}://${this.username ? `${this.username}:${this.password}@` : ''}${this.host}:${this.port}`;
    
    const agent = new HttpsProxyAgent(proxyUrl);
    
    await axios.get('https://httpbin.org/ip', {
      httpsAgent: agent,
      timeout: 10000
    });
    
    const responseTime = Date.now() - startTime;
    
    this.totalTests += 1;
    this.successfulTests += 1;
    this.responseTime = responseTime;
    this.successRate = (this.successfulTests / this.totalTests) * 100;
    this.lastTested = new Date();
    this.lastError = null;
    
    await this.save();
    return { success: true, responseTime };
    
  } catch (error) {
    this.totalTests += 1;
    this.successRate = (this.successfulTests / this.totalTests) * 100;
    this.lastTested = new Date();
    this.lastError = error.message;
    
    await this.save();
    return { success: false, error: error.message };
  }
};

ProxyConfig.prototype.getConnectionString = function() {
  return `${this.proxyType}://${this.username ? `${this.username}:${this.password}@` : ''}${this.host}:${this.port}`;
};

module.exports = ProxyConfig;
