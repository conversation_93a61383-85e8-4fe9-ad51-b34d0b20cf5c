const fs = require('fs').promises;
const path = require('path');
const archiver = require('archiver');
const extract = require('extract-zip');
const { sequelize } = require('../config/database');
const { 
  User, TelegramAccount, ScrapedMember, AutomationTask, 
  MessageTemplate, ProxyConfig, GroupManagement, Analytics, 
  Payment, AccountHealth 
} = require('../models');

class BackupService {
  constructor() {
    this.backupDir = path.join(__dirname, '../backups');
    this.ensureBackupDir();
  }

  async ensureBackupDir() {
    try {
      await fs.mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      console.error('Error creating backup directory:', error);
    }
  }

  // Create full system backup
  async createFullBackup(userId = null, options = {}) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupName = userId 
        ? `user_${userId}_backup_${timestamp}`
        : `full_system_backup_${timestamp}`;
      
      const backupPath = path.join(this.backupDir, `${backupName}.zip`);
      
      // Create backup data
      const backupData = await this.gatherBackupData(userId, options);
      
      // Create ZIP archive
      await this.createZipArchive(backupData, backupPath, backupName);
      
      return {
        success: true,
        backupPath,
        backupName,
        size: await this.getFileSize(backupPath),
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Create backup error:', error);
      throw error;
    }
  }

  // Gather all data for backup
  async gatherBackupData(userId = null, options = {}) {
    const data = {};
    
    try {
      // Base where clause for user-specific backups
      const userWhere = userId ? { userId } : {};
      
      // Users data
      if (!userId || options.includeUserData) {
        data.users = await User.findAll({
          where: userId ? { id: userId } : {},
          attributes: { exclude: ['password'] } // Don't backup passwords
        });
      }

      // Telegram accounts
      data.telegramAccounts = await TelegramAccount.findAll({
        where: userWhere,
        attributes: { exclude: ['sessionString'] } // Don't backup session strings for security
      });

      // Scraped members
      data.scrapedMembers = await ScrapedMember.findAll({
        where: userWhere,
        limit: options.memberLimit || 50000 // Limit to prevent huge backups
      });

      // Automation tasks
      data.automationTasks = await AutomationTask.findAll({
        where: userWhere
      });

      // Message templates
      data.messageTemplates = await MessageTemplate.findAll({
        where: userWhere
      });

      // Proxy configurations
      data.proxyConfigs = await ProxyConfig.findAll({
        where: userWhere
      });

      // Group management
      data.groupManagement = await GroupManagement.findAll({
        where: userWhere
      });

      // Analytics (last 90 days to keep size manageable)
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
      
      data.analytics = await Analytics.findAll({
        where: {
          ...userWhere,
          timestamp: { [sequelize.Op.gte]: ninetyDaysAgo }
        }
      });

      // Payments
      data.payments = await Payment.findAll({
        where: userWhere
      });

      // Account health
      if (data.telegramAccounts.length > 0) {
        const accountIds = data.telegramAccounts.map(acc => acc.id);
        data.accountHealth = await AccountHealth.findAll({
          where: { telegramAccountId: { [sequelize.Op.in]: accountIds } }
        });
      }

      // Add metadata
      data.metadata = {
        backupDate: new Date(),
        version: '1.0.0',
        userId: userId,
        recordCounts: {
          users: data.users?.length || 0,
          telegramAccounts: data.telegramAccounts?.length || 0,
          scrapedMembers: data.scrapedMembers?.length || 0,
          automationTasks: data.automationTasks?.length || 0,
          messageTemplates: data.messageTemplates?.length || 0,
          proxyConfigs: data.proxyConfigs?.length || 0,
          groupManagement: data.groupManagement?.length || 0,
          analytics: data.analytics?.length || 0,
          payments: data.payments?.length || 0,
          accountHealth: data.accountHealth?.length || 0
        }
      };

      return data;
    } catch (error) {
      console.error('Gather backup data error:', error);
      throw error;
    }
  }

  // Create ZIP archive
  async createZipArchive(data, outputPath, backupName) {
    return new Promise((resolve, reject) => {
      const output = require('fs').createWriteStream(outputPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        console.log(`Backup created: ${archive.pointer()} total bytes`);
        resolve();
      });

      archive.on('error', (err) => {
        reject(err);
      });

      archive.pipe(output);

      // Add data files to archive
      Object.keys(data).forEach(key => {
        const jsonData = JSON.stringify(data[key], null, 2);
        archive.append(jsonData, { name: `${key}.json` });
      });

      // Add README
      const readme = this.generateBackupReadme(data.metadata);
      archive.append(readme, { name: 'README.txt' });

      archive.finalize();
    });
  }

  // Generate backup README
  generateBackupReadme(metadata) {
    return `
Telegram Management System Backup
=================================

Backup Date: ${metadata.backupDate}
Version: ${metadata.version}
User ID: ${metadata.userId || 'All Users'}

Record Counts:
${Object.entries(metadata.recordCounts)
  .map(([key, count]) => `- ${key}: ${count}`)
  .join('\n')}

Files Included:
- users.json: User account data (passwords excluded)
- telegramAccounts.json: Telegram account configurations (session strings excluded)
- scrapedMembers.json: Scraped member data
- automationTasks.json: Automation task configurations
- messageTemplates.json: Message templates
- proxyConfigs.json: Proxy configurations
- groupManagement.json: Group management settings
- analytics.json: Analytics data (last 90 days)
- payments.json: Payment history
- accountHealth.json: Account health monitoring data
- metadata.json: Backup metadata

Security Notes:
- Passwords and session strings are excluded for security
- This backup should be stored securely
- Do not share this backup with unauthorized users

To restore this backup, use the restore functionality in the admin panel.
    `.trim();
  }

  // Restore from backup
  async restoreFromBackup(backupPath, options = {}) {
    try {
      const tempDir = path.join(this.backupDir, 'temp_restore');
      
      // Extract backup
      await extract(backupPath, { dir: tempDir });
      
      // Read metadata
      const metadataPath = path.join(tempDir, 'metadata.json');
      const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
      
      console.log('Restoring backup from:', metadata.backupDate);
      
      // Start transaction
      const transaction = await sequelize.transaction();
      
      try {
        const results = {};
        
        // Restore in dependency order
        const restoreOrder = [
          'users',
          'telegramAccounts', 
          'scrapedMembers',
          'automationTasks',
          'messageTemplates',
          'proxyConfigs',
          'groupManagement',
          'analytics',
          'payments',
          'accountHealth'
        ];

        for (const tableName of restoreOrder) {
          const filePath = path.join(tempDir, `${tableName}.json`);
          
          try {
            const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
            if (!fileExists) continue;
            
            const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
            if (!data || data.length === 0) continue;
            
            results[tableName] = await this.restoreTableData(tableName, data, options, transaction);
          } catch (error) {
            console.error(`Error restoring ${tableName}:`, error);
            if (!options.continueOnError) throw error;
          }
        }
        
        await transaction.commit();
        
        // Cleanup temp directory
        await fs.rmdir(tempDir, { recursive: true });
        
        return {
          success: true,
          metadata,
          results
        };
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      console.error('Restore backup error:', error);
      throw error;
    }
  }

  // Restore data for specific table
  async restoreTableData(tableName, data, options, transaction) {
    const models = {
      users: User,
      telegramAccounts: TelegramAccount,
      scrapedMembers: ScrapedMember,
      automationTasks: AutomationTask,
      messageTemplates: MessageTemplate,
      proxyConfigs: ProxyConfig,
      groupManagement: GroupManagement,
      analytics: Analytics,
      payments: Payment,
      accountHealth: AccountHealth
    };

    const model = models[tableName];
    if (!model) {
      throw new Error(`Unknown table: ${tableName}`);
    }

    let created = 0;
    let updated = 0;
    let skipped = 0;

    for (const record of data) {
      try {
        if (options.replaceExisting) {
          // Replace existing records
          const [instance, wasCreated] = await model.upsert(record, { transaction });
          if (wasCreated) created++;
          else updated++;
        } else {
          // Only create if doesn't exist
          const existing = await model.findByPk(record.id, { transaction });
          if (existing) {
            skipped++;
          } else {
            await model.create(record, { transaction });
            created++;
          }
        }
      } catch (error) {
        console.error(`Error restoring record in ${tableName}:`, error);
        if (!options.continueOnError) throw error;
        skipped++;
      }
    }

    return { created, updated, skipped };
  }

  // List available backups
  async listBackups() {
    try {
      const files = await fs.readdir(this.backupDir);
      const backups = [];

      for (const file of files) {
        if (file.endsWith('.zip')) {
          const filePath = path.join(this.backupDir, file);
          const stats = await fs.stat(filePath);
          
          backups.push({
            name: file,
            path: filePath,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          });
        }
      }

      return backups.sort((a, b) => b.created - a.created);
    } catch (error) {
      console.error('List backups error:', error);
      throw error;
    }
  }

  // Delete backup
  async deleteBackup(backupName) {
    try {
      const backupPath = path.join(this.backupDir, backupName);
      await fs.unlink(backupPath);
      return true;
    } catch (error) {
      console.error('Delete backup error:', error);
      throw error;
    }
  }

  // Get file size
  async getFileSize(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  // Schedule automatic backups
  scheduleAutoBackups() {
    const cron = require('node-cron');
    
    // Daily backup at 2 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        console.log('Starting scheduled backup...');
        await this.createFullBackup(null, { memberLimit: 10000 });
        console.log('Scheduled backup completed');
        
        // Cleanup old backups (keep last 7 days)
        await this.cleanupOldBackups(7);
      } catch (error) {
        console.error('Scheduled backup failed:', error);
      }
    });
  }

  // Cleanup old backups
  async cleanupOldBackups(keepDays = 7) {
    try {
      const backups = await this.listBackups();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - keepDays);

      for (const backup of backups) {
        if (backup.created < cutoffDate) {
          await this.deleteBackup(backup.name);
          console.log(`Deleted old backup: ${backup.name}`);
        }
      }
    } catch (error) {
      console.error('Cleanup old backups error:', error);
    }
  }
}

module.exports = new BackupService();
