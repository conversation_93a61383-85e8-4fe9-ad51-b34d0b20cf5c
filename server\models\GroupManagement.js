const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const GroupManagement = sequelize.define('GroupManagement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  groupId: {
    type: DataTypes.BIGINT,
    allowNull: false
  },
  groupTitle: {
    type: DataTypes.STRING,
    allowNull: false
  },
  groupUsername: {
    type: DataTypes.STRING,
    allowNull: true
  },
  groupType: {
    type: DataTypes.ENUM('group', 'supergroup', 'channel'),
    allowNull: false
  },
  memberCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  adminRights: {
    type: DataTypes.JSON,
    defaultValue: {},
    comment: 'Admin permissions in the group'
  },
  joinDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lastActivity: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('active', 'left', 'banned', 'restricted'),
    defaultValue: 'active'
  },
  autoJoinEnabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  autoLeaveEnabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  autoLeaveAfterDays: {
    type: DataTypes.INTEGER,
    defaultValue: 30
  },
  autoReactEnabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  autoReactEmojis: {
    type: DataTypes.JSON,
    defaultValue: ['👍', '❤️', '🔥']
  },
  autoForwardEnabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  forwardToGroups: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  messagesSent: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  membersAdded: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
});

// Instance methods
GroupManagement.prototype.updateMemberCount = function(count) {
  this.memberCount = count;
  this.lastActivity = new Date();
  return this.save();
};

GroupManagement.prototype.incrementMessageCount = function() {
  this.messagesSent += 1;
  this.lastActivity = new Date();
  return this.save();
};

GroupManagement.prototype.incrementMemberCount = function() {
  this.membersAdded += 1;
  this.lastActivity = new Date();
  return this.save();
};

GroupManagement.prototype.shouldAutoLeave = function() {
  if (!this.autoLeaveEnabled) return false;
  
  const daysSinceJoin = Math.floor((new Date() - this.joinDate) / (1000 * 60 * 60 * 24));
  return daysSinceJoin >= this.autoLeaveAfterDays;
};

GroupManagement.prototype.getRandomReactEmoji = function() {
  if (!this.autoReactEnabled || !this.autoReactEmojis.length) return null;
  
  const randomIndex = Math.floor(Math.random() * this.autoReactEmojis.length);
  return this.autoReactEmojis[randomIndex];
};

module.exports = GroupManagement;
