const { Telegram<PERSON>pi, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');
const { ScrapedMember } = require('../models');
const DeviceSpoofingService = require('./DeviceSpoofingService');
const ProxyService = require('./ProxyService');

class MemberScrapingService {
  constructor() {
    this.activeClients = new Map();
  }

  async scrapeMembers(account, groupUsername, options = {}) {
    const {
      limit = 1000,
      includeInactive = false,
      skipBots = true,
      onProgress = null,
      user = null
    } = options;

    // Use user's API credentials if provided, otherwise fall back to account's user
    let apiId, apiHash;
    if (user && user.telegramApiId && user.telegramApiHash) {
      apiId = parseInt(user.telegramApiId);
      apiHash = user.telegramApiHash;
    } else {
      // This is a fallback - in the new system, we should always have user credentials
      apiId = parseInt(process.env.TELEGRAM_API_ID);
      apiHash = process.env.TELEGRAM_API_HASH;
    }
    
    // Get device fingerprint and proxy configuration
    const deviceFingerprint = await DeviceSpoofingService.getDeviceFingerprint(
      account.userId,
      account.id
    );

    const proxyConfig = await ProxyService.getOptimalProxy(
      account.userId,
      account.id
    );

    // Create client with enhanced security
    const clientOptions = {
      connectionRetries: 5,
      retryDelay: 1000,
      timeout: 30000,
      deviceModel: deviceFingerprint.device_model,
      systemVersion: deviceFingerprint.system_version,
      appVersion: deviceFingerprint.app_version,
      langCode: deviceFingerprint.lang_code,
      systemLangCode: deviceFingerprint.system_lang_code,
      langPack: deviceFingerprint.lang_pack
    };

    // Add proxy if available
    if (proxyConfig) {
      clientOptions.proxy = proxyConfig;
      console.log(`Using proxy for account ${account.id}: ${proxyConfig.addr}:${proxyConfig.port} (${proxyConfig.country})`);
    }

    const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash, clientOptions);

    try {
      await client.connect();
      
      // Get group entity with multiple fallback methods
      const group = await this.getGroupEntity(client, groupUsername);
      
      // Get participants using the best available method
      const participants = await this.getParticipants(client, group, limit, onProgress);
      
      // Process and save members
      const result = await this.processMembers(
        participants, 
        account, 
        group, 
        groupUsername, 
        { includeInactive, skipBots }
      );

      await client.disconnect();
      return result;

    } catch (error) {
      try {
        await client.disconnect();
      } catch (disconnectError) {
        console.error('Disconnect error:', disconnectError);
      }
      throw error;
    }
  }

  async getGroupEntity(client, groupUsername) {
    const attempts = [
      groupUsername,
      groupUsername.startsWith('@') ? groupUsername : `@${groupUsername}`,
      groupUsername.startsWith('@') ? groupUsername.slice(1) : groupUsername
    ];

    for (const attempt of attempts) {
      try {
        return await client.getEntity(attempt);
      } catch (error) {
        console.log(`Failed to get entity with "${attempt}":`, error.message);
        continue;
      }
    }

    throw new Error('Group not found with any username variation');
  }

  async getParticipants(client, group, limit, onProgress) {
    const methods = [
      // Method 1: Standard getParticipants
      async () => {
        return await client.getParticipants(group, { 
          limit,
          aggressive: true 
        });
      },
      
      // Method 2: Using iterParticipants
      async () => {
        const participants = [];
        const iterator = client.iterParticipants(group, { limit });
        
        for await (const participant of iterator) {
          participants.push(participant);
          if (onProgress) {
            onProgress(participants.length, limit);
          }
          if (participants.length >= limit) break;
        }
        
        return participants;
      },

      // Method 3: Chunked approach
      async () => {
        const participants = [];
        const chunkSize = 200;
        let offset = 0;

        while (participants.length < limit) {
          const chunk = await client.getParticipants(group, {
            limit: Math.min(chunkSize, limit - participants.length),
            offset
          });

          if (chunk.length === 0) break;
          
          participants.push(...chunk);
          offset += chunk.length;
          
          if (onProgress) {
            onProgress(participants.length, limit);
          }

          // Human-like delay between chunks
          const delay = DeviceSpoofingService.generateHumanDelay('reading');
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        return participants;
      }
    ];

    for (const [index, method] of methods.entries()) {
      try {
        console.log(`Trying scraping method ${index + 1}...`);
        const result = await method();
        console.log(`Method ${index + 1} succeeded, got ${result.length} participants`);
        return result;
      } catch (error) {
        console.log(`Method ${index + 1} failed:`, error.message);
        if (index === methods.length - 1) {
          throw error;
        }
      }
    }
  }

  async processMembers(participants, account, group, groupUsername, options) {
    const { includeInactive, skipBots } = options;
    let scrapedCount = 0;
    const errors = [];
    const duplicates = [];

    for (const participant of participants) {
      try {
        // Skip bots if requested
        if (skipBots && participant.bot) {
          continue;
        }

        // Skip inactive users if requested
        if (!includeInactive && this.isInactiveUser(participant)) {
          continue;
        }

        // Check if member already exists
        const existingMember = await ScrapedMember.findOne({
          where: { 
            telegramId: participant.id.toString(),
            userId: account.userId 
          }
        });

        if (existingMember) {
          duplicates.push(participant.id.toString());
          continue;
        }

        // Create member record
        await ScrapedMember.create({
          userId: account.userId,
          telegramAccountId: account.id,
          telegramId: participant.id.toString(),
          username: participant.username,
          firstName: participant.firstName,
          lastName: participant.lastName,
          phoneNumber: participant.phone,
          isBot: participant.bot || false,
          isVerified: participant.verified || false,
          isPremium: participant.premium || false,
          isRestricted: participant.restricted || false,
          isScam: participant.scam || false,
          isFake: participant.fake || false,
          sourceGroupId: group.id.toString(),
          sourceGroupTitle: group.title,
          sourceGroupUsername: groupUsername,
          status: 'active',
          lastSeen: participant.status ? this.parseLastSeen(participant.status) : null,
          scrapedAt: new Date()
        });

        scrapedCount++;

      } catch (memberError) {
        errors.push(`Failed to save member ${participant.id}: ${memberError.message}`);
      }
    }

    return {
      scrapedCount,
      totalFound: participants.length,
      duplicates: duplicates.length,
      errors
    };
  }

  isInactiveUser(participant) {
    // Consider user inactive if they haven't been online for a long time
    if (!participant.status) return false;
    
    const status = participant.status;
    if (status.className === 'UserStatusOffline') {
      const wasOnline = status.wasOnline;
      if (wasOnline) {
        const daysSinceOnline = (Date.now() - wasOnline * 1000) / (1000 * 60 * 60 * 24);
        return daysSinceOnline > 30; // Consider inactive if offline for more than 30 days
      }
    }
    
    return false;
  }

  parseLastSeen(status) {
    if (!status) return null;
    
    switch (status.className) {
      case 'UserStatusOnline':
        return new Date();
      case 'UserStatusOffline':
        return status.wasOnline ? new Date(status.wasOnline * 1000) : null;
      case 'UserStatusRecently':
        return new Date(Date.now() - 3 * 24 * 60 * 60 * 1000); // ~3 days ago
      case 'UserStatusLastWeek':
        return new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // ~1 week ago
      case 'UserStatusLastMonth':
        return new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // ~1 month ago
      default:
        return null;
    }
  }

  async getGroupInfo(account, groupUsername, user = null) {
    // Use user's API credentials if provided
    let apiId, apiHash;
    if (user && user.telegramApiId && user.telegramApiHash) {
      apiId = parseInt(user.telegramApiId);
      apiHash = user.telegramApiHash;
    } else {
      // Fallback to environment variables
      apiId = parseInt(process.env.TELEGRAM_API_ID);
      apiHash = process.env.TELEGRAM_API_HASH;
    }
    
    const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);

    try {
      await client.connect();
      const group = await this.getGroupEntity(client, groupUsername);
      
      // Get additional group info
      const fullGroup = await client.invoke(new Api.channels.GetFullChannel({
        channel: group
      }));

      await client.disconnect();

      return {
        id: group.id.toString(),
        title: group.title,
        username: group.username,
        participantsCount: group.participantsCount,
        description: fullGroup.fullChat.about,
        isChannel: group.broadcast,
        isMegagroup: group.megagroup,
        isPublic: !group.accessHash,
        createdDate: group.date ? new Date(group.date * 1000) : null
      };

    } catch (error) {
      try {
        await client.disconnect();
      } catch (disconnectError) {
        console.error('Disconnect error:', disconnectError);
      }
      throw error;
    }
  }
}

module.exports = new MemberScrapingService();
