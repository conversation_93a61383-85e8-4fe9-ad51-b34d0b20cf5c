const express = require('express');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { TelegramAccount, MessageTemplate, ProxyConfig } = require('../models');
const usernameChecker = require('../services/usernameChecker');
const phoneFormatter = require('../services/phoneFormatter');

const router = express.Router();

// Username checker
router.post('/check-username', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { username, telegramAccountId } = req.body;

    if (!username || !telegramAccountId) {
      return res.status(400).json({ error: 'Username and account ID are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const result = await usernameChecker.checkUsername(username, account);

    res.json(result);
  } catch (error) {
    console.error('Username check error:', error);
    res.status(500).json({ error: error.message || 'Failed to check username' });
  }
});

// Bulk username checker
router.post('/check-usernames-bulk', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { usernames, telegramAccountId } = req.body;

    if (!usernames || !Array.isArray(usernames) || !telegramAccountId) {
      return res.status(400).json({ error: 'Usernames array and account ID are required' });
    }

    if (usernames.length > 50) {
      return res.status(400).json({ error: 'Maximum 50 usernames allowed per request' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const results = await usernameChecker.checkMultipleUsernames(usernames, account);

    res.json({ results });
  } catch (error) {
    console.error('Bulk username check error:', error);
    res.status(500).json({ error: 'Failed to check usernames' });
  }
});

// Find available usernames
router.post('/find-available-usernames', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { baseUsername, telegramAccountId, count = 10 } = req.body;

    if (!baseUsername || !telegramAccountId) {
      return res.status(400).json({ error: 'Base username and account ID are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const availableUsernames = await usernameChecker.findAvailableUsernames(
      baseUsername, 
      account, 
      Math.min(count, 20)
    );

    res.json({ availableUsernames });
  } catch (error) {
    console.error('Find available usernames error:', error);
    res.status(500).json({ error: 'Failed to find available usernames' });
  }
});

// Phone number formatter
router.post('/format-phone', authenticateToken, async (req, res) => {
  try {
    const { phoneNumber, countryCode } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({ error: 'Phone number is required' });
    }

    const formatted = phoneFormatter.formatPhone(phoneNumber, countryCode);
    const isValid = phoneFormatter.validatePhone(phoneNumber);
    const countryInfo = phoneFormatter.extractCountryCode(phoneNumber);
    const telegramFormat = phoneFormatter.formatForTelegram(phoneNumber);

    res.json({
      original: phoneNumber,
      formatted,
      telegramFormat,
      isValid,
      countryInfo
    });
  } catch (error) {
    console.error('Phone format error:', error);
    res.status(500).json({ error: 'Failed to format phone number' });
  }
});

// Bulk phone formatter
router.post('/format-phones-bulk', authenticateToken, async (req, res) => {
  try {
    const { phoneNumbers, countryCode } = req.body;

    if (!phoneNumbers || !Array.isArray(phoneNumbers)) {
      return res.status(400).json({ error: 'Phone numbers array is required' });
    }

    if (phoneNumbers.length > 100) {
      return res.status(400).json({ error: 'Maximum 100 phone numbers allowed per request' });
    }

    const results = phoneNumbers.map(phoneNumber => {
      try {
        const formatted = phoneFormatter.formatPhone(phoneNumber, countryCode);
        const isValid = phoneFormatter.validatePhone(phoneNumber);
        const countryInfo = phoneFormatter.extractCountryCode(phoneNumber);
        const telegramFormat = phoneFormatter.formatForTelegram(phoneNumber);

        return {
          original: phoneNumber,
          formatted,
          telegramFormat,
          isValid,
          countryInfo
        };
      } catch (error) {
        return {
          original: phoneNumber,
          error: error.message,
          isValid: false
        };
      }
    });

    res.json({ results });
  } catch (error) {
    console.error('Bulk phone format error:', error);
    res.status(500).json({ error: 'Failed to format phone numbers' });
  }
});

// Get country codes
router.get('/country-codes', authenticateToken, async (req, res) => {
  try {
    const countryCodes = phoneFormatter.getAllCountryCodes();
    res.json({ countryCodes });
  } catch (error) {
    console.error('Get country codes error:', error);
    res.status(500).json({ error: 'Failed to get country codes' });
  }
});

// Generate phone variations
router.post('/generate-phone-variations', authenticateToken, async (req, res) => {
  try {
    const { baseNumber, count = 10 } = req.body;

    if (!baseNumber) {
      return res.status(400).json({ error: 'Base phone number is required' });
    }

    const variations = phoneFormatter.generatePhoneVariations(
      baseNumber, 
      Math.min(count, 50)
    );

    res.json({ variations });
  } catch (error) {
    console.error('Generate phone variations error:', error);
    res.status(500).json({ error: 'Failed to generate phone variations' });
  }
});

// Message templates
router.get('/message-templates', authenticateToken, async (req, res) => {
  try {
    const templates = await MessageTemplate.findAll({
      where: { userId: req.user.id },
      order: [['lastUsed', 'DESC'], ['createdAt', 'DESC']]
    });

    res.json({ templates });
  } catch (error) {
    console.error('Get message templates error:', error);
    res.status(500).json({ error: 'Failed to fetch message templates' });
  }
});

// Create message template
router.post('/message-templates', authenticateToken, async (req, res) => {
  try {
    const { name, content, category, mediaType, mediaPath, variables } = req.body;

    if (!name || !content) {
      return res.status(400).json({ error: 'Name and content are required' });
    }

    const template = await MessageTemplate.create({
      userId: req.user.id,
      name,
      content,
      category: category || 'custom',
      mediaType: mediaType || 'text',
      mediaPath,
      variables: variables || []
    });

    res.status(201).json({
      message: 'Message template created successfully',
      template
    });
  } catch (error) {
    console.error('Create message template error:', error);
    res.status(500).json({ error: 'Failed to create message template' });
  }
});

// Update message template
router.put('/message-templates/:id', authenticateToken, async (req, res) => {
  try {
    const templateId = req.params.id;
    const { name, content, category, mediaType, mediaPath, variables } = req.body;

    const template = await MessageTemplate.findOne({
      where: { id: templateId, userId: req.user.id }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    await template.update({
      name,
      content,
      category,
      mediaType,
      mediaPath,
      variables
    });

    res.json({
      message: 'Message template updated successfully',
      template
    });
  } catch (error) {
    console.error('Update message template error:', error);
    res.status(500).json({ error: 'Failed to update message template' });
  }
});

// Delete message template
router.delete('/message-templates/:id', authenticateToken, async (req, res) => {
  try {
    const templateId = req.params.id;

    const template = await MessageTemplate.findOne({
      where: { id: templateId, userId: req.user.id }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    await template.destroy();

    res.json({ message: 'Message template deleted successfully' });
  } catch (error) {
    console.error('Delete message template error:', error);
    res.status(500).json({ error: 'Failed to delete message template' });
  }
});

// Proxy configurations
router.get('/proxies', authenticateToken, async (req, res) => {
  try {
    const proxies = await ProxyConfig.findAll({
      where: { userId: req.user.id },
      order: [['lastTested', 'DESC'], ['createdAt', 'DESC']]
    });

    res.json({ proxies });
  } catch (error) {
    console.error('Get proxies error:', error);
    res.status(500).json({ error: 'Failed to fetch proxies' });
  }
});

// Create proxy configuration
router.post('/proxies', authenticateToken, async (req, res) => {
  try {
    const { name, host, port, username, password, proxyType, country } = req.body;

    if (!name || !host || !port) {
      return res.status(400).json({ error: 'Name, host, and port are required' });
    }

    const proxy = await ProxyConfig.create({
      userId: req.user.id,
      name,
      host,
      port,
      username,
      password,
      proxyType: proxyType || 'http',
      country
    });

    res.status(201).json({
      message: 'Proxy configuration created successfully',
      proxy
    });
  } catch (error) {
    console.error('Create proxy error:', error);
    res.status(500).json({ error: 'Failed to create proxy configuration' });
  }
});

// Test proxy
router.post('/proxies/:id/test', authenticateToken, async (req, res) => {
  try {
    const proxyId = req.params.id;

    const proxy = await ProxyConfig.findOne({
      where: { id: proxyId, userId: req.user.id }
    });

    if (!proxy) {
      return res.status(404).json({ error: 'Proxy not found' });
    }

    const result = await proxy.testConnection();

    res.json(result);
  } catch (error) {
    console.error('Test proxy error:', error);
    res.status(500).json({ error: 'Failed to test proxy' });
  }
});

// Delete proxy
router.delete('/proxies/:id', authenticateToken, async (req, res) => {
  try {
    const proxyId = req.params.id;

    const proxy = await ProxyConfig.findOne({
      where: { id: proxyId, userId: req.user.id }
    });

    if (!proxy) {
      return res.status(404).json({ error: 'Proxy not found' });
    }

    await proxy.destroy();

    res.json({ message: 'Proxy configuration deleted successfully' });
  } catch (error) {
    console.error('Delete proxy error:', error);
    res.status(500).json({ error: 'Failed to delete proxy configuration' });
  }
});

// Group link generator
router.post('/generate-group-link', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { telegramAccountId, groupUsername, expireDate, usageLimit } = req.body;

    if (!telegramAccountId || !groupUsername) {
      return res.status(400).json({ error: 'Account ID and group username are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    // This would require Telegram API implementation
    // For now, return a placeholder response
    const inviteLink = `https://t.me/joinchat/placeholder_${Date.now()}`;

    res.json({
      inviteLink,
      groupUsername,
      expireDate,
      usageLimit,
      createdAt: new Date()
    });
  } catch (error) {
    console.error('Generate group link error:', error);
    res.status(500).json({ error: 'Failed to generate group link' });
  }
});

module.exports = router;
