import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';
import toast from 'react-hot-toast';

const Security = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('proxies');
  const [proxies, setProxies] = useState([]);
  const [fingerprints, setFingerprints] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddProxy, setShowAddProxy] = useState(false);

  const [newProxy, setNewProxy] = useState({
    name: '',
    type: 'http',
    host: '',
    port: '',
    username: '',
    password: '',
    country: 'US',
    provider: ''
  });

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      const [proxiesRes, fingerprintsRes, recommendationsRes] = await Promise.all([
        api.get('/security/proxies'),
        api.get('/security/device-fingerprints'),
        api.get('/security/recommendations')
      ]);

      setProxies(proxiesRes.data.proxies || []);
      setFingerprints(fingerprintsRes.data.fingerprints || []);
      setRecommendations(recommendationsRes.data.recommendations || []);
    } catch (error) {
      console.error('Error loading security data:', error);
      toast.error('Failed to load security data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddProxy = async (e) => {
    e.preventDefault();
    try {
      await api.post('/security/proxies', newProxy);
      toast.success('Proxy added successfully');
      setShowAddProxy(false);
      setNewProxy({
        name: '',
        type: 'http',
        host: '',
        port: '',
        username: '',
        password: '',
        country: 'US',
        provider: ''
      });
      loadSecurityData();
    } catch (error) {
      toast.error(error.response?.data?.error || 'Failed to add proxy');
    }
  };

  const handleTestProxy = async (proxyId) => {
    try {
      const response = await api.post(`/security/proxies/${proxyId}/test`);
      if (response.data.success) {
        toast.success(`Proxy test successful! Response time: ${response.data.responseTime}ms`);
      } else {
        toast.error('Proxy test failed');
      }
      loadSecurityData();
    } catch (error) {
      toast.error('Failed to test proxy');
    }
  };

  const handleDeleteProxy = async (proxyId) => {
    if (!confirm('Are you sure you want to delete this proxy?')) return;
    
    try {
      await api.delete(`/security/proxies/${proxyId}`);
      toast.success('Proxy deleted successfully');
      loadSecurityData();
    } catch (error) {
      toast.error(error.response?.data?.error || 'Failed to delete proxy');
    }
  };

  const handleRegenerateFingerprint = async (accountId) => {
    try {
      await api.post(`/security/device-fingerprints/${accountId}/regenerate`);
      toast.success('Device fingerprint regenerated');
      loadSecurityData();
    } catch (error) {
      toast.error('Failed to regenerate fingerprint');
    }
  };

  const getStatusColor = (isActive, lastError) => {
    if (!isActive || lastError) return 'text-red-600 bg-red-100';
    return 'text-green-600 bg-green-100';
  };

  const getRecommendationColor = (priority) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'low': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Security & Privacy Management
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Manage your proxies, device fingerprints, and security settings to protect your Telegram accounts.
          </p>
        </div>
      </div>

      {/* Security Recommendations */}
      {recommendations.length > 0 && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Security Recommendations</h4>
            <div className="space-y-3">
              {recommendations.map((rec, index) => (
                <div key={index} className={`p-4 rounded-lg border ${getRecommendationColor(rec.priority)}`}>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      {rec.type === 'warning' ? (
                        <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <div className="ml-3">
                      <h5 className="text-sm font-medium text-gray-900">{rec.title}</h5>
                      <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                      <p className="text-xs text-gray-500 mt-1">{rec.action}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('proxies')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'proxies'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Proxies ({proxies.length})
            </button>
            <button
              onClick={() => setActiveTab('fingerprints')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'fingerprints'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Device Fingerprints ({fingerprints.length})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'proxies' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="text-lg font-medium text-gray-900">Proxy Configuration</h4>
                <button
                  onClick={() => setShowAddProxy(true)}
                  className="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700"
                >
                  Add Proxy
                </button>
              </div>

              {proxies.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No proxies configured</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by adding your first proxy.</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {proxies.map((proxy) => (
                    <div key={proxy.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <h5 className="text-sm font-medium text-gray-900">{proxy.name}</h5>
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(proxy.isActive, proxy.lastError)}`}>
                              {proxy.isActive && !proxy.lastError ? 'Active' : 'Inactive'}
                            </span>
                            <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">
                              {proxy.country}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {proxy.type.toUpperCase()} • {proxy.host}:{proxy.port}
                            {proxy.responseTime && ` • ${proxy.responseTime}ms`}
                          </p>
                          {proxy.lastError && (
                            <p className="text-sm text-red-600 mt-1">Error: {proxy.lastError}</p>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleTestProxy(proxy.id)}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            Test
                          </button>
                          <button
                            onClick={() => handleDeleteProxy(proxy.id)}
                            className="text-red-600 hover:text-red-800 text-sm font-medium"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'fingerprints' && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-gray-900">Device Fingerprints</h4>
              
              {fingerprints.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No device fingerprints</h3>
                  <p className="mt-1 text-sm text-gray-500">Device fingerprints will be generated automatically when you use your accounts.</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {fingerprints.map((fp) => (
                    <div key={fp.accountId} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h5 className="text-sm font-medium text-gray-900">{fp.accountName}</h5>
                          {fp.fingerprint && (
                            <div className="text-sm text-gray-600 mt-1">
                              <p>{fp.fingerprint.device} • {fp.fingerprint.system}</p>
                              <p className="text-xs text-gray-500">
                                {fp.fingerprint.platform} • {fp.fingerprint.timezone}
                              </p>
                            </div>
                          )}
                        </div>
                        <button
                          onClick={() => handleRegenerateFingerprint(fp.accountId)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          Regenerate
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add Proxy Modal */}
      {showAddProxy && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Proxy</h3>
              <form onSubmit={handleAddProxy} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    required
                    value={newProxy.name}
                    onChange={(e) => setNewProxy({...newProxy, name: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="My Proxy"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Type</label>
                    <select
                      value={newProxy.type}
                      onChange={(e) => setNewProxy({...newProxy, type: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="http">HTTP</option>
                      <option value="https">HTTPS</option>
                      <option value="socks4">SOCKS4</option>
                      <option value="socks5">SOCKS5</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Country</label>
                    <input
                      type="text"
                      value={newProxy.country}
                      onChange={(e) => setNewProxy({...newProxy, country: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      placeholder="US"
                      maxLength={2}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Host</label>
                    <input
                      type="text"
                      required
                      value={newProxy.host}
                      onChange={(e) => setNewProxy({...newProxy, host: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      placeholder="proxy.example.com"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Port</label>
                    <input
                      type="number"
                      required
                      value={newProxy.port}
                      onChange={(e) => setNewProxy({...newProxy, port: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      placeholder="8080"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Username (Optional)</label>
                    <input
                      type="text"
                      value={newProxy.username}
                      onChange={(e) => setNewProxy({...newProxy, username: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Password (Optional)</label>
                    <input
                      type="password"
                      value={newProxy.password}
                      onChange={(e) => setNewProxy({...newProxy, password: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddProxy(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700"
                  >
                    Add Proxy
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Security;
