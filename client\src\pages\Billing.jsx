import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

const Billing = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState(null);
  const [pricing, setPricing] = useState(null);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [selectedDuration, setSelectedDuration] = useState(1);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    try {
      setLoading(true);
      
      const [subscriptionResponse, pricingResponse, historyResponse] = await Promise.all([
        api.get('/payment/subscription'),
        api.get('/payment/pricing'),
        api.get('/payment/history?limit=10')
      ]);

      setSubscription(subscriptionResponse.data);
      setPricing(pricingResponse.data.pricing);
      setPaymentHistory(historyResponse.data.payments);
    } catch (error) {
      console.error('Failed to fetch billing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (plan, duration) => {
    try {
      setProcessing(true);
      
      // Create payment intent
      const response = await api.post('/payment/create-intent', {
        subscriptionPlan: plan,
        duration: duration
      });

      // In a real implementation, you would integrate with Stripe Elements here
      // For now, we'll simulate a successful payment
      alert(`Payment intent created for ${plan} plan (${duration} month${duration > 1 ? 's' : ''}). Amount: $${response.data.amount}`);
      
      // Refresh billing data
      await fetchBillingData();
    } catch (error) {
      console.error('Upgrade error:', error);
      alert('Failed to process upgrade. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features.')) {
      return;
    }

    try {
      setProcessing(true);
      await api.post('/payment/cancel-subscription');
      alert('Subscription cancelled successfully');
      await fetchBillingData();
    } catch (error) {
      console.error('Cancel subscription error:', error);
      alert('Failed to cancel subscription. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatAmount = (amount) => {
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  const getStatusColor = (status) => {
    const colors = {
      succeeded: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      failed: 'bg-red-100 text-red-800',
      canceled: 'bg-gray-100 text-gray-800',
      refunded: 'bg-blue-100 text-blue-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Billing & Subscription</h1>

      {/* Current Subscription */}
      {subscription && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Current Subscription</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <p className="text-sm text-gray-600">Plan</p>
              <p className="text-lg font-semibold text-gray-900 capitalize">
                {subscription.plan}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Status</p>
              <p className={`text-lg font-semibold ${
                subscription.isActive ? 'text-green-600' : 'text-red-600'
              }`}>
                {subscription.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Expires</p>
              <p className="text-lg font-semibold text-gray-900">
                {subscription.expiry ? formatDate(subscription.expiry) : 'Never'}
              </p>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Usage Limits</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Daily Messages</p>
                <p className="text-xl font-bold text-gray-900">{subscription.limits.dailyMessages}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Daily Adds</p>
                <p className="text-xl font-bold text-gray-900">{subscription.limits.dailyAdds}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Max Accounts</p>
                <p className="text-xl font-bold text-gray-900">{subscription.limits.maxAccounts}</p>
              </div>
            </div>
          </div>

          {subscription.plan !== 'free' && subscription.isActive && (
            <div className="mt-6">
              <button
                onClick={handleCancelSubscription}
                disabled={processing}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
              >
                {processing ? 'Processing...' : 'Cancel Subscription'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Pricing Plans */}
      {pricing && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Upgrade Your Plan</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(pricing).map(([planName, planData]) => (
              <div
                key={planName}
                className={`border-2 rounded-lg p-6 ${
                  subscription?.plan === planName 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold text-gray-900 capitalize">{planName}</h3>
                  <div className="mt-2">
                    <span className="text-3xl font-bold text-gray-900">
                      ${planData.monthly}
                    </span>
                    <span className="text-gray-600">/month</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    or ${planData.yearly}/year (save ${(planData.monthly * 12 - planData.yearly).toFixed(2)})
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    <span className="text-sm">{planData.features.maxAccounts} Telegram accounts</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    <span className="text-sm">{planData.features.dailyMessages} daily messages</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    <span className="text-sm">{planData.features.dailyAdds} daily member adds</span>
                  </div>
                  {planData.features.automation && (
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✓</span>
                      <span className="text-sm">Automation features</span>
                    </div>
                  )}
                  {planData.features.analytics && (
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✓</span>
                      <span className="text-sm">Advanced analytics</span>
                    </div>
                  )}
                  {planData.features.proxySupport && (
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✓</span>
                      <span className="text-sm">Proxy support</span>
                    </div>
                  )}
                </div>

                {subscription?.plan !== planName && (
                  <div className="space-y-2">
                    <button
                      onClick={() => handleUpgrade(planName, 1)}
                      disabled={processing}
                      className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
                    >
                      {processing ? 'Processing...' : `Upgrade Monthly - $${planData.monthly}`}
                    </button>
                    <button
                      onClick={() => handleUpgrade(planName, 12)}
                      disabled={processing}
                      className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
                    >
                      {processing ? 'Processing...' : `Upgrade Yearly - $${planData.yearly}`}
                    </button>
                  </div>
                )}

                {subscription?.plan === planName && (
                  <div className="text-center">
                    <span className="inline-flex px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full">
                      Current Plan
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Payment History */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Payment History</h2>
        
        {paymentHistory.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paymentHistory.map((payment) => (
                  <tr key={payment.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(payment.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                      {payment.subscriptionPlan}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatAmount(payment.amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                        {payment.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.subscriptionDuration} month{payment.subscriptionDuration > 1 ? 's' : ''}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No payment history found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Billing;
