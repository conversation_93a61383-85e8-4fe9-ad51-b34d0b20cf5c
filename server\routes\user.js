const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { User } = require('../models');
const { TelegramApi } = require('telegram');
const { StringSession } = require('telegram/sessions');

const router = express.Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    res.json({ user: req.user.toJSON() });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Failed to get profile' });
  }
});

// Update user profile
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { firstName, lastName, username } = req.body;
    
    const updateData = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (username !== undefined) updateData.username = username;

    // Check if username is already taken
    if (username && username !== req.user.username) {
      const existingUser = await User.findOne({ where: { username } });
      if (existingUser) {
        return res.status(400).json({ error: 'Username already taken' });
      }
    }

    await req.user.update(updateData);
    
    res.json({ 
      message: 'Profile updated successfully',
      user: req.user.toJSON()
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Change password
router.put('/password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current and new passwords are required' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }

    // Verify current password
    const isValidPassword = await req.user.comparePassword(currentPassword);
    if (!isValidPassword) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }

    // Update password
    await req.user.update({ password: newPassword });

    res.json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ error: 'Failed to change password' });
  }
});

// Save API credentials
router.post('/api-credentials', authenticateToken, async (req, res) => {
  try {
    const { telegramApiId, telegramApiHash } = req.body;

    if (!telegramApiId || !telegramApiHash) {
      return res.status(400).json({ error: 'Both API ID and API Hash are required' });
    }

    // Validate API ID format
    if (!/^\d+$/.test(telegramApiId)) {
      return res.status(400).json({ error: 'API ID must be a number' });
    }

    // Validate API Hash format
    if (telegramApiHash.length !== 32) {
      return res.status(400).json({ error: 'API Hash must be exactly 32 characters long' });
    }

    // Update user with API credentials
    await req.user.update({
      telegramApiId,
      telegramApiHash,
      apiCredentialsVerified: false,
      apiCredentialsVerifiedAt: null
    });

    res.json({
      message: 'API credentials saved successfully',
      user: req.user.toJSON()
    });
  } catch (error) {
    console.error('Save API credentials error:', error);
    res.status(500).json({ error: 'Failed to save API credentials' });
  }
});

// Test API credentials
router.post('/test-api-credentials', authenticateToken, async (req, res) => {
  try {
    const { telegramApiId, telegramApiHash } = req.body;

    if (!telegramApiId || !telegramApiHash) {
      return res.status(400).json({ error: 'Both API ID and API Hash are required' });
    }

    const apiId = parseInt(telegramApiId);
    const apiHash = telegramApiHash;

    // Test the credentials by creating a client and connecting
    const client = new TelegramApi(new StringSession(''), apiId, apiHash, {
      connectionRetries: 3,
      retryDelay: 1000,
      timeout: 10000
    });

    try {
      await client.connect();

      // Try to get the current configuration to test if credentials work
      const { Api } = require('telegram');
      const config = await client.invoke(new Api.help.GetConfig());

      await client.disconnect();

      // Update user to mark credentials as verified
      await req.user.update({
        telegramApiId,
        telegramApiHash,
        apiCredentialsVerified: true,
        apiCredentialsVerifiedAt: new Date()
      });

      res.json({
        message: 'API credentials are valid and working!',
        verified: true
      });

    } catch (telegramError) {
      try {
        await client.disconnect();
      } catch (disconnectError) {
        // Ignore disconnect errors
      }

      console.error('Telegram API test error:', telegramError);

      if (telegramError.message.includes('API_ID_INVALID')) {
        res.status(400).json({ error: 'Invalid API ID. Please check your credentials.' });
      } else if (telegramError.message.includes('API_HASH_INVALID')) {
        res.status(400).json({ error: 'Invalid API Hash. Please check your credentials.' });
      } else {
        res.status(400).json({ error: 'Failed to connect with these credentials: ' + telegramError.message });
      }
    }

  } catch (error) {
    console.error('Test API credentials error:', error);
    res.status(500).json({ error: 'Failed to test API credentials' });
  }
});

// Get API credentials status
router.get('/api-credentials/status', authenticateToken, async (req, res) => {
  try {
    res.json({
      hasCredentials: !!(req.user.telegramApiId && req.user.telegramApiHash),
      verified: req.user.apiCredentialsVerified,
      verifiedAt: req.user.apiCredentialsVerifiedAt,
      apiId: req.user.telegramApiId // Only return API ID, not the hash
    });
  } catch (error) {
    console.error('Get API credentials status error:', error);
    res.status(500).json({ error: 'Failed to get API credentials status' });
  }
});

// Delete API credentials
router.delete('/api-credentials', authenticateToken, async (req, res) => {
  try {
    await req.user.update({
      telegramApiId: null,
      telegramApiHash: null,
      apiCredentialsVerified: false,
      apiCredentialsVerifiedAt: null
    });

    res.json({
      message: 'API credentials deleted successfully',
      user: req.user.toJSON()
    });
  } catch (error) {
    console.error('Delete API credentials error:', error);
    res.status(500).json({ error: 'Failed to delete API credentials' });
  }
});

module.exports = router;
