const { Analytics, TelegramAccount, User, Payment } = require('../models');
const { Op } = require('sequelize');

class AnalyticsService {
  // Log various events
  static async logEvent(eventData) {
    try {
      return await Analytics.logEvent(eventData);
    } catch (error) {
      console.error('Failed to log analytics event:', error);
    }
  }

  // Get comprehensive dashboard statistics
  static async getDashboardStats(userId, timeRange = '30d') {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case '24h':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    const [
      eventStats,
      dailyStats,
      accountPerformance,
      recentActivity
    ] = await Promise.all([
      this.getEventStats(userId, startDate, endDate),
      this.getDailyStats(userId, startDate, endDate),
      this.getAccountPerformance(userId),
      this.getRecentActivity(userId, 50)
    ]);

    return {
      timeRange,
      period: { startDate, endDate },
      eventStats,
      dailyStats,
      accountPerformance,
      recentActivity
    };
  }

  // Get event statistics
  static async getEventStats(userId, startDate, endDate) {
    const stats = await Analytics.findAll({
      where: {
        userId,
        timestamp: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        'eventType',
        [Analytics.sequelize.fn('COUNT', Analytics.sequelize.col('id')), 'total'],
        [Analytics.sequelize.fn('SUM', Analytics.sequelize.literal('CASE WHEN success = 1 THEN 1 ELSE 0 END')), 'successful'],
        [Analytics.sequelize.fn('SUM', Analytics.sequelize.literal('CASE WHEN success = 0 THEN 1 ELSE 0 END')), 'failed']
      ],
      group: ['eventType'],
      raw: true
    });

    return stats.map(stat => ({
      ...stat,
      successRate: stat.total > 0 ? (stat.successful / stat.total * 100).toFixed(2) : 0
    }));
  }

  // Get daily statistics for charts
  static async getDailyStats(userId, startDate, endDate) {
    const stats = await Analytics.findAll({
      where: {
        userId,
        timestamp: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        [Analytics.sequelize.fn('DATE', Analytics.sequelize.col('timestamp')), 'date'],
        'eventType',
        [Analytics.sequelize.fn('COUNT', Analytics.sequelize.col('id')), 'count'],
        [Analytics.sequelize.fn('SUM', Analytics.sequelize.literal('CASE WHEN success = 1 THEN 1 ELSE 0 END')), 'successful']
      ],
      group: [
        Analytics.sequelize.fn('DATE', Analytics.sequelize.col('timestamp')),
        'eventType'
      ],
      order: [[Analytics.sequelize.fn('DATE', Analytics.sequelize.col('timestamp')), 'ASC']],
      raw: true
    });

    // Group by date for easier frontend consumption
    const groupedStats = {};
    stats.forEach(stat => {
      if (!groupedStats[stat.date]) {
        groupedStats[stat.date] = {};
      }
      groupedStats[stat.date][stat.eventType] = {
        count: parseInt(stat.count),
        successful: parseInt(stat.successful),
        successRate: stat.count > 0 ? (stat.successful / stat.count * 100).toFixed(2) : 0
      };
    });

    return groupedStats;
  }

  // Get account performance metrics
  static async getAccountPerformance(userId) {
    const accounts = await TelegramAccount.findAll({
      where: { userId },
      include: [{
        model: Analytics,
        as: 'analytics',
        where: {
          timestamp: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        required: false
      }],
      attributes: [
        'id', 'accountName', 'phoneNumber', 'isActive', 'status',
        'dailyMessagesSent', 'totalMessagesSent',
        'dailyMembersAdded', 'totalMembersAdded',
        'lastActivity'
      ]
    });

    return accounts.map(account => {
      const analytics = account.analytics || [];
      const totalEvents = analytics.length;
      const successfulEvents = analytics.filter(a => a.success).length;
      
      return {
        ...account.toJSON(),
        analytics: {
          totalEvents,
          successfulEvents,
          successRate: totalEvents > 0 ? (successfulEvents / totalEvents * 100).toFixed(2) : 0,
          eventBreakdown: this.groupEventsByType(analytics)
        }
      };
    });
  }

  // Get recent activity
  static async getRecentActivity(userId, limit = 50) {
    return await Analytics.findAll({
      where: { userId },
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['accountName', 'phoneNumber'],
        required: false
      }],
      order: [['timestamp', 'DESC']],
      limit,
      raw: false
    });
  }

  // Helper method to group events by type
  static groupEventsByType(analytics) {
    const grouped = {};
    analytics.forEach(event => {
      if (!grouped[event.eventType]) {
        grouped[event.eventType] = { total: 0, successful: 0 };
      }
      grouped[event.eventType].total++;
      if (event.success) {
        grouped[event.eventType].successful++;
      }
    });

    Object.keys(grouped).forEach(type => {
      const data = grouped[type];
      data.successRate = data.total > 0 ? (data.successful / data.total * 100).toFixed(2) : 0;
    });

    return grouped;
  }

  // Get usage statistics for subscription management
  static async getUsageStats(userId, timeRange = '24h') {
    const endDate = new Date();
    const startDate = new Date();
    
    if (timeRange === '24h') {
      startDate.setHours(startDate.getHours() - 24);
    } else if (timeRange === '7d') {
      startDate.setDate(startDate.getDate() - 7);
    } else if (timeRange === '30d') {
      startDate.setDate(startDate.getDate() - 30);
    }

    const usage = await Analytics.findAll({
      where: {
        userId,
        timestamp: { [Op.between]: [startDate, endDate] },
        eventType: { [Op.in]: ['message_sent', 'member_added'] }
      },
      attributes: [
        'eventType',
        [Analytics.sequelize.fn('COUNT', Analytics.sequelize.col('id')), 'count']
      ],
      group: ['eventType'],
      raw: true
    });

    const messagesSent = usage.find(u => u.eventType === 'message_sent')?.count || 0;
    const membersAdded = usage.find(u => u.eventType === 'member_added')?.count || 0;

    return {
      timeRange,
      messagesSent: parseInt(messagesSent),
      membersAdded: parseInt(membersAdded)
    };
  }

  // Export analytics data
  static async exportData(userId, options = {}) {
    const { startDate, endDate, eventTypes, format = 'json' } = options;
    
    const whereClause = { userId };
    
    if (startDate && endDate) {
      whereClause.timestamp = { [Op.between]: [startDate, endDate] };
    }
    
    if (eventTypes && eventTypes.length > 0) {
      whereClause.eventType = { [Op.in]: eventTypes };
    }

    const data = await Analytics.findAll({
      where: whereClause,
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['accountName', 'phoneNumber'],
        required: false
      }],
      order: [['timestamp', 'DESC']]
    });

    if (format === 'csv') {
      return this.convertToCSV(data);
    }

    return data;
  }

  // Convert data to CSV format
  static convertToCSV(data) {
    if (!data.length) return '';

    const headers = [
      'Timestamp', 'Event Type', 'Success', 'Account Name', 
      'Phone Number', 'Error Message', 'IP Address'
    ];

    const rows = data.map(item => [
      item.timestamp,
      item.eventType,
      item.success ? 'Yes' : 'No',
      item.telegramAccount?.accountName || '',
      item.telegramAccount?.phoneNumber || '',
      item.errorMessage || '',
      item.ipAddress || ''
    ]);

    return [headers, ...rows].map(row => 
      row.map(field => `"${field}"`).join(',')
    ).join('\n');
  }
}

module.exports = AnalyticsService;
