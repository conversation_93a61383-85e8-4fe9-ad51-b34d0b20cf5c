import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';
import {
  PhoneIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    accounts: 0,
    activeAccounts: 0,
    totalMembers: 0,
    messagesSentToday: 0,
    membersAddedToday: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch telegram accounts
      const accountsResponse = await api.getTelegramAccounts();
      const accounts = accountsResponse.data.accounts;
      
      // Calculate stats
      const activeAccounts = accounts.filter(acc => acc.isActive).length;
      const messagesSentToday = accounts.reduce((sum, acc) => sum + (acc.dailyMessagesSent || 0), 0);
      const membersAddedToday = accounts.reduce((sum, acc) => sum + (acc.dailyMembersAdded || 0), 0);
      
      // Fetch members count
      const membersResponse = await api.getScrapedMembers({ limit: 1 });
      const totalMembers = membersResponse.data.pagination?.total || 0;

      setStats({
        accounts: accounts.length,
        activeAccounts,
        totalMembers,
        messagesSentToday,
        membersAddedToday
      });
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      name: 'Telegram Accounts',
      value: `${stats.activeAccounts}/${stats.accounts}`,
      description: 'Active / Total',
      icon: PhoneIcon,
      color: 'bg-blue-500'
    },
    {
      name: 'Scraped Members',
      value: stats.totalMembers.toLocaleString(),
      description: 'Total members in database',
      icon: UserGroupIcon,
      color: 'bg-green-500'
    },
    {
      name: 'Messages Today',
      value: stats.messagesSentToday.toLocaleString(),
      description: 'Messages sent today',
      icon: ChatBubbleLeftRightIcon,
      color: 'bg-purple-500'
    },
    {
      name: 'Members Added Today',
      value: stats.membersAddedToday.toLocaleString(),
      description: 'Members added to groups today',
      icon: ChartBarIcon,
      color: 'bg-orange-500'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.firstName || user?.username}!
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Here's what's happening with your Telegram automation today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {statCards.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-3 rounded-lg ${stat.color}`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                <p className="text-xs text-gray-500">{stat.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Activity */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {stats.accounts === 0 ? (
              <div className="text-center py-6">
                <PhoneIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No accounts yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by adding your first Telegram account.
                </p>
                <div className="mt-6">
                  <a
                    href="/accounts"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                  >
                    Add Account
                  </a>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">Messages sent today</span>
                  <span className="text-sm font-medium text-gray-900">{stats.messagesSentToday}</span>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">Members added today</span>
                  <span className="text-sm font-medium text-gray-900">{stats.membersAddedToday}</span>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">Active accounts</span>
                  <span className="text-sm font-medium text-gray-900">{stats.activeAccounts}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Subscription Info */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription Plan</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Current Plan</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 capitalize">
                {user?.subscriptionPlan || 'Free'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Max Accounts</span>
              <span className="text-sm font-medium text-gray-900">{user?.maxAccounts || 1}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Daily Message Limit</span>
              <span className="text-sm font-medium text-gray-900">{user?.dailyMessageLimit || 100}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Daily Add Limit</span>
              <span className="text-sm font-medium text-gray-900">{user?.dailyAddLimit || 50}</span>
            </div>
            {user?.subscriptionPlan === 'free' && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  Upgrade to unlock more features and higher limits!
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
