const jwt = require('jsonwebtoken');
const { User } = require('../models');

const socketHandler = (io) => {
  // Authentication middleware for socket connections
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findByPk(decoded.userId);

      if (!user || !user.isActive) {
        return next(new Error('Authentication error'));
      }

      socket.userId = user.id;
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User ${socket.userId} connected`);

    // Join user to their personal room
    socket.join(`user_${socket.userId}`);

    // Handle scraping progress updates
    socket.on('start_scraping', (data) => {
      // This would be called from the scraping process
      // to send real-time updates to the client
      console.log(`User ${socket.userId} started scraping:`, data);
    });

    // Handle bulk messaging progress
    socket.on('start_bulk_messaging', (data) => {
      console.log(`User ${socket.userId} started bulk messaging:`, data);
    });

    // Handle member adding progress
    socket.on('start_member_adding', (data) => {
      console.log(`User ${socket.userId} started member adding:`, data);
    });

    socket.on('disconnect', () => {
      console.log(`User ${socket.userId} disconnected`);
    });
  });

  // Helper functions to emit to specific users
  const emitToUser = (userId, event, data) => {
    io.to(`user_${userId}`).emit(event, data);
  };

  const emitScrapingProgress = (userId, progress) => {
    emitToUser(userId, 'scraping_progress', progress);
  };

  const emitMessagingProgress = (userId, progress) => {
    emitToUser(userId, 'messaging_progress', progress);
  };

  const emitMemberAddingProgress = (userId, progress) => {
    emitToUser(userId, 'member_adding_progress', progress);
  };

  // Export helper functions
  return {
    emitToUser,
    emitScrapingProgress,
    emitMessagingProgress,
    emitMemberAddingProgress
  };
};

module.exports = socketHandler;
