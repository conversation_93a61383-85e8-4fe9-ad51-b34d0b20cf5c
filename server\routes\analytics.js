const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const AnalyticsService = require('../services/analyticsService');
const AccountHealthService = require('../services/accountHealthService');

const router = express.Router();

// Get dashboard analytics
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;
    const stats = await AnalyticsService.getDashboardStats(req.user.id, timeRange);
    res.json(stats);
  } catch (error) {
    console.error('Get dashboard analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard analytics' });
  }
});

// Get event statistics
router.get('/events', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate, eventType } = req.query;
    
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();
    
    const stats = await AnalyticsService.getEventStats(req.user.id, start, end);
    res.json({ stats, period: { startDate: start, endDate: end } });
  } catch (error) {
    console.error('Get event statistics error:', error);
    res.status(500).json({ error: 'Failed to fetch event statistics' });
  }
});

// Get daily statistics for charts
router.get('/daily', authenticateToken, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - parseInt(days) * 24 * 60 * 60 * 1000);
    
    const stats = await AnalyticsService.getDailyStats(req.user.id, startDate, endDate);
    res.json({ stats, period: { startDate, endDate } });
  } catch (error) {
    console.error('Get daily statistics error:', error);
    res.status(500).json({ error: 'Failed to fetch daily statistics' });
  }
});

// Get account performance metrics
router.get('/accounts', authenticateToken, async (req, res) => {
  try {
    const performance = await AnalyticsService.getAccountPerformance(req.user.id);
    res.json({ accounts: performance });
  } catch (error) {
    console.error('Get account performance error:', error);
    res.status(500).json({ error: 'Failed to fetch account performance' });
  }
});

// Get recent activity
router.get('/activity', authenticateToken, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    const activity = await AnalyticsService.getRecentActivity(req.user.id, parseInt(limit));
    res.json({ activity });
  } catch (error) {
    console.error('Get recent activity error:', error);
    res.status(500).json({ error: 'Failed to fetch recent activity' });
  }
});

// Get usage statistics
router.get('/usage', authenticateToken, async (req, res) => {
  try {
    const { timeRange = '24h' } = req.query;
    const usage = await AnalyticsService.getUsageStats(req.user.id, timeRange);
    res.json(usage);
  } catch (error) {
    console.error('Get usage statistics error:', error);
    res.status(500).json({ error: 'Failed to fetch usage statistics' });
  }
});

// Export analytics data
router.get('/export', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate, eventTypes, format = 'json' } = req.query;
    
    const options = {};
    if (startDate) options.startDate = new Date(startDate);
    if (endDate) options.endDate = new Date(endDate);
    if (eventTypes) options.eventTypes = eventTypes.split(',');
    options.format = format;

    const data = await AnalyticsService.exportData(req.user.id, options);
    
    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=analytics.csv');
      res.send(data);
    } else {
      res.json({ data });
    }
  } catch (error) {
    console.error('Export analytics error:', error);
    res.status(500).json({ error: 'Failed to export analytics data' });
  }
});

// Account health endpoints
router.get('/health/summary', authenticateToken, async (req, res) => {
  try {
    const summary = await AccountHealthService.getHealthSummary(req.user.id);
    res.json(summary);
  } catch (error) {
    console.error('Get health summary error:', error);
    res.status(500).json({ error: 'Failed to fetch health summary' });
  }
});

// Get health details for specific account
router.get('/health/:accountId', authenticateToken, async (req, res) => {
  try {
    const { accountId } = req.params;
    
    // Verify account ownership
    const { TelegramAccount } = require('../models');
    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });
    
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const health = await AccountHealthService.performHealthCheck(accountId);
    const recommendations = await AccountHealthService.getHealthRecommendations(accountId);
    
    res.json({ health, recommendations });
  } catch (error) {
    console.error('Get account health error:', error);
    res.status(500).json({ error: 'Failed to fetch account health' });
  }
});

// Start account warmup
router.post('/health/:accountId/warmup', authenticateToken, async (req, res) => {
  try {
    const { accountId } = req.params;
    
    // Verify account ownership
    const { TelegramAccount } = require('../models');
    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });
    
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const warmupPlan = await AccountHealthService.startWarmup(accountId);
    res.json({ message: 'Warmup started successfully', warmupPlan });
  } catch (error) {
    console.error('Start warmup error:', error);
    res.status(500).json({ error: 'Failed to start account warmup' });
  }
});

// Manual health check
router.post('/health/:accountId/check', authenticateToken, async (req, res) => {
  try {
    const { accountId } = req.params;
    
    // Verify account ownership
    const { TelegramAccount } = require('../models');
    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });
    
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const health = await AccountHealthService.performHealthCheck(accountId);
    res.json({ health, message: 'Health check completed' });
  } catch (error) {
    console.error('Manual health check error:', error);
    res.status(500).json({ error: 'Failed to perform health check' });
  }
});

// Get analytics overview for admin dashboard
router.get('/overview', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { Analytics, User, TelegramAccount } = require('../models');
    
    // Get system-wide statistics
    const [
      totalUsers,
      totalAccounts,
      totalEvents,
      recentActivity
    ] = await Promise.all([
      User.count(),
      TelegramAccount.count(),
      Analytics.count(),
      Analytics.findAll({
        limit: 100,
        order: [['timestamp', 'DESC']],
        include: [
          { model: User, as: 'user', attributes: ['username', 'email'] },
          { model: TelegramAccount, as: 'telegramAccount', attributes: ['accountName', 'phoneNumber'] }
        ]
      })
    ]);

    res.json({
      systemStats: {
        totalUsers,
        totalAccounts,
        totalEvents
      },
      recentActivity
    });
  } catch (error) {
    console.error('Get analytics overview error:', error);
    res.status(500).json({ error: 'Failed to fetch analytics overview' });
  }
});

module.exports = router;
