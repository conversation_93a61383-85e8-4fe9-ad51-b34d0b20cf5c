const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:3002/api';
const TDATA_PATH = './+************/tdata';
const JSON_PATH = './+************/+************.json';

// Auto-generated user for testing
const USER_CREDENTIALS = {
  email: 'tdata_user_' + Date.now() + '@example.com',
  password: 'password123',
  username: 'tdata_user_' + Date.now(),
  firstName: 'TData',
  lastName: 'User'
};

async function registerAndLogin() {
  try {
    console.log('🔐 Creating user account...');
    
    // Try to register
    const registerResponse = await axios.post(`${API_BASE_URL}/auth/register`, USER_CREDENTIALS);
    
    if (registerResponse.data.token) {
      console.log('✅ User registered and logged in');
      return registerResponse.data.token;
    }
    
    throw new Error('Registration failed');
    
  } catch (error) {
    console.error('❌ Registration failed:', error.response?.data?.message || error.message);
    throw error;
  }
}

async function setApiCredentials(token) {
  try {
    console.log('🔑 Setting API credentials...');
    
    const response = await axios.post(`${API_BASE_URL}/users/api-credentials`, {
      telegramApiId: '2040',
      telegramApiHash: 'b18441a1ff607e10a989891a5462e627'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ API credentials set');
    return response.data;
    
  } catch (error) {
    console.error('❌ Failed to set API credentials:', error.response?.data?.error || error.message);
    throw error;
  }
}

async function importTDataAsJSON(token) {
  try {
    console.log('📁 Converting TData to JSON format...');
    
    // Read the existing JSON file (easier than parsing TData)
    if (!fs.existsSync(JSON_PATH)) {
      throw new Error('JSON session file not found');
    }
    
    const jsonData = JSON.parse(fs.readFileSync(JSON_PATH, 'utf8'));
    console.log(`📱 Found session for: ${jsonData.first_name} (${jsonData.phone})`);
    
    // Create a proper session data structure
    const sessionData = {
      // Use the session file name as session string (common pattern)
      session_string: jsonData.session_file || jsonData.phone,
      api_id: jsonData.app_id,
      api_hash: jsonData.app_hash,
      phone: jsonData.phone,
      first_name: jsonData.first_name,
      last_name: jsonData.last_name,
      username: jsonData.username,
      device: jsonData.device,
      app_version: jsonData.app_version,
      sdk: jsonData.sdk,
      lang_code: jsonData.lang_code,
      system_lang_code: jsonData.system_lang_code,
      // Add TData path for reference
      tdata_path: TDATA_PATH
    };
    
    console.log('📤 Importing session...');
    
    const response = await axios.post(`${API_BASE_URL}/session-import/json`, {
      sessionData: sessionData,
      accountName: `${jsonData.first_name} (${jsonData.phone}) - TData Import`
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log('✅ TData session imported successfully!');
      console.log(`   Account ID: ${response.data.account.id}`);
      console.log(`   Account Name: ${response.data.account.accountName}`);
      console.log(`   Phone: ${response.data.account.phoneNumber}`);
      console.log(`   Status: ${response.data.account.status}`);
      
      return response.data.account;
    } else {
      throw new Error(response.data.message || 'Import failed');
    }
    
  } catch (error) {
    console.error('❌ TData import failed:', error.response?.data?.error || error.message);
    throw error;
  }
}

async function testAccountConnection(token, accountId) {
  try {
    console.log('🔍 Testing account connection...');
    
    const response = await axios.get(`${API_BASE_URL}/telegram/accounts/${accountId}/status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`📊 Account status: ${response.data.status}`);
    
    if (response.data.status === 'online') {
      console.log('✅ Account is ONLINE and ready for automation!');
      return true;
    } else {
      console.log('⚠️ Account is offline or has issues');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Connection test failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function demonstrateScraping(token, accountId) {
  try {
    console.log('🕷️ Testing member scraping...');
    
    // Test with a public group (you can change this)
    const testGroup = '@telegram'; // Telegram's official channel
    
    const response = await axios.post(`${API_BASE_URL}/members/scrape`, {
      accountId: accountId,
      groupUsername: testGroup,
      limit: 10, // Small test
      skipBots: true,
      includeInactive: false
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.scrapedCount > 0) {
      console.log(`✅ Successfully scraped ${response.data.scrapedCount} members from ${testGroup}`);
      console.log('🎉 Your TData session is working for member scraping!');
      return true;
    } else {
      console.log('⚠️ No members scraped, but connection works');
      return true;
    }
    
  } catch (error) {
    console.error('❌ Scraping test failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting TData Import and Testing...\n');
    
    // Check if files exist
    console.log('📋 Checking files:');
    console.log(`   TData folder: ${fs.existsSync(TDATA_PATH) ? '✅' : '❌'} ${TDATA_PATH}`);
    console.log(`   JSON file: ${fs.existsSync(JSON_PATH) ? '✅' : '❌'} ${JSON_PATH}\n`);
    
    if (!fs.existsSync(JSON_PATH)) {
      throw new Error('JSON session file is required for this import method');
    }
    
    // Step 1: Create user account
    const token = await registerAndLogin();
    
    // Step 2: Set API credentials
    await setApiCredentials(token);
    
    // Step 3: Import TData session
    const account = await importTDataAsJSON(token);
    
    // Step 4: Test connection
    const isOnline = await testAccountConnection(token, account.id);
    
    // Step 5: Test scraping (optional)
    if (isOnline) {
      await demonstrateScraping(token, account.id);
    }
    
    console.log('\n🎉 TData Import Complete!');
    console.log('\n📝 What you can do now:');
    console.log('1. Open http://localhost:3000');
    console.log('2. Login with these credentials:');
    console.log(`   Email: ${USER_CREDENTIALS.email}`);
    console.log(`   Password: ${USER_CREDENTIALS.password}`);
    console.log('3. Go to "Telegram Accounts" - your account should be active');
    console.log('4. Go to "Members" - start scraping from groups');
    console.log('5. Use "Add Members" to transfer between groups');
    
    console.log('\n🔄 The Complete Workflow:');
    console.log('   TData → Session → Login → Scrape Group A → Transfer to Group B');
    
  } catch (error) {
    console.error('\n💥 Process failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the server is running (npm run dev)');
    console.log('2. Check that your JSON file exists and is valid');
    console.log('3. Ensure TData folder structure is correct');
    console.log('4. Check server logs for detailed errors');
  }
}

// Run the import
main();
