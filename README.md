# Telegram Management System

A comprehensive web application for managing multiple Telegram accounts, scraping group members, sending bulk messages, and automating member additions to groups.

## Features

### 🔐 User Management
- User registration and authentication
- JWT-based session management
- Subscription plans with usage limits
- Role-based access control

### 📱 Telegram Account Management
- Add multiple Telegram accounts
- Phone verification with 2FA support
- Account health monitoring
- Session management and rotation
- Proxy support for each account

### 👥 Member Scraping & Management
- Scrape members from public/private groups
- Export member data to CSV
- Filter and search members
- Track member activity and status
- Bulk member operations

### 💬 Messaging Features
- Send individual messages
- Bulk messaging with personalization
- Message templates and variables
- Media support (images, videos, documents)
- Configurable delays and rate limiting

### 🚀 Auto Member Adding
- Bulk invite members to groups/channels
- Smart adding with delays and limits
- Success/failure tracking
- Anti-ban protection mechanisms

### 📊 Analytics & Reporting
- Real-time activity monitoring
- Usage statistics and limits
- Account performance metrics
- Export capabilities

## Technology Stack

### Backend
- **Node.js** with Express.js
- **SQLite** database with Sequelize ORM
- **Telegram MTProto API** for advanced features
- **JWT** for authentication
- **Socket.io** for real-time updates
- **Redis** for session management (optional)

### Frontend
- **React 18** with modern hooks
- **Vite** for fast development
- **Tailwind CSS** for styling
- **React Router** for navigation
- **Axios** for API calls
- **React Hot Toast** for notifications

## Prerequisites

Before running this application, you need:

1. **Node.js** (v16 or higher)
2. **Telegram API credentials** from [my.telegram.org/apps](https://my.telegram.org/apps)
3. **Git** for cloning the repository

## Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd telegram-management-system
```

### 2. Install Dependencies
```bash
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### 3. Configure Environment Variables

Copy the example environment file and configure it:
```bash
cd server
cp .env.example .env
```

Edit `server/.env` with your configuration:
```env
# Get these from https://my.telegram.org/apps
TELEGRAM_API_ID=your-api-id
TELEGRAM_API_HASH=your-api-hash

# Change this to a secure random string
JWT_SECRET=your-super-secret-jwt-key

# Other configurations...
```

### 4. Start the Application

From the root directory:
```bash
# Start both frontend and backend
npm run dev
```

Or start them separately:
```bash
# Terminal 1 - Backend
cd server
npm run dev

# Terminal 2 - Frontend
cd client
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## Getting Telegram API Credentials

1. Go to [my.telegram.org/apps](https://my.telegram.org/apps)
2. Log in with your Telegram account
3. Create a new application
4. Copy the `api_id` and `api_hash`
5. Add them to your `.env` file

## Usage

### 1. Register an Account
- Visit http://localhost:3000
- Click "Sign up" and create your account
- Log in with your credentials

### 2. Add Telegram Accounts
- Go to "Telegram Accounts" page
- Click "Add Account"
- Enter your phone number
- Verify with the code sent to your phone
- Enter 2FA password if enabled

### 3. Scrape Members
- Go to "Members" page
- Click "Scrape Members"
- Select a Telegram account
- Enter the group username (e.g., @groupname)
- Set the limit and start scraping

### 4. Send Messages
- Go to "Messages" page
- Choose between single or bulk messaging
- Select your Telegram account
- Compose your message
- For bulk messages, select target members

### 5. Add Members to Groups
- Go to "Members" page
- Select members you want to add
- Click "Add to Group"
- Enter target group username
- Configure delay settings

## Project Structure

```
telegram-management-system/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── contexts/       # React contexts
│   │   ├── services/       # API services
│   │   └── ...
│   └── package.json
├── server/                 # Node.js backend
│   ├── config/            # Database configuration
│   ├── models/            # Sequelize models
│   ├── routes/            # Express routes
│   ├── middleware/        # Custom middleware
│   ├── services/          # Business logic
│   └── package.json
├── package.json           # Root package.json
└── README.md
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user

### Telegram Accounts
- `GET /api/telegram/accounts` - Get user's accounts
- `POST /api/telegram/accounts` - Add new account
- `POST /api/telegram/accounts/:id/verify` - Verify account
- `DELETE /api/telegram/accounts/:id` - Delete account

### Members
- `GET /api/members` - Get scraped members
- `POST /api/members/scrape` - Scrape group members
- `POST /api/members/add` - Add members to group
- `GET /api/members/export` - Export members to CSV

### Messages
- `POST /api/messages/send` - Send single message
- `POST /api/messages/bulk` - Send bulk messages
- `GET /api/messages/history` - Get message history

## Security Features

- JWT-based authentication
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS protection
- Helmet.js security headers
- Password hashing with bcrypt
- SQL injection prevention with Sequelize

## Deployment

### VPS Deployment

1. **Prepare your VPS:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2
```

2. **Clone and setup:**
```bash
git clone <your-repo-url>
cd telegram-management-system
npm run install:all
```

3. **Configure environment:**
```bash
cd server
cp .env.example .env
# Edit .env with production values
```

4. **Build and start:**
```bash
cd ../client
npm run build

cd ../server
pm2 start app.js --name "telegram-management"
pm2 startup
pm2 save
```

5. **Setup Nginx (optional):**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the code comments

## Disclaimer

This tool is for educational and legitimate business purposes only. Users are responsible for complying with Telegram's Terms of Service and applicable laws. The developers are not responsible for any misuse of this software.

## Roadmap

- [ ] Advanced scheduling features
- [ ] More messaging templates
- [ ] Group management tools
- [ ] Advanced analytics dashboard
- [ ] API rate limiting improvements
- [ ] Docker containerization
- [ ] Multi-language support
- [ ] Advanced proxy rotation
- [ ] Webhook integrations
- [ ] Mobile app companion
