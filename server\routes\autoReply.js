const express = require('express');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { AutoReplyRule, TelegramAccount } = require('../models');
const autoReplyService = require('../services/autoReplyService');

const router = express.Router();

// Get all auto-reply rules for user
router.get('/rules', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 50, accountId } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { userId: req.user.id };
    if (accountId) whereClause.telegramAccountId = accountId;

    const { count, rows } = await AutoReplyRule.findAndCountAll({
      where: whereClause,
      include: [{
        model: TelegramAccount,
        as: 'telegramAccount',
        attributes: ['id', 'accountName', 'phoneNumber']
      }],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      rules: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get auto-reply rules error:', error);
    res.status(500).json({ error: 'Failed to fetch auto-reply rules' });
  }
});

// Create new auto-reply rule
router.post('/rules', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const {
      telegramAccountId,
      ruleName,
      triggerType,
      triggerValue,
      replyText,
      caseSensitive,
      replyDelay,
      includeReplyToOriginal,
      triggerOnce,
      targetChats,
      excludedChats
    } = req.body;

    if (!telegramAccountId || !ruleName || !triggerValue || !replyText) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    // Create rule
    const rule = await AutoReplyRule.create({
      userId: req.user.id,
      telegramAccountId,
      ruleName,
      triggerType: triggerType || 'contains',
      triggerValue,
      replyText,
      caseSensitive: caseSensitive || false,
      replyDelay: replyDelay || 0,
      includeReplyToOriginal: includeReplyToOriginal !== false,
      triggerOnce: triggerOnce || false,
      targetChats: targetChats || null,
      excludedChats: excludedChats || null
    });

    // If monitoring is active, update rules
    await autoReplyService.updateRules(telegramAccountId);

    res.status(201).json({
      message: 'Auto-reply rule created successfully',
      rule
    });
  } catch (error) {
    console.error('Create auto-reply rule error:', error);
    res.status(500).json({ error: 'Failed to create auto-reply rule' });
  }
});

// Update auto-reply rule
router.put('/rules/:id', authenticateToken, async (req, res) => {
  try {
    const ruleId = req.params.id;
    const {
      ruleName,
      triggerType,
      triggerValue,
      replyText,
      caseSensitive,
      isActive,
      replyDelay,
      includeReplyToOriginal,
      triggerOnce,
      targetChats,
      excludedChats
    } = req.body;

    const rule = await AutoReplyRule.findOne({
      where: { id: ruleId, userId: req.user.id }
    });

    if (!rule) {
      return res.status(404).json({ error: 'Rule not found' });
    }

    await rule.update({
      ruleName,
      triggerType,
      triggerValue,
      replyText,
      caseSensitive,
      isActive,
      replyDelay,
      includeReplyToOriginal,
      triggerOnce,
      targetChats,
      excludedChats
    });

    // If monitoring is active, update rules
    await autoReplyService.updateRules(rule.telegramAccountId);

    res.json({
      message: 'Auto-reply rule updated successfully',
      rule
    });
  } catch (error) {
    console.error('Update auto-reply rule error:', error);
    res.status(500).json({ error: 'Failed to update auto-reply rule' });
  }
});

// Delete auto-reply rule
router.delete('/rules/:id', authenticateToken, async (req, res) => {
  try {
    const ruleId = req.params.id;

    const rule = await AutoReplyRule.findOne({
      where: { id: ruleId, userId: req.user.id }
    });

    if (!rule) {
      return res.status(404).json({ error: 'Rule not found' });
    }

    const telegramAccountId = rule.telegramAccountId;
    await rule.destroy();

    // If monitoring is active, update rules
    await autoReplyService.updateRules(telegramAccountId);

    res.json({ message: 'Auto-reply rule deleted successfully' });
  } catch (error) {
    console.error('Delete auto-reply rule error:', error);
    res.status(500).json({ error: 'Failed to delete auto-reply rule' });
  }
});

// Start monitoring for auto-replies
router.post('/monitor/start', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { telegramAccountId } = req.body;

    if (!telegramAccountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    const result = await autoReplyService.startMonitoring(telegramAccountId);
    
    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Start monitoring error:', error);
    res.status(500).json({ error: 'Failed to start monitoring' });
  }
});

// Stop monitoring for auto-replies
router.post('/monitor/stop', authenticateToken, async (req, res) => {
  try {
    const { telegramAccountId } = req.body;

    if (!telegramAccountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: telegramAccountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const result = await autoReplyService.stopMonitoring(telegramAccountId);
    
    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Stop monitoring error:', error);
    res.status(500).json({ error: 'Failed to stop monitoring' });
  }
});

// Get monitoring status
router.get('/monitor/status/:accountId', authenticateToken, async (req, res) => {
  try {
    const accountId = req.params.accountId;

    // Verify account ownership
    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }

    const status = autoReplyService.getMonitoringStatus(accountId);
    res.json(status);
  } catch (error) {
    console.error('Get monitoring status error:', error);
    res.status(500).json({ error: 'Failed to get monitoring status' });
  }
});

module.exports = router; 