const express = require('express');
const { authenticateToken, checkSubscription } = require('../middleware/auth');
const { TelegramAccount, ScrapedMember } = require('../models');
const { TelegramApi, Api } = require('telegram');
const { StringSession } = require('telegram/sessions');

const router = express.Router();

// Send single message
router.post('/send', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { accountId, targetType, targetId, message, mediaPath } = req.body;

    if (!accountId || !targetType || !targetId || !message) {
      return res.status(400).json({ error: 'Account ID, target type, target ID, and message are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    // Check daily limits
    await account.resetDailyCounters();
    if (account.dailyMessagesSent >= req.limits.dailyMessages) {
      return res.status(403).json({ 
        error: `Daily message limit exceeded. Limit: ${req.limits.dailyMessages}` 
      });
    }

    // Initialize Telegram client
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    
    const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);

    try {
      await client.connect();

      let target;
      if (targetType === 'user') {
        target = await client.getEntity(targetId);
      } else if (targetType === 'group' || targetType === 'channel') {
        target = await client.getEntity(targetId);
      } else {
        return res.status(400).json({ error: 'Invalid target type' });
      }

      // Send message
      const result = await client.sendMessage(target, {
        message: message,
        file: mediaPath ? mediaPath : undefined
      });

      await account.incrementMessageCount();
      await client.disconnect();

      res.json({
        message: 'Message sent successfully',
        messageId: result.id
      });

    } catch (telegramError) {
      await client.disconnect();
      console.error('Telegram send error:', telegramError);
      
      if (telegramError.message.includes('USER_PRIVACY_RESTRICTED')) {
        res.status(400).json({ error: 'User privacy settings prevent messaging' });
      } else if (telegramError.message.includes('CHAT_WRITE_FORBIDDEN')) {
        res.status(400).json({ error: 'Cannot write to this chat' });
      } else {
        res.status(500).json({ error: 'Failed to send message' });
      }
    }

  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});

// Send bulk messages
router.post('/bulk', authenticateToken, checkSubscription, async (req, res) => {
  try {
    const { accountId, targets, message, delay = 30, mediaPath } = req.body;

    if (!accountId || !targets || !Array.isArray(targets) || !message) {
      return res.status(400).json({ error: 'Account ID, targets array, and message are required' });
    }

    const account = await TelegramAccount.findOne({
      where: { id: accountId, userId: req.user.id, isActive: true }
    });

    if (!account) {
      return res.status(404).json({ error: 'Account not found or inactive' });
    }

    // Check daily limits
    await account.resetDailyCounters();
    if (account.dailyMessagesSent + targets.length > req.limits.dailyMessages) {
      return res.status(403).json({ 
        error: `Daily message limit exceeded. You can send ${req.limits.dailyMessages - account.dailyMessagesSent} more messages today.` 
      });
    }

    // Initialize Telegram client
    const apiId = parseInt(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;
    
    const client = new TelegramApi(new StringSession(account.sessionString), apiId, apiHash);

    try {
      await client.connect();

      let sentCount = 0;
      const errors = [];

      for (const target of targets) {
        try {
          let entity;
          
          if (target.type === 'member') {
            // Get member from database
            const member = await ScrapedMember.findOne({
              where: { id: target.id, userId: req.user.id }
            });
            
            if (!member) {
              errors.push(`Member ${target.id} not found`);
              continue;
            }
            
            entity = await client.getEntity(parseInt(member.telegramId));
          } else {
            entity = await client.getEntity(target.username || target.id);
          }

          // Personalize message if needed
          let personalizedMessage = message;
          if (target.firstName) {
            personalizedMessage = message.replace('{firstName}', target.firstName);
          }
          if (target.lastName) {
            personalizedMessage = personalizedMessage.replace('{lastName}', target.lastName);
          }
          if (target.username) {
            personalizedMessage = personalizedMessage.replace('{username}', target.username);
          }

          // Send message
          await client.sendMessage(entity, {
            message: personalizedMessage,
            file: mediaPath ? mediaPath : undefined
          });

          await account.incrementMessageCount();
          sentCount++;

          // Update member message count if applicable
          if (target.type === 'member') {
            const member = await ScrapedMember.findByPk(target.id);
            if (member) {
              await member.update({
                messagesSent: (member.messagesSent || 0) + 1,
                lastMessageDate: new Date()
              });
            }
          }

          // Delay between messages
          if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay * 1000));
          }

        } catch (sendError) {
          console.error(`Failed to send to target ${target.id}:`, sendError);
          errors.push(`Failed to send to ${target.id}: ${sendError.message}`);
        }
      }

      await client.disconnect();

      res.json({
        message: `Successfully sent ${sentCount} messages`,
        sentCount,
        totalAttempted: targets.length,
        errors: errors.length > 0 ? errors : undefined
      });

    } catch (telegramError) {
      await client.disconnect();
      console.error('Telegram bulk send error:', telegramError);
      res.status(500).json({ error: 'Failed to send bulk messages' });
    }

  } catch (error) {
    console.error('Bulk message error:', error);
    res.status(500).json({ error: 'Failed to send bulk messages' });
  }
});

// Get message history/statistics
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 50, accountId } = req.query;
    
    // This would require a MessageHistory model to track sent messages
    // For now, return account statistics
    const whereClause = { userId: req.user.id };
    if (accountId) {
      whereClause.id = accountId;
    }

    const accounts = await TelegramAccount.findAll({
      where: whereClause,
      attributes: [
        'id', 
        'accountName', 
        'phoneNumber', 
        'dailyMessagesSent', 
        'totalMessagesSent',
        'dailyMembersAdded',
        'totalMembersAdded',
        'lastActivity'
      ]
    });

    res.json({ accounts });
  } catch (error) {
    console.error('Get message history error:', error);
    res.status(500).json({ error: 'Failed to fetch message history' });
  }
});

module.exports = router;
