const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const backupService = require('../services/backupService');
const path = require('path');

const router = express.Router();

// Create backup
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const { includeUserData = false, memberLimit = 50000 } = req.body;
    
    // Check if user is admin for full system backup
    const userId = req.user.role === 'admin' && includeUserData ? null : req.user.id;
    
    const result = await backupService.createFullBackup(userId, {
      includeUserData,
      memberLimit: parseInt(memberLimit)
    });
    
    res.json({
      success: true,
      message: 'Backup created successfully',
      backup: {
        name: result.backupName,
        size: result.size,
        timestamp: result.timestamp
      }
    });
  } catch (error) {
    console.error('Create backup error:', error);
    res.status(500).json({ error: 'Failed to create backup' });
  }
});

// List backups
router.get('/list', authenticateToken, async (req, res) => {
  try {
    // Only admins can see all backups
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    const backups = await backupService.listBackups();
    res.json({ backups });
  } catch (error) {
    console.error('List backups error:', error);
    res.status(500).json({ error: 'Failed to list backups' });
  }
});

// Download backup
router.get('/download/:backupName', authenticateToken, async (req, res) => {
  try {
    const { backupName } = req.params;
    
    // Only admins can download backups
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    // Validate backup name to prevent path traversal
    if (!/^[a-zA-Z0-9_-]+\.zip$/.test(backupName)) {
      return res.status(400).json({ error: 'Invalid backup name' });
    }
    
    const backupPath = path.join(backupService.backupDir, backupName);
    
    // Check if file exists
    const fs = require('fs').promises;
    try {
      await fs.access(backupPath);
    } catch {
      return res.status(404).json({ error: 'Backup not found' });
    }
    
    res.download(backupPath, backupName, (err) => {
      if (err) {
        console.error('Download error:', err);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Failed to download backup' });
        }
      }
    });
  } catch (error) {
    console.error('Download backup error:', error);
    res.status(500).json({ error: 'Failed to download backup' });
  }
});

// Delete backup
router.delete('/:backupName', authenticateToken, async (req, res) => {
  try {
    const { backupName } = req.params;
    
    // Only admins can delete backups
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    // Validate backup name
    if (!/^[a-zA-Z0-9_-]+\.zip$/.test(backupName)) {
      return res.status(400).json({ error: 'Invalid backup name' });
    }
    
    const success = await backupService.deleteBackup(backupName);
    
    if (success) {
      res.json({ success: true, message: 'Backup deleted successfully' });
    } else {
      res.status(404).json({ error: 'Backup not found' });
    }
  } catch (error) {
    console.error('Delete backup error:', error);
    res.status(500).json({ error: 'Failed to delete backup' });
  }
});

// Restore backup
router.post('/restore', authenticateToken, async (req, res) => {
  try {
    // Only admins can restore backups
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    const { backupName, replaceExisting = false, continueOnError = true } = req.body;
    
    if (!backupName) {
      return res.status(400).json({ error: 'Backup name is required' });
    }
    
    // Validate backup name
    if (!/^[a-zA-Z0-9_-]+\.zip$/.test(backupName)) {
      return res.status(400).json({ error: 'Invalid backup name' });
    }
    
    const backupPath = path.join(backupService.backupDir, backupName);
    
    const result = await backupService.restoreFromBackup(backupPath, {
      replaceExisting,
      continueOnError
    });
    
    res.json({
      success: true,
      message: 'Backup restored successfully',
      metadata: result.metadata,
      results: result.results
    });
  } catch (error) {
    console.error('Restore backup error:', error);
    res.status(500).json({ 
      error: 'Failed to restore backup',
      details: error.message 
    });
  }
});

// Upload backup file
router.post('/upload', authenticateToken, async (req, res) => {
  try {
    // Only admins can upload backups
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    const multer = require('multer');
    const upload = multer({
      dest: backupService.backupDir,
      limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
      },
      fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/zip' || file.originalname.endsWith('.zip')) {
          cb(null, true);
        } else {
          cb(new Error('Only ZIP files are allowed'));
        }
      }
    }).single('backup');
    
    upload(req, res, (err) => {
      if (err) {
        return res.status(400).json({ error: err.message });
      }
      
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }
      
      // Rename file to include timestamp
      const fs = require('fs');
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const newName = `uploaded_backup_${timestamp}.zip`;
      const newPath = path.join(backupService.backupDir, newName);
      
      fs.renameSync(req.file.path, newPath);
      
      res.json({
        success: true,
        message: 'Backup uploaded successfully',
        filename: newName
      });
    });
  } catch (error) {
    console.error('Upload backup error:', error);
    res.status(500).json({ error: 'Failed to upload backup' });
  }
});

// Get backup info
router.get('/info/:backupName', authenticateToken, async (req, res) => {
  try {
    const { backupName } = req.params;
    
    // Only admins can view backup info
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    // Validate backup name
    if (!/^[a-zA-Z0-9_-]+\.zip$/.test(backupName)) {
      return res.status(400).json({ error: 'Invalid backup name' });
    }
    
    const backupPath = path.join(backupService.backupDir, backupName);
    const extract = require('extract-zip');
    const fs = require('fs').promises;
    
    // Create temp directory for extraction
    const tempDir = path.join(backupService.backupDir, 'temp_info');
    await fs.mkdir(tempDir, { recursive: true });
    
    try {
      // Extract only metadata
      await extract(backupPath, { 
        dir: tempDir,
        onEntry: (entry) => {
          // Only extract metadata.json
          return entry.fileName === 'metadata.json';
        }
      });
      
      const metadataPath = path.join(tempDir, 'metadata.json');
      const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
      
      // Cleanup temp directory
      await fs.rmdir(tempDir, { recursive: true });
      
      res.json({ metadata });
    } catch (error) {
      // Cleanup temp directory on error
      try {
        await fs.rmdir(tempDir, { recursive: true });
      } catch {}
      throw error;
    }
  } catch (error) {
    console.error('Get backup info error:', error);
    res.status(500).json({ error: 'Failed to get backup info' });
  }
});

// Cleanup old backups
router.post('/cleanup', authenticateToken, async (req, res) => {
  try {
    // Only admins can cleanup backups
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    const { keepDays = 7 } = req.body;
    
    await backupService.cleanupOldBackups(parseInt(keepDays));
    
    res.json({
      success: true,
      message: `Cleaned up backups older than ${keepDays} days`
    });
  } catch (error) {
    console.error('Cleanup backups error:', error);
    res.status(500).json({ error: 'Failed to cleanup backups' });
  }
});

module.exports = router;
