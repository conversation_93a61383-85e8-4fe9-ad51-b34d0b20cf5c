const jwt = require('jsonwebtoken');
const { User } = require('../models');

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId);

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'Invalid or inactive user' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(403).json({ error: 'Invalid token' });
  }
};

const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

const checkSubscription = (req, res, next) => {
  const user = req.user;
  const now = new Date();

  if (user.subscriptionPlan === 'free') {
    // Free plan limitations
    req.limits = {
      maxAccounts: 1,
      dailyMessages: 50,
      dailyAdds: 25
    };
  } else if (user.subscriptionExpiry && user.subscriptionExpiry < now) {
    return res.status(403).json({ error: 'Subscription expired' });
  } else {
    // Set limits based on subscription plan
    const limits = {
      basic: { maxAccounts: 3, dailyMessages: 500, dailyAdds: 250 },
      premium: { maxAccounts: 10, dailyMessages: 2000, dailyAdds: 1000 },
      enterprise: { maxAccounts: 50, dailyMessages: 10000, dailyAdds: 5000 }
    };
    req.limits = limits[user.subscriptionPlan] || limits.basic;
  }

  next();
};

module.exports = {
  authenticateToken,
  requireAdmin,
  checkSubscription
};
