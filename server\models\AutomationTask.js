const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AutomationTask = sequelize.define('AutomationTask', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  telegramAccountId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'TelegramAccounts',
      key: 'id'
    }
  },
  taskType: {
    type: DataTypes.ENUM(
      'auto_join',
      'auto_leave', 
      'auto_react',
      'auto_forward',
      'scheduled_message',
      'member_scraping',
      'member_adding'
    ),
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'running', 'completed', 'failed', 'paused'),
    defaultValue: 'pending'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 5,
    validate: {
      min: 1,
      max: 10
    }
  },
  scheduledAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  executeAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  config: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {}
  },
  result: {
    type: DataTypes.JSON,
    allowNull: true
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  retryCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  maxRetries: {
    type: DataTypes.INTEGER,
    defaultValue: 3
  },
  isRecurring: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  recurringPattern: {
    type: DataTypes.STRING,
    allowNull: true // cron pattern
  },
  nextRun: {
    type: DataTypes.DATE,
    allowNull: true
  }
});

// Instance methods
AutomationTask.prototype.markAsRunning = function() {
  this.status = 'running';
  this.executeAt = new Date();
  return this.save();
};

AutomationTask.prototype.markAsCompleted = function(result = null) {
  this.status = 'completed';
  this.completedAt = new Date();
  if (result) this.result = result;
  return this.save();
};

AutomationTask.prototype.markAsFailed = function(error) {
  this.status = 'failed';
  this.errorMessage = error.message || error;
  this.retryCount += 1;
  return this.save();
};

AutomationTask.prototype.canRetry = function() {
  return this.retryCount < this.maxRetries && this.status === 'failed';
};

AutomationTask.prototype.scheduleNextRun = function() {
  if (this.isRecurring && this.recurringPattern) {
    const cron = require('node-cron');
    // Calculate next run time based on cron pattern
    // This is a simplified version - you might want to use a proper cron library
    const now = new Date();
    this.nextRun = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Default to 24 hours
    this.status = 'pending';
    return this.save();
  }
  return Promise.resolve(this);
};

module.exports = AutomationTask;
